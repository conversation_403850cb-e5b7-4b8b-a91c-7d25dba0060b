=== 类型转换统计摘要 ===

总变量数: 61
总转换次数: 73

变量 '0': 1 次转换
  1. 第107行: int → half (explicit_cast)
     代码: half _9176 = half(0);
     原因: 强制精度转换为 half

变量 '1': 1 次转换
  1. 第109行: int → half (explicit_cast)
     代码: half _9199 = half(1);
     原因: 强制精度转换为 half

变量 '10': 1 次转换
  1. 第262行: int → half (explicit_cast)
     代码: float _10004 = fast::min(1.0 - float(half(10) * half(9.9956989288330078125e-05)), _9997.z);
     原因: 强制精度转换为 half

变量 '2': 1 次转换
  1. 第130行: int → half (explicit_cast)
     代码: half _9251 = half(2);
     原因: 强制精度转换为 half

变量 '_10557': 1 次转换
  1. 第495行: half3 → float3 (explicit_cast)
     代码: float3 _12025 = float3(_10557);
     原因: 强制精度转换为 float3

变量 '_10569': 1 次转换
  1. 第1075行: half3 → float3 (explicit_cast)
     代码: float3 _15762 = float3(_10569);
     原因: 强制精度转换为 float3

变量 '_10704': 1 次转换
  1. 第357行: uint → float (explicit_cast)
     代码: float _10900 = 3.0 - float(_10704);
     原因: 强制精度转换为 float

变量 '_11163.xyz': 1 次转换
  1. 第388行: half3 → float3 (explicit_cast)
     代码: _18297 = clamp((_11163.w * half(32.0)) + half(float(dot(half3(_8373), half3((float3(_11163.xyz) * float3(2.0)) - float3(1.0)))) * 2.0), half(0.0), half(1.0));
     原因: 强制精度转换为 float3

变量 '_11536': 1 次转换
  1. 第467行: half → float (explicit_cast)
     代码: float _11919 = float(_11536);
     原因: 强制精度转换为 float

变量 '_11764': 1 次转换
  1. 第482行: half → float (explicit_cast)
     代码: half _11768 = half(float(_11764) + 0.100000001490116119384765625);
     原因: 强制精度转换为 float

变量 '_11815': 1 次转换
  1. 第489行: half → float (explicit_cast)
     代码: float _11858 = float(_11815) * 0.5;
     原因: 强制精度转换为 float

变量 '_11970': 2 次转换
  1. 第497行: half4 → float4 (implicit_promotion)
     代码: float3 _8521 = ((_9698 + (float3(mix(_9179, half3(half(((_11862 * powr(float(half(fast::max(float(_9199 - (_11827 * _11827)), 0.0078125))), _11858)) * 0.15915493667125701904296875) * float(half4(float4(_11970 / powr(max(half4(half(9.9956989288330078125e-05)), _11973 - (_11975 * (-half(dot(_11517, _11622))))), _11982)) * float4(0.0795769989490509033203125)).x))), half3(half(fast::clamp((_11763 * _11763) * float(dot(_11812, _8303)), 0.0, 1.0)))) * _11812) * float3(_11629))) + (((float3(_11812) * ((_12025 + (_12120 * _11756)) * (fast::min(1000.0, (_12062 * _12062) * 0.3183098733425140380859375) * (1.0 / fast::max((_11736 + sqrt((_11736 * (_11736 - (_11736 * _12080))) + _12080)) * (_11919 + sqrt((_11919 * (_11919 - (_11919 * _12080))) + _12080)), _12108))))) * _11629) * float(_11772))) * _10588;
     原因: / 运算中的精度提升
  2. 第784行: half4 → float4 (implicit_promotion)
     代码: _19965 = (_18431 + (float3(mix(_9179, half3(half(((_11862 * powr(float(half(fast::max(float(_9199 - (_14084 * _14084)), 0.0078125))), _11858)) * 0.15915493667125701904296875) * float(half4(float4(_11970 / powr(max(half4(half(9.9956989288330078125e-05)), _11973 - (_11975 * (-half(dot(_19716, _19751))))), _11982)) * float4(0.0795769989490509033203125)).x))), half3(half(fast::clamp((_14020 * _14020) * float(dot(_14069, _8303)), 0.0, 1.0)))) * _14069) * float3(_19573))) + (((float3(_14069) * _19883) * _19573) * float(_14029));
     原因: / 运算中的精度提升

变量 '_14451': 1 次转换
  1. 第861行: uint → float (explicit_cast)
     代码: float _14797 = float(_14451);
     原因: 强制精度转换为 float

变量 '_14469': 1 次转换
  1. 第869行: uint → float (explicit_cast)
     代码: float _14850 = (_14788 - float((_8397 & 251658240u) >> 24u)) + ((3.0 - float(_14469)) * 3.0);
     原因: 强制精度转换为 float

变量 '_14556': 3 次转换
  1. 第931行: half3 → float (implicit_promotion)
     代码: _18130.x = half(float(dot(_14556, _15343)) * 2.0);
     原因: dot 函数中的精度提升
  2. 第933行: half3 → float (implicit_promotion)
     代码: _18130.y = half(float(dot(_14556, _15352)) * 2.0);
     原因: dot 函数中的精度提升
  3. 第935行: half3 → float (implicit_promotion)
     代码: _18130.z = half(float(dot(_14556, _15361)) * 2.0);
     原因: dot 函数中的精度提升

变量 '_15133.xyz': 1 次转换
  1. 第929行: half3 → float3 (explicit_cast)
     代码: half3 _15343 = half3(((float3(_15133.xyz) * float3(2.0)) - float3(1.0)) * float(_15258)).xyz;
     原因: 强制精度转换为 float3

变量 '_15193.xyz': 1 次转换
  1. 第932行: half3 → float3 (explicit_cast)
     代码: half3 _15352 = half3(((float3(_15193.xyz) * float3(2.0)) - float3(1.0)) * float(_15262)).xyz;
     原因: 强制精度转换为 float3

变量 '_15253.xyz': 1 次转换
  1. 第934行: half3 → float3 (explicit_cast)
     代码: half3 _15361 = half3(((float3(_15253.xyz) * float3(2.0)) - float3(1.0)) * float(_15266)).xyz;
     原因: 强制精度转换为 float3

变量 '_15258': 1 次转换
  1. 第929行: half → float (explicit_cast)
     代码: half3 _15343 = half3(((float3(_15133.xyz) * float3(2.0)) - float3(1.0)) * float(_15258)).xyz;
     原因: 强制精度转换为 float

变量 '_15262': 1 次转换
  1. 第932行: half → float (explicit_cast)
     代码: half3 _15352 = half3(((float3(_15193.xyz) * float3(2.0)) - float3(1.0)) * float(_15262)).xyz;
     原因: 强制精度转换为 float

变量 '_15266': 1 次转换
  1. 第934行: half → float (explicit_cast)
     代码: half3 _15361 = half3(((float3(_15253.xyz) * float3(2.0)) - float3(1.0)) * float(_15266)).xyz;
     原因: 强制精度转换为 float

变量 '_15343': 2 次转换
  1. 第931行: half3 → float (implicit_promotion)
     代码: _18130.x = half(float(dot(_14556, _15343)) * 2.0);
     原因: dot 函数中的精度提升
  2. 第939行: half3 → float3 (explicit_cast)
     代码: _18819 = half3(((float3(_15343) * float3(0.21199999749660491943359375)) + (float3(_15352) * float3(0.714999973773956298828125))) + (float3(_15361) * float3(0.0719999969005584716796875)));
     原因: 强制精度转换为 float3

变量 '_15352': 2 次转换
  1. 第933行: half3 → float (implicit_promotion)
     代码: _18130.y = half(float(dot(_14556, _15352)) * 2.0);
     原因: dot 函数中的精度提升
  2. 第939行: half3 → float3 (explicit_cast)
     代码: _18819 = half3(((float3(_15343) * float3(0.21199999749660491943359375)) + (float3(_15352) * float3(0.714999973773956298828125))) + (float3(_15361) * float3(0.0719999969005584716796875)));
     原因: 强制精度转换为 float3

变量 '_15361': 2 次转换
  1. 第935行: half3 → float (implicit_promotion)
     代码: _18130.z = half(float(dot(_14556, _15361)) * 2.0);
     原因: dot 函数中的精度提升
  2. 第939行: half3 → float3 (explicit_cast)
     代码: _18819 = half3(((float3(_15343) * float3(0.21199999749660491943359375)) + (float3(_15352) * float3(0.714999973773956298828125))) + (float3(_15361) * float3(0.0719999969005584716796875)));
     原因: 强制精度转换为 float3

变量 '_15974': 1 次转换
  1. 第1110行: half → float (explicit_cast)
     代码: half3 _8776 = ((_18255 + (half3((float3(_15813 * (half(fast::min(1000.0, (_15839 * _15839) * 0.3183098733425140380859375)) * half(0.25 / fast::max(_15861 * _15861, _12108)))) * _Block1.cVirtualLitColor.xyz) * _15805) + half3((_Block1.cVirtualLitColor.xyz * abs(_8378)) * _15762))) + half3(fast::min(float3(8192.0), ((((float3((_15813 * (half(fast::min(1000.0, (_16159 * _16159) * 0.3183098733425140380859375)) * half(0.25 / fast::max(_15861 * ((float(_16031) * _15858) + _15854), _12108)))) * _16031) * float3(_Block1.cLocalVirtualLitPos.w)) + float3(_10569 * _16031)) * float(half(fast::min(float(half((_15986 * _15986) / ((float(_15974) * abs(_Block1.cLocalVirtualLitCustom.y)) + 9.9999997473787516355514526367188e-05))), _Block1.cLocalVirtualLitCustom.w)))) * (_Block1.cLocalVirtualLitColor.xyz * abs(_Block1.cLocalVirtualLitColor.w))) * _Block1.DiyLightingInfo.z))) * _8696;
     原因: 强制精度转换为 float

变量 '_16031': 1 次转换
  1. 第1110行: half → float (explicit_cast)
     代码: half3 _8776 = ((_18255 + (half3((float3(_15813 * (half(fast::min(1000.0, (_15839 * _15839) * 0.3183098733425140380859375)) * half(0.25 / fast::max(_15861 * _15861, _12108)))) * _Block1.cVirtualLitColor.xyz) * _15805) + half3((_Block1.cVirtualLitColor.xyz * abs(_8378)) * _15762))) + half3(fast::min(float3(8192.0), ((((float3((_15813 * (half(fast::min(1000.0, (_16159 * _16159) * 0.3183098733425140380859375)) * half(0.25 / fast::max(_15861 * ((float(_16031) * _15858) + _15854), _12108)))) * _16031) * float3(_Block1.cLocalVirtualLitPos.w)) + float3(_10569 * _16031)) * float(half(fast::min(float(half((_15986 * _15986) / ((float(_15974) * abs(_Block1.cLocalVirtualLitCustom.y)) + 9.9999997473787516355514526367188e-05))), _Block1.cLocalVirtualLitCustom.w)))) * (_Block1.cLocalVirtualLitColor.xyz * abs(_Block1.cLocalVirtualLitColor.w))) * _Block1.DiyLightingInfo.z))) * _8696;
     原因: 强制精度转换为 float

变量 '_16258': 1 次转换
  1. 第1126行: half3 → float3 (explicit_cast)
     代码: float3 _16862 = (((((((((((_Block1.cVisibilitySH[0].xyz * _8657) * _15762) * _Block1.AmbientColor.w) * _Block1.ReflectionProbeBBMin.w) * float3(_Block1.cSHCoefficients[0].w)).xyz + float3(_18268)).xyz + (_18989 + float3(((_9179 * _9254) + _8393) * _8696))).xyz + float3((_19028 + half3(float3(_16258) * float3((_15805 * 0.5) + 0.5))) * _10569)).xyz + float3((half3(_9698 * _10588) * _11772) + _8776)).xyz * dot(_16715, _16698)) + (_16805 - (_16805 * _16715))).xyz;
     原因: 强制精度转换为 float3

变量 '_16342': 1 次转换
  1. 第1099行: half → float (explicit_cast)
     代码: float4 _16284 = float4(half4(_16335, _16336, _16335, half(0.858085691928863525390625 * float(_16342))) * _9064.yzxx);
     原因: 强制精度转换为 float

变量 '_16385': 1 次转换
  1. 第1100行: half4 → float4 (explicit_cast)
     代码: float4 _16306 = float4(_16385);
     原因: 强制精度转换为 float4

变量 '_16397': 1 次转换
  1. 第1109行: half3 → float (implicit_promotion)
     代码: half3 _16258 = ((((max(_16279 + (half3(half(dot(_Block1.cSHCoefficients[1], _16284)), half(dot(_Block1.cSHCoefficients[3], _16284)), half(dot(_Block1.cSHCoefficients[5], _16284))) + half3(half(dot(_Block1.cSHCoefficients[2], _16306)), half(dot(_Block1.cSHCoefficients[4], _16306)), half(dot(_Block1.cSHCoefficients[6], _16306)))), _9179) * half3(half(0.699999988079071044921875 + (0.300000011920928955078125 * _Block1.EnvInfo.z)))) * half3(half(float(_8295 * _18329) * mix(1.0, _Block1.WorldProbeInfo.y, step(0.0, _Block1.cVisibilitySH[0].w))))) * half3(half(_Block1.GIInfo.z))) + half3(float3(max(_16279 + (half3(half(dot(_Block1.cSHCoefficients[1], _16451)), half(dot(_Block1.cSHCoefficients[3], _16451)), half(dot(_Block1.cSHCoefficients[5], _16451))) + half3(half(dot(_Block1.cSHCoefficients[2], _16473)), half(dot(_Block1.cSHCoefficients[4], _16473)), half(dot(_Block1.cSHCoefficients[6], _16473)))), _9179)) * float3(((3.1415927410125732421875 * float(clamp(dot(_9064, _16397), half(0.0), half(1.0)))) * _Block1.WorldProbeInfo.w) * float(half(float(_8295 * _18236)) * half(0.5))))) * half3(half(_Block1.cSHCoefficients[0].w));
     原因: dot 函数中的精度提升

变量 '_16509': 1 次转换
  1. 第1107行: half → float (explicit_cast)
     代码: float4 _16451 = float4(half4(_16335, _16336, _16335, half(0.858085691928863525390625 * float(_16509))) * _16397.yzxx);
     原因: 强制精度转换为 float

变量 '_16552': 1 次转换
  1. 第1108行: half4 → float4 (explicit_cast)
     代码: float4 _16473 = float4(_16552);
     原因: 强制精度转换为 float4

变量 '_18225': 1 次转换
  1. 第201行: half3 → float (implicit_promotion)
     代码: half3 _8315 = mix(half3(dot(_18225, _8303)), _18225, half3(half(_Block1.cSaturation)));
     原因: dot 函数中的精度提升

变量 '_18230': 1 次转换
  1. 第219行: half → float (explicit_cast)
     代码: float _9868 = float(_18230);
     原因: 强制精度转换为 float

变量 '_18268': 1 次转换
  1. 第1126行: half3 → float3 (explicit_cast)
     代码: float3 _16862 = (((((((((((_Block1.cVisibilitySH[0].xyz * _8657) * _15762) * _Block1.AmbientColor.w) * _Block1.ReflectionProbeBBMin.w) * float3(_Block1.cSHCoefficients[0].w)).xyz + float3(_18268)).xyz + (_18989 + float3(((_9179 * _9254) + _8393) * _8696))).xyz + float3((_19028 + half3(float3(_16258) * float3((_15805 * 0.5) + 0.5))) * _10569)).xyz + float3((half3(_9698 * _10588) * _11772) + _8776)).xyz * dot(_16715, _16698)) + (_16805 - (_16805 * _16715))).xyz;
     原因: 强制精度转换为 float3

变量 '_18272': 1 次转换
  1. 第465行: half → float (explicit_cast)
     代码: float _11560 = float(half(fast::max(0.119999997317790985107421875, float(_18272))));
     原因: 强制精度转换为 float

变量 '_18297': 1 次转换
  1. 第417行: half → float (explicit_cast)
     代码: _18303 = half(mix(float(_18297), 1.0, fast::clamp(fast::max(_11410.x, fast::max(_11410.y, _11410.z)), 0.0, 1.0)));
     原因: 强制精度转换为 float

变量 '_18304': 1 次转换
  1. 第438行: half → float (explicit_cast)
     代码: _18306 = half(1.0 - fast::clamp((1.0 - float(half(fast::clamp(1.0 - float(half(powr(float(half(fast::clamp(1.0 - float(_18304), 0.0, 1.0))), fast::max(mix(6.0, 0.0, fast::clamp(_Block1.SHGIParam2.w, 0.0, 1.0)), 0.5)))), 0.0, 1.0)))) * float(half(mix(_Block1.SHAOParam.x, _Block1.SHAOParam.y, 1.0 - (_11479 * _11479)))), 0.0, 1.0));
     原因: 强制精度转换为 float

变量 '_18329': 1 次转换
  1. 第1086行: half → float (explicit_cast)
     代码: float _8657 = float(_18329);
     原因: 强制精度转换为 float

变量 '_18824': 1 次转换
  1. 第1028行: half3 → float3 (explicit_cast)
     代码: float3 _14569 = float3(_18824);
     原因: 强制精度转换为 float3

变量 '_18826': 3 次转换
  1. 第1029行: half3 → float (implicit_promotion)
     代码: float _14575 = float(dot(_18826, _18826));
     原因: dot 函数中的精度提升
  2. 第1029行: half3 → float (implicit_promotion)
     代码: float _14575 = float(dot(_18826, _18826));
     原因: dot 函数中的精度提升
  3. 第1034行: half3 → float3 (explicit_cast)
     代码: float3 _14600 = (float3(_18826) / float3(sqrt(_14575))) * float3(fast::clamp(1.0 - (_14592 * _14592), 0.0, 1.0));
     原因: 强制精度转换为 float3

变量 '_19481': 1 次转换
  1. 第750行: half → float (explicit_cast)
     代码: float _14176 = float(_19481);
     原因: 强制精度转换为 float

变量 '_19821': 1 次转换
  1. 第758行: half3 → float3 (explicit_cast)
     代码: half3 _14069 = half3(((float3(_19821) * float3(_19786)) * _14176) * float(clamp(((_14029 + _11768) / _11777) * _11777, half(0.0), half(1.0)) * clamp(((_19481 + _11764) / _11790) * _11790, half(0.0), half(1.0))));
     原因: 强制精度转换为 float3

变量 '_8303': 2 次转换
  1. 第201行: half3 → float (implicit_promotion)
     代码: half3 _8315 = mix(half3(dot(_18225, _8303)), _18225, half3(half(_Block1.cSaturation)));
     原因: dot 函数中的精度提升
  2. 第1116行: half3 → float3 (explicit_cast)
     代码: float3 _16698 = float3(_8303);
     原因: 强制精度转换为 float3

变量 '_8929': 1 次转换
  1. 第143行: half → float (explicit_cast)
     代码: half _9096 = half(mix(1.0, float(mix(half3(_9019), half3(half(fast::clamp(_9014 + _Block1.cAOoffset, 0.0, 1.0))), in.IN_TintColor.xxx).x * _9074.z), float(_8929)));
     原因: 强制精度转换为 float

变量 '_8949': 1 次转换
  1. 第147行: half3 → float3 (explicit_cast)
     代码: half3 _9145 = _9130.xyz * half3((float3(_Block1.cEmissionColor) * _Block1.cEmissionScale) * float3(_8949));
     原因: 强制精度转换为 float3

变量 '_9064': 4 次转换
  1. 第145行: half3 → float3 (explicit_cast)
     代码: float3 _9109 = float3(_9064);
     原因: 强制精度转换为 float3
  2. 第488行: half3 → float (implicit_promotion)
     代码: half _11827 = clamp(dot(half3(fast::normalize(_11622 + _11517)), _9064), half(0.0), half(1.0));
     原因: dot 函数中的精度提升
  3. 第759行: half3 → float (implicit_promotion)
     代码: half _14084 = clamp(dot(half3(fast::normalize(_19751 + _19716)), _9064), half(0.0), half(1.0));
     原因: dot 函数中的精度提升
  4. 第1109行: half3 → float (implicit_promotion)
     代码: half3 _16258 = ((((max(_16279 + (half3(half(dot(_Block1.cSHCoefficients[1], _16284)), half(dot(_Block1.cSHCoefficients[3], _16284)), half(dot(_Block1.cSHCoefficients[5], _16284))) + half3(half(dot(_Block1.cSHCoefficients[2], _16306)), half(dot(_Block1.cSHCoefficients[4], _16306)), half(dot(_Block1.cSHCoefficients[6], _16306)))), _9179) * half3(half(0.699999988079071044921875 + (0.300000011920928955078125 * _Block1.EnvInfo.z)))) * half3(half(float(_8295 * _18329) * mix(1.0, _Block1.WorldProbeInfo.y, step(0.0, _Block1.cVisibilitySH[0].w))))) * half3(half(_Block1.GIInfo.z))) + half3(float3(max(_16279 + (half3(half(dot(_Block1.cSHCoefficients[1], _16451)), half(dot(_Block1.cSHCoefficients[3], _16451)), half(dot(_Block1.cSHCoefficients[5], _16451))) + half3(half(dot(_Block1.cSHCoefficients[2], _16473)), half(dot(_Block1.cSHCoefficients[4], _16473)), half(dot(_Block1.cSHCoefficients[6], _16473)))), _9179)) * float3(((3.1415927410125732421875 * float(clamp(dot(_9064, _16397), half(0.0), half(1.0)))) * _Block1.WorldProbeInfo.w) * float(half(float(_8295 * _18236)) * half(0.5))))) * half3(half(_Block1.cSHCoefficients[0].w));
     原因: dot 函数中的精度提升

变量 '_9096': 1 次转换
  1. 第144行: half → float (explicit_cast)
     代码: float _9100 = float(_9096);
     原因: 强制精度转换为 float

变量 '_9179': 1 次转换
  1. 第157行: half3 → float3 (explicit_cast)
     代码: float3 _9698 = float3(_9179);
     原因: 强制精度转换为 float3

变量 '_9257': 1 次转换
  1. 第138行: half → float (explicit_cast)
     代码: float3 _9331 = (((float4(float3(in.IN_WorldTangent.xyz), 0.0) * _Block1.Local) * float(_9257)) + ((float4(float3(in.IN_WorldBinormal.xyz), 0.0) * _Block1.Local) * float(_9263))) + (_9286 * float(_9270));
     原因: 强制精度转换为 float

变量 '_9263': 1 次转换
  1. 第138行: half → float (explicit_cast)
     代码: float3 _9331 = (((float4(float3(in.IN_WorldTangent.xyz), 0.0) * _Block1.Local) * float(_9257)) + ((float4(float3(in.IN_WorldBinormal.xyz), 0.0) * _Block1.Local) * float(_9263))) + (_9286 * float(_9270));
     原因: 强制精度转换为 float

变量 '_9270': 1 次转换
  1. 第138行: half → float (explicit_cast)
     代码: float3 _9331 = (((float4(float3(in.IN_WorldTangent.xyz), 0.0) * _Block1.Local) * float(_9257)) + ((float4(float3(in.IN_WorldBinormal.xyz), 0.0) * _Block1.Local) * float(_9263))) + (_9286 * float(_9270));
     原因: 强制精度转换为 float

变量 '_9535': 1 次转换
  1. 第179行: half → float (explicit_cast)
     代码: float _9556 = float(half((float(_9535) * fast::clamp(float(_9535 + _18217) + 0.5, 0.0, 1.0)) * _9429));
     原因: 强制精度转换为 float

变量 '_9585': 1 次转换
  1. 第183行: half → float (explicit_cast)
     代码: float _9603 = float(_9585);
     原因: 强制精度转换为 float

变量 '_9772': 1 次转换
  1. 第184行: half → float (explicit_cast)
     代码: _18272 = half(mix(float(_9772), 1.0, _9603));
     原因: 强制精度转换为 float

变量 'in.IN_LocalPosition.y': 1 次转换
  1. 第178行: half → float (explicit_cast)
     代码: half _9535 = half(1.0 - fast::clamp((float(in.IN_LocalPosition.y) * (2.0 - _9460)) * _Block1.cCISnowData.y, 0.0, 1.0));
     原因: 强制精度转换为 float

变量 'in.IN_StaticWorldNormal.w': 1 次转换
  1. 第176行: half → float (explicit_cast)
     代码: float _9510 = float(half(fast::clamp(((float2(0.800000011920928955078125, 0.5) * _9429).x * (1.0 - powr(float(half(powr(float(clamp(_18217, half(0.0), half(1.0))) + fast::clamp(0.20000000298023223876953125 - fast::clamp((-1.0) * _Block1.cCISnowData.z, 0.0, 1.0), 0.0, 1.0), 2.0 - _9462) * float(in.IN_StaticWorldNormal.w))), 0.800000011920928955078125 - (_9462 * 0.4000000059604644775390625)))) + ((_9499 * _9499) * _9499), 0.0, 1.0)));
     原因: 强制精度转换为 float

变量 'in.IN_StaticWorldNormal.xyz': 1 次转换
  1. 第137行: half3 → float3 (explicit_cast)
     代码: float3 _9286 = float3(in.IN_StaticWorldNormal.xyz);
     原因: 强制精度转换为 float3

变量 'in.IN_WorldBinormal.xyz': 1 次转换
  1. 第138行: half3 → float3 (explicit_cast)
     代码: float3 _9331 = (((float4(float3(in.IN_WorldTangent.xyz), 0.0) * _Block1.Local) * float(_9257)) + ((float4(float3(in.IN_WorldBinormal.xyz), 0.0) * _Block1.Local) * float(_9263))) + (_9286 * float(_9270));
     原因: 强制精度转换为 float3

变量 'in.IN_WorldNormal.xyz': 1 次转换
  1. 第111行: half3 → float3 (explicit_cast)
     代码: float3 _8925 = float3(in.IN_WorldNormal.xyz);
     原因: 强制精度转换为 float3

变量 'in.IN_WorldTangent.xyz': 1 次转换
  1. 第138行: half3 → float3 (explicit_cast)
     代码: float3 _9331 = (((float4(float3(in.IN_WorldTangent.xyz), 0.0) * _Block1.Local) * float(_9257)) + ((float4(float3(in.IN_WorldBinormal.xyz), 0.0) * _Block1.Local) * float(_9263))) + (_9286 * float(_9270));
     原因: 强制精度转换为 float3

