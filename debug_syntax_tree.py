#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试语法树构建
"""

from Process.Analysis.syntax_tree_builder import SyntaxTreeAnalyzer

def debug_syntax_tree():
    """调试语法树构建"""
    
    analyzer = SyntaxTreeAnalyzer()
    
    # 简单的测试代码
    test_code = '''
    float4 main() {
        half a = 1.0h;
        float b = 2.0f;
        
        float result = a + b;
        
        return float4(result, 0, 0, 1);
    }
    '''
    
    print("=== 调试语法树构建 ===")
    
    # 分析代码
    result = analyzer.analyze_shader_with_syntax_trees(test_code)
    
    print("=== 语法树分析 ===")
    for line_analysis in result['line_analyses']:
        line_content = line_analysis.line_content.strip()
        if not line_content or line_content.startswith('//') or line_content in ['{', '}']:
            continue
            
        print(f"\n第 {line_analysis.line_number} 行: {line_content}")
        
        # 检查语法树
        if hasattr(line_analysis, 'syntax_tree') and line_analysis.syntax_tree:
            tree = line_analysis.syntax_tree
            print(f"  语法树根节点: {tree.node_type.name} - {tree.value}")
            
            def print_tree(node, indent=2):
                if node.children:
                    for child in node.children:
                        print(f"{'  ' * indent}子节点: {child.node_type.name} - {child.value}")
                        print_tree(child, indent + 1)
            
            print_tree(tree)
        else:
            print(f"  无语法树")
        
        # 检查运算过程
        if hasattr(line_analysis, 'operation_process') and line_analysis.operation_process:
            print(f"  运算过程: {len(line_analysis.operation_process)} 个操作")
            for op in line_analysis.operation_process:
                print(f"    {op.string}")
        else:
            print(f"  无运算过程")
        
        # 检查类型转换
        if hasattr(line_analysis, 'type_conversions') and line_analysis.type_conversions:
            print(f"  类型转换: {len(line_analysis.type_conversions)} 个")
            for conv in line_analysis.type_conversions:
                print(f"    {conv}")
        else:
            print(f"  无类型转换")

if __name__ == "__main__":
    debug_syntax_tree()
