=== 运算过程分析日志 ===

第2行运算过程: vertex VertexOut vertex_main(
  无运算过程
==================================================
第3行运算过程: VertexIn in [[stage_in]],
  无运算过程
==================================================
第4行运算过程: constant Uniforms& uniforms [[buffer(0)]]
  无运算过程
==================================================
第6行运算过程: VertexOut out;
  无运算过程
==================================================
第9行运算过程: half _9176 = 0.5h;
运算过程详情:
  步骤1: _9176 = 0.5
    结果类型: half
    操作数类型: float

==================================================
第10行运算过程: half3 position = half3(_9176);  // _9176: half → half3 (scalar_broadcast)
运算过程详情:
  步骤1: tmp_0 = half3(_9176)
    结果类型: half3
    操作数类型: half

  步骤2: position = tmp_0
    结果类型: half3
    操作数类型: half3

==================================================
第13行运算过程: float4 worldPos = float4(position, 1.0f);  // position: half3 → float4 (precision_promotion)
运算过程详情:
  步骤1: tmp_0 = float4(position, 1.0f)
    结果类型: float4
    操作数类型: half3, float

  步骤2: worldPos = tmp_0
    结果类型: float4
    操作数类型: float4

==================================================
第16行运算过程: half4x4 mvpMatrix = uniforms.mvpMatrix;
运算过程详情:
  步骤1: mvpMatrix = uniforms.mvpMatrix
    结果类型: half4x4
    操作数类型: unknown

==================================================
第17行运算过程: float4 clipPos = mvpMatrix * worldPos;  // mvpMatrix: half4x4 → float4x4 (precision_promotion)
运算过程详情:
  步骤1: tmp_0 = mvpMatrix * worldPos
    结果类型: half4x4
    操作数类型: half4x4, float4

  步骤2: clipPos = tmp_0
    结果类型: float4
    操作数类型: half4x4

==================================================
第20行运算过程: int index = 42;
运算过程详情:
  步骤1: index = 42
    结果类型: int
    操作数类型: int

==================================================
第21行运算过程: float weight = index * 0.1f;  // index: int → float (precision_promotion)
运算过程详情:
  步骤1: tmp_0 = index * 0.1f
    结果类型: float
    操作数类型: int, float

  步骤2: weight = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第23行运算过程: out.position = clipPos;
运算过程详情:
  步骤1: out.position = clipPos
    操作数类型: float4

==================================================
第24行运算过程: out.color = float4(weight, weight, weight, 1.0f);
运算过程详情:
  步骤1: tmp_0 = float4(weight, weight, weight, 1.0f)
    结果类型: float4
    操作数类型: float, float, float, float

  步骤2: out.color = tmp_0
    操作数类型: float4

==================================================
第26行运算过程: return out;
  无运算过程
==================================================
