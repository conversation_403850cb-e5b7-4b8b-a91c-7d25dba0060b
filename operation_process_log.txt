=== 运算过程分析日志 ===

第2行运算过程: float4 main() {
  无运算过程
==================================================
第3行运算过程: half a = 1.0h;
运算过程详情:
  步骤1: a = 1.0
    结果类型: half
    操作数类型: float

==================================================
第4行运算过程: float b = 2.0f;
运算过程详情:
  步骤1: b = 2.0f
    结果类型: float
    操作数类型: float

==================================================
第7行运算过程: float result1 = a + b;          // a: half → float
运算过程详情:
  步骤1: tmp_0 = a + b
    结果类型: float
    操作数类型: half, float

  步骤2: result1 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第10行运算过程: float result2 = float(a);       // a: half → float (强转)
运算过程详情:
  步骤1: tmp_0 = float(a)
    结果类型: float
    操作数类型: half

  步骤2: result2 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第11行运算过程: half result3 = half(b);         // b: float → half (强转)
运算过程详情:
  步骤1: tmp_0 = half(b)
    结果类型: half
    操作数类型: float

  步骤2: result3 = tmp_0
    结果类型: half
    操作数类型: half

==================================================
第14行运算过程: float result4 = dot(a, b);      // a: half → float
运算过程详情:
  步骤1: tmp_0 = dot(a, b)
    结果类型: float
    操作数类型: half, float

  步骤2: result4 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第17行运算过程: float result5 = a * _Block1.cEmissionScale;
运算过程详情:
  步骤1: tmp_0 = a * _Block1.cEmissionScale
    结果类型: half
    操作数类型: half, unknown

  步骤2: result5 = tmp_0
    结果类型: float
    操作数类型: half

==================================================
第19行运算过程: return float4(result1, result2, result3, result4);
  无运算过程
==================================================
