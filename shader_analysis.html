<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 精确类型分析报告</title>
    <style>
        /* 浅色主题样式 */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
            transition: all 0.3s ease;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        .controls {
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .control-group {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }
        .control-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .style-selector {
            margin-left: auto;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .style-selector label {
            font-weight: 500;
            color: #666;
        }
        .style-selector select {
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            font-size: 12px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            transition: all 0.3s ease;
        }
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #4fc3f7;
            margin-bottom: 5px;
        }
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        .code-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .code-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
        }
        .code-line {
            display: flex;
            border-bottom: 1px solid #f0f0f0;
            min-height: 24px;
            align-items: flex-start;
            transition: background-color 0.2s;
        }
        .code-line:hover {
            background-color: #f8f9fa;
        }
        .line-number {
            background-color: #f8f9fa;
            color: #666;
            padding: 4px 12px;
            font-family: 'Consolas', monospace;
            font-size: 12px;
            min-width: 60px;
            text-align: right;
            border-right: 1px solid #dee2e6;
            user-select: none;
            transition: all 0.3s ease;
        }
        .line-content {
            flex: 1;
            padding: 4px 12px;
            font-family: 'Consolas', monospace;
            font-size: 13px;
            overflow-x: auto;
            transition: all 0.3s ease;
        }
        .code-text {
            white-space: pre;
            margin-bottom: 8px;
        }
        .analysis-info {
            margin-top: 8px;
            padding-top: 8px;
            border-top: 1px solid #eee;
        }
        .basic-stats {
            font-size: 11px;
            color: #666;
            font-style: italic;
            margin-bottom: 8px;
        }
        .operation-process, .type-conversions, .variable-types {
            margin-bottom: 12px;
        }
        .operation-process h4, .type-conversions h4, .variable-types h4 {
            font-size: 12px;
            margin: 0 0 6px 0;
            color: #555;
            font-weight: 600;
        }
        .operation-list, .conversion-list, .variable-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .operation-step {
            margin-bottom: 4px;
            padding: 4px 8px;
            background: #f8f9fa;
            border-radius: 4px;
            font-size: 11px;
        }
        .step-number {
            color: #666;
            font-weight: 600;
            margin-right: 4px;
        }
        .operation-code {
            background: #e9ecef;
            padding: 2px 4px;
            border-radius: 2px;
            font-family: 'Consolas', monospace;
            font-size: 10px;
        }
        .type-info {
            margin-top: 2px;
            font-size: 10px;
        }
        .left-types, .right-types {
            margin-right: 8px;
            padding: 1px 4px;
            border-radius: 2px;
            font-size: 9px;
        }
        .left-types {
            background: #e3f2fd;
            color: #1976d2;
        }
        .right-types {
            background: #f3e5f5;
            color: #7b1fa2;
        }
        .conversion-item {
            margin-bottom: 4px;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
        }
        .conversion-item.precision-loss {
            background: #ffebee;
            border-left: 3px solid #f44336;
        }
        .conversion-item.precision-gain {
            background: #e8f5e8;
            border-left: 3px solid #4caf50;
        }
        .conversion-item.vector-conversion {
            background: #fff3e0;
            border-left: 3px solid #ff9800;
        }
        .conversion-item.implicit-conversion {
            background: #f3e5f5;
            border-left: 3px solid #9c27b0;
        }
        .conversion-types {
            font-weight: 600;
            margin-right: 8px;
        }
        .conversion-type {
            color: #666;
            font-size: 10px;
        }
        .conversion-operation {
            margin-top: 2px;
            font-size: 10px;
            color: #666;
            font-family: 'Consolas', monospace;
        }
        .conversion-reason {
            margin-top: 2px;
            font-size: 10px;
            color: #888;
            font-style: italic;
        }
        .variable-item {
            margin-bottom: 2px;
            font-size: 11px;
        }
        .var-name {
            font-weight: 600;
            color: #1976d2;
        }
        .var-type {
            color: #7b1fa2;
            font-family: 'Consolas', monospace;
        }
        .node-info {
            font-size: 11px;
            color: #666;
            margin-top: 2px;
            font-style: italic;
            transition: all 0.3s ease;
        }
        .type-conversion {
            background-color: #fff3e0;
        }
        .precision-issue {
            background-color: #ffebee;
        }
        .performance-normal {
            border-left: 3px solid #4caf50;
        }
        .performance-intensive {
            border-left: 3px solid #ff9800;
        }
        .performance-conversion {
            border-left: 3px solid #f44336;
        }
        .performance-issue {
            border-left: 3px solid #9c27b0;
        }
        .hidden {
            display: none;
        }
        .filter-info {
            padding: 10px;
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            margin-bottom: 10px;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        /* 深色主题样式 */
        body.dark-theme {
            background-color: #1a1a1a;
            color: #e0e0e0;
        }
        .dark-theme .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        }
        .dark-theme .controls {
            background: #2d2d2d;
            color: #e0e0e0;
            border: 1px solid #444;
        }
        .dark-theme .control-item label {
            color: #e0e0e0;
        }
        .dark-theme .style-selector label {
            color: #b0b0b0;
        }
        .dark-theme .style-selector select {
            background: #3a3a3a;
            color: #e0e0e0;
            border-color: #555;
        }
        .dark-theme .stat-card {
            background: #2d2d2d;
            color: #e0e0e0;
            border: 1px solid #444;
        }
        .dark-theme .stat-value {
            color: #64b5f6;
        }
        .dark-theme .stat-label {
            color: #b0b0b0;
        }
        .dark-theme .code-section {
            background: #2d2d2d;
            border: 1px solid #444;
        }
        .dark-theme .code-header {
            background: #3a3a3a;
            border-bottom-color: #555;
            color: #e0e0e0;
        }
        .dark-theme .code-line {
            border-bottom-color: #444;
        }
        .dark-theme .code-line:hover {
            background-color: #3a3a3a;
        }
        .dark-theme .line-number {
            background-color: #3a3a3a;
            color: #b0b0b0;
            border-right-color: #555;
        }
        .dark-theme .line-content {
            color: #e0e0e0;
        }
        .dark-theme .node-info {
            color: #b0b0b0;
        }
        .dark-theme .filter-info {
            background: #1e3a5f;
            border-left-color: #4a9eff;
            color: #e0e0e0;
        }
        .dark-theme .type-conversion {
            background-color: #3d2c1a;
        }
        .dark-theme .precision-issue {
            background-color: #3d1a1a;
        }
        .dark-theme .analysis-info {
            border-top-color: #555;
        }
        .dark-theme .basic-stats {
            color: #b0b0b0;
        }
        .dark-theme .operation-process h4, .dark-theme .type-conversions h4, .dark-theme .variable-types h4 {
            color: #e0e0e0;
        }
        .dark-theme .operation-step {
            background: #3a3a3a;
            color: #e0e0e0;
        }
        .dark-theme .operation-code {
            background: #2d2d2d;
            color: #e0e0e0;
        }
        .dark-theme .left-types {
            background: #1e3a5f;
            color: #64b5f6;
        }
        .dark-theme .right-types {
            background: #3d1a3d;
            color: #ba68c8;
        }
        .dark-theme .conversion-item.precision-loss {
            background: #3d1a1a;
        }
        .dark-theme .conversion-item.precision-gain {
            background: #1a3d1a;
        }
        .dark-theme .conversion-item.vector-conversion {
            background: #3d2c1a;
        }
        .dark-theme .conversion-item.implicit-conversion {
            background: #3d1a3d;
        }
        .dark-theme .conversion-type, .dark-theme .conversion-operation {
            color: #b0b0b0;
        }
        .dark-theme .var-name {
            color: #64b5f6;
        }
        .dark-theme .var-type {
            color: #ba68c8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 精确类型分析报告</h1>
            <p>基于语法树的Metal着色器运算过程分析</p>
            <p>总代码行数: 28 | 分析时间: 2025-08-05 17:48:14</p>
        </div>

        <!-- 显示控制面板 -->
        <div class="controls">
            <div class="control-group">
                <div class="control-item">
                    <input type="checkbox" id="showNodeDetails" checked>
                    <label for="showNodeDetails">显示基本统计</label>
                </div>
                <div class="control-item">
                    <input type="checkbox" id="showOperationProcess" checked>
                    <label for="showOperationProcess">显示运算过程</label>
                </div>
                <div class="control-item">
                    <input type="checkbox" id="showTypeConversions" checked>
                    <label for="showTypeConversions">显示类型转换</label>
                </div>
                <div class="control-item">
                    <input type="checkbox" id="showVariableTypes">
                    <label for="showVariableTypes">显示变量类型</label>
                </div>
                <div class="control-item">
                    <input type="checkbox" id="showTypeColors" checked>
                    <label for="showTypeColors">类型颜色标记</label>
                </div>
                <div class="control-item">
                    <input type="checkbox" id="showOnlyAnalyzed">
                    <label for="showOnlyAnalyzed">只显示有分析的行</label>
                </div>
                <div class="control-item">
                    <select id="performanceFilter">
                        <option value="all">显示所有行</option>
                        <option value="conversion">只显示类型转换</option>
                        <option value="intensive">只显示运算密集</option>
                        <option value="issue">只显示精度问题</option>
                    </select>
                </div>
                <div class="style-selector">
                    <label for="themeSelector">主题样式:</label>
                    <select id="themeSelector">
                        <option value="light">浅色主题</option>
                        <option value="dark">深色主题</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">27</div>
                <div class="stat-label">总节点数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">15</div>
                <div class="stat-label">有运算的行</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">14</div>
                <div class="stat-label">总运算次数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">9</div>
                <div class="stat-label">类型转换</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">0</div>
                <div class="stat-label">精度问题</div>
            </div>
        </div>

        <div class="code-section">
            <div class="code-header">
                <h3>📝 完整着色器代码分析</h3>
                <div class="filter-info" id="filterInfo" style="display: none;">
                    <span id="filterText"></span>
                </div>
            </div>

            <div class="code-line " data-has-analysis="false" data-performance="normal" data-operations="0" data-conversions="0">
                <div class="line-number">1</div>
                <div class="line-content">
                    <div class="code-text"></div>
                    <div class="analysis-info">
                        <div class="basic-stats"></div>
                        
                    </div>
                </div>
            </div>
            
            <div class="code-line " data-has-analysis="true" data-performance="normal" data-operations="0" data-conversions="0">
                <div class="line-number">2</div>
                <div class="line-content">
                    <div class="code-text">vertex VertexOut vertex_main(</div>
                    <div class="analysis-info">
                        <div class="basic-stats"></div>
                        <div class="variable-types"><h4>变量类型:</h4><ul class="variable-list"><li class="variable-item"><span class="var-name">VertexOut</span>: <span class="var-type">unknown</span></li></ul></div>
                    </div>
                </div>
            </div>
            
            <div class="code-line " data-has-analysis="true" data-performance="normal" data-operations="0" data-conversions="0">
                <div class="line-number">3</div>
                <div class="line-content">
                    <div class="code-text">    VertexIn in [[stage_in]],</div>
                    <div class="analysis-info">
                        <div class="basic-stats"></div>
                        <div class="variable-types"><h4>变量类型:</h4><ul class="variable-list"><li class="variable-item"><span class="var-name">in</span>: <span class="var-type">unknown</span></li></ul></div>
                    </div>
                </div>
            </div>
            
            <div class="code-line " data-has-analysis="true" data-performance="normal" data-operations="0" data-conversions="0">
                <div class="line-number">4</div>
                <div class="line-content">
                    <div class="code-text">    constant Uniforms&amp; uniforms [[buffer(0)]]</div>
                    <div class="analysis-info">
                        <div class="basic-stats"></div>
                        <div class="variable-types"><h4>变量类型:</h4><ul class="variable-list"><li class="variable-item"><span class="var-name">constant</span>: <span class="var-type">unknown</span></li></ul></div>
                    </div>
                </div>
            </div>
            
            <div class="code-line " data-has-analysis="true" data-performance="normal" data-operations="0" data-conversions="0">
                <div class="line-number">5</div>
                <div class="line-content">
                    <div class="code-text">) {</div>
                    <div class="analysis-info">
                        <div class="basic-stats"></div>
                        
                    </div>
                </div>
            </div>
            
            <div class="code-line " data-has-analysis="true" data-performance="normal" data-operations="0" data-conversions="0">
                <div class="line-number">6</div>
                <div class="line-content">
                    <div class="code-text">    VertexOut out;</div>
                    <div class="analysis-info">
                        <div class="basic-stats"></div>
                        <div class="variable-types"><h4>变量类型:</h4><ul class="variable-list"><li class="variable-item"><span class="var-name">out</span>: <span class="var-type">unknown</span></li></ul></div>
                    </div>
                </div>
            </div>
            
            <div class="code-line " data-has-analysis="false" data-performance="normal" data-operations="0" data-conversions="0">
                <div class="line-number">7</div>
                <div class="line-content">
                    <div class="code-text">    </div>
                    <div class="analysis-info">
                        <div class="basic-stats"></div>
                        
                    </div>
                </div>
            </div>
            
            <div class="code-line " data-has-analysis="false" data-performance="normal" data-operations="0" data-conversions="0">
                <div class="line-number">8</div>
                <div class="line-content">
                    <div class="code-text">    // 类型转换场景1: 构造函数中的标量广播</div>
                    <div class="analysis-info">
                        <div class="basic-stats"></div>
                        
                    </div>
                </div>
            </div>
            
            <div class="code-line performance-normal" data-has-analysis="true" data-performance="normal" data-operations="1" data-conversions="0">
                <div class="line-number">9</div>
                <div class="line-content">
                    <div class="code-text">    half _9176 = 0.5h;</div>
                    <div class="analysis-info">
                        <div class="basic-stats">运算: 1次</div>
                        <div class="operation-process"><h4>运算过程:</h4><ul class="operation-list"><li class="operation-step"><span class="step-number">1.</span> <code class="operation-code">_9176 = 0.5</code><div class="type-info"><span class="left-types">左: half</span><span class="right-types">右: float</span></div></li></ul></div><div class="variable-types"><h4>变量类型:</h4><ul class="variable-list"><li class="variable-item"><span class="var-name">_9176</span>: <span class="var-type">half</span></li></ul></div>
                    </div>
                </div>
            </div>
            
            <div class="code-line type-conversion performance-conversion" data-has-analysis="true" data-performance="conversion" data-operations="2" data-conversions="1">
                <div class="line-number">10</div>
                <div class="line-content">
                    <div class="code-text">    half3 position = half3(_9176);  // _9176: half → half3 (scalar_broadcast)</div>
                    <div class="analysis-info">
                        <div class="basic-stats">运算: 2次 | 类型转换: 1次</div>
                        <div class="operation-process"><h4>运算过程:</h4><ul class="operation-list"><li class="operation-step"><span class="step-number">1.</span> <code class="operation-code">tmp_0 = half3(_9176)</code><div class="type-info"><span class="left-types">左: half3</span><span class="right-types">右: half</span></div></li><li class="operation-step"><span class="step-number">2.</span> <code class="operation-code">position = tmp_0</code><div class="type-info"><span class="left-types">左: half3</span><span class="right-types">右: half3</span></div></li></ul></div><div class="type-conversions"><h4>类型转换:</h4><ul class="conversion-list"><li class="conversion-item implicit-conversion"><span class="var-name">变量 _9176:</span> <span class="conversion-types">half → half3</span> <span class="conversion-type">(scalar_broadcast)</span><div class="conversion-reason">原因: 构造函数参数转换为 half3</div><div class="conversion-operation">在: tmp_0 = half3(_9176)</div></li></ul></div><div class="variable-types"><h4>变量类型:</h4><ul class="variable-list"><li class="variable-item"><span class="var-name">position</span>: <span class="var-type">half3</span></li><li class="variable-item"><span class="var-name">_9176</span>: <span class="var-type">half</span></li></ul></div>
                    </div>
                </div>
            </div>
            
            <div class="code-line " data-has-analysis="false" data-performance="normal" data-operations="0" data-conversions="0">
                <div class="line-number">11</div>
                <div class="line-content">
                    <div class="code-text">    </div>
                    <div class="analysis-info">
                        <div class="basic-stats"></div>
                        
                    </div>
                </div>
            </div>
            
            <div class="code-line " data-has-analysis="false" data-performance="normal" data-operations="0" data-conversions="0">
                <div class="line-number">12</div>
                <div class="line-content">
                    <div class="code-text">    // 类型转换场景2: 精度提升运算</div>
                    <div class="analysis-info">
                        <div class="basic-stats"></div>
                        
                    </div>
                </div>
            </div>
            
            <div class="code-line type-conversion performance-conversion" data-has-analysis="true" data-performance="conversion" data-operations="2" data-conversions="2">
                <div class="line-number">13</div>
                <div class="line-content">
                    <div class="code-text">    float4 worldPos = float4(position, 1.0f);  // position: half3 → float4 (precision_promotion)</div>
                    <div class="analysis-info">
                        <div class="basic-stats">运算: 2次 | 类型转换: 2次</div>
                        <div class="operation-process"><h4>运算过程:</h4><ul class="operation-list"><li class="operation-step"><span class="step-number">1.</span> <code class="operation-code">tmp_0 = float4(position, 1.0f)</code><div class="type-info"><span class="left-types">左: float4</span><span class="right-types">右: half3, float</span></div></li><li class="operation-step"><span class="step-number">2.</span> <code class="operation-code">worldPos = tmp_0</code><div class="type-info"><span class="left-types">左: float4</span><span class="right-types">右: float4</span></div></li></ul></div><div class="type-conversions"><h4>类型转换:</h4><ul class="conversion-list"><li class="conversion-item vector-conversion"><span class="var-name">变量 position:</span> <span class="conversion-types">half3 → float4</span> <span class="conversion-type">(vector_conversion)</span><div class="conversion-reason">原因: 构造函数参数转换为 float4</div><div class="conversion-operation">在: tmp_0 = float4(position, 1.0f)</div></li><li class="conversion-item implicit-conversion"><span class="var-name">变量 1.0f:</span> <span class="conversion-types">float → float4</span> <span class="conversion-type">(scalar_broadcast)</span><div class="conversion-reason">原因: 构造函数参数转换为 float4</div><div class="conversion-operation">在: tmp_0 = float4(position, 1.0f)</div></li></ul></div><div class="variable-types"><h4>变量类型:</h4><ul class="variable-list"><li class="variable-item"><span class="var-name">worldPos</span>: <span class="var-type">float4</span></li><li class="variable-item"><span class="var-name">position</span>: <span class="var-type">half3</span></li></ul></div>
                    </div>
                </div>
            </div>
            
            <div class="code-line " data-has-analysis="false" data-performance="normal" data-operations="0" data-conversions="0">
                <div class="line-number">14</div>
                <div class="line-content">
                    <div class="code-text">    </div>
                    <div class="analysis-info">
                        <div class="basic-stats"></div>
                        
                    </div>
                </div>
            </div>
            
            <div class="code-line " data-has-analysis="false" data-performance="normal" data-operations="0" data-conversions="0">
                <div class="line-number">15</div>
                <div class="line-content">
                    <div class="code-text">    // 类型转换场景3: 矩阵运算中的类型转换</div>
                    <div class="analysis-info">
                        <div class="basic-stats"></div>
                        
                    </div>
                </div>
            </div>
            
            <div class="code-line performance-normal" data-has-analysis="true" data-performance="normal" data-operations="1" data-conversions="0">
                <div class="line-number">16</div>
                <div class="line-content">
                    <div class="code-text">    half4x4 mvpMatrix = uniforms.mvpMatrix;</div>
                    <div class="analysis-info">
                        <div class="basic-stats">运算: 1次</div>
                        <div class="operation-process"><h4>运算过程:</h4><ul class="operation-list"><li class="operation-step"><span class="step-number">1.</span> <code class="operation-code">mvpMatrix = uniforms.mvpMatrix</code><div class="type-info"><span class="left-types">左: half4x4</span><span class="right-types">右: unknown</span></div></li></ul></div><div class="variable-types"><h4>变量类型:</h4><ul class="variable-list"><li class="variable-item"><span class="var-name">mvpMatrix</span>: <span class="var-type">half4x4</span></li><li class="variable-item"><span class="var-name">uniforms.mvpMatrix</span>: <span class="var-type">unknown</span></li></ul></div>
                    </div>
                </div>
            </div>
            
            <div class="code-line type-conversion performance-conversion" data-has-analysis="true" data-performance="conversion" data-operations="2" data-conversions="1">
                <div class="line-number">17</div>
                <div class="line-content">
                    <div class="code-text">    float4 clipPos = mvpMatrix * worldPos;  // mvpMatrix: half4x4 → float4x4 (precision_promotion)</div>
                    <div class="analysis-info">
                        <div class="basic-stats">运算: 2次 | 类型转换: 1次</div>
                        <div class="operation-process"><h4>运算过程:</h4><ul class="operation-list"><li class="operation-step"><span class="step-number">1.</span> <code class="operation-code">tmp_0 = mvpMatrix * worldPos</code><div class="type-info"><span class="left-types">左: half4x4</span><span class="right-types">右: half4x4, float4</span></div></li><li class="operation-step"><span class="step-number">2.</span> <code class="operation-code">clipPos = tmp_0</code><div class="type-info"><span class="left-types">左: float4</span><span class="right-types">右: half4x4</span></div></li></ul></div><div class="type-conversions"><h4>类型转换:</h4><ul class="conversion-list"><li class="conversion-item implicit-conversion"><span class="var-name">变量 tmp_0:</span> <span class="conversion-types">half4x4 → float4</span> <span class="conversion-type">(type_conversion)</span><div class="conversion-reason">原因: 赋值类型转换</div><div class="conversion-operation">在: clipPos = tmp_0</div></li></ul></div><div class="variable-types"><h4>变量类型:</h4><ul class="variable-list"><li class="variable-item"><span class="var-name">clipPos</span>: <span class="var-type">float4</span></li><li class="variable-item"><span class="var-name">mvpMatrix</span>: <span class="var-type">half4x4</span></li><li class="variable-item"><span class="var-name">worldPos</span>: <span class="var-type">float4</span></li></ul></div>
                    </div>
                </div>
            </div>
            
            <div class="code-line " data-has-analysis="false" data-performance="normal" data-operations="0" data-conversions="0">
                <div class="line-number">18</div>
                <div class="line-content">
                    <div class="code-text">    </div>
                    <div class="analysis-info">
                        <div class="basic-stats"></div>
                        
                    </div>
                </div>
            </div>
            
            <div class="code-line " data-has-analysis="false" data-performance="normal" data-operations="0" data-conversions="0">
                <div class="line-number">19</div>
                <div class="line-content">
                    <div class="code-text">    // 类型转换场景4: 混合类型运算</div>
                    <div class="analysis-info">
                        <div class="basic-stats"></div>
                        
                    </div>
                </div>
            </div>
            
            <div class="code-line performance-normal" data-has-analysis="true" data-performance="normal" data-operations="1" data-conversions="0">
                <div class="line-number">20</div>
                <div class="line-content">
                    <div class="code-text">    int index = 42;</div>
                    <div class="analysis-info">
                        <div class="basic-stats">运算: 1次</div>
                        <div class="operation-process"><h4>运算过程:</h4><ul class="operation-list"><li class="operation-step"><span class="step-number">1.</span> <code class="operation-code">index = 42</code><div class="type-info"><span class="left-types">左: int</span><span class="right-types">右: int</span></div></li></ul></div><div class="variable-types"><h4>变量类型:</h4><ul class="variable-list"><li class="variable-item"><span class="var-name">index</span>: <span class="var-type">int</span></li></ul></div>
                    </div>
                </div>
            </div>
            
            <div class="code-line type-conversion performance-conversion" data-has-analysis="true" data-performance="conversion" data-operations="2" data-conversions="1">
                <div class="line-number">21</div>
                <div class="line-content">
                    <div class="code-text">    float weight = index * 0.1f;  // index: int → float (precision_promotion)</div>
                    <div class="analysis-info">
                        <div class="basic-stats">运算: 2次 | 类型转换: 1次</div>
                        <div class="operation-process"><h4>运算过程:</h4><ul class="operation-list"><li class="operation-step"><span class="step-number">1.</span> <code class="operation-code">tmp_0 = index * 0.1f</code><div class="type-info"><span class="left-types">左: float</span><span class="right-types">右: int, float</span></div></li><li class="operation-step"><span class="step-number">2.</span> <code class="operation-code">weight = tmp_0</code><div class="type-info"><span class="left-types">左: float</span><span class="right-types">右: float</span></div></li></ul></div><div class="type-conversions"><h4>类型转换:</h4><ul class="conversion-list"><li class="conversion-item implicit-conversion"><span class="var-name">变量 index:</span> <span class="conversion-types">int → float</span> <span class="conversion-type">(precision_promotion)</span><div class="conversion-reason">原因: 精度提升用于 * 运算</div><div class="conversion-operation">在: tmp_0 = index * 0.1f</div></li></ul></div><div class="variable-types"><h4>变量类型:</h4><ul class="variable-list"><li class="variable-item"><span class="var-name">weight</span>: <span class="var-type">float</span></li><li class="variable-item"><span class="var-name">index</span>: <span class="var-type">int</span></li></ul></div>
                    </div>
                </div>
            </div>
            
            <div class="code-line " data-has-analysis="false" data-performance="normal" data-operations="0" data-conversions="0">
                <div class="line-number">22</div>
                <div class="line-content">
                    <div class="code-text">    </div>
                    <div class="analysis-info">
                        <div class="basic-stats"></div>
                        
                    </div>
                </div>
            </div>
            
            <div class="code-line performance-normal" data-has-analysis="true" data-performance="normal" data-operations="1" data-conversions="0">
                <div class="line-number">23</div>
                <div class="line-content">
                    <div class="code-text">    out.position = clipPos;</div>
                    <div class="analysis-info">
                        <div class="basic-stats">运算: 1次</div>
                        <div class="operation-process"><h4>运算过程:</h4><ul class="operation-list"><li class="operation-step"><span class="step-number">1.</span> <code class="operation-code">out.position = clipPos</code><div class="type-info"><span class="right-types">右: float4</span></div></li></ul></div><div class="variable-types"><h4>变量类型:</h4><ul class="variable-list"><li class="variable-item"><span class="var-name">out.position</span>: <span class="var-type">unknown</span></li><li class="variable-item"><span class="var-name">clipPos</span>: <span class="var-type">float4</span></li></ul></div>
                    </div>
                </div>
            </div>
            
            <div class="code-line type-conversion performance-conversion" data-has-analysis="true" data-performance="conversion" data-operations="2" data-conversions="4">
                <div class="line-number">24</div>
                <div class="line-content">
                    <div class="code-text">    out.color = float4(weight, weight, weight, 1.0f);</div>
                    <div class="analysis-info">
                        <div class="basic-stats">运算: 2次 | 类型转换: 4次</div>
                        <div class="operation-process"><h4>运算过程:</h4><ul class="operation-list"><li class="operation-step"><span class="step-number">1.</span> <code class="operation-code">tmp_0 = float4(weight, weight, weight, 1.0f)</code><div class="type-info"><span class="left-types">左: float4</span><span class="right-types">右: float, float, float, float</span></div></li><li class="operation-step"><span class="step-number">2.</span> <code class="operation-code">out.color = tmp_0</code><div class="type-info"><span class="right-types">右: float4</span></div></li></ul></div><div class="type-conversions"><h4>类型转换:</h4><ul class="conversion-list"><li class="conversion-item implicit-conversion"><span class="var-name">变量 weight:</span> <span class="conversion-types">float → float4</span> <span class="conversion-type">(scalar_broadcast)</span><div class="conversion-reason">原因: 构造函数参数转换为 float4</div><div class="conversion-operation">在: tmp_0 = float4(weight, weight, weight, 1.0f)</div></li><li class="conversion-item implicit-conversion"><span class="var-name">变量 weight:</span> <span class="conversion-types">float → float4</span> <span class="conversion-type">(scalar_broadcast)</span><div class="conversion-reason">原因: 构造函数参数转换为 float4</div><div class="conversion-operation">在: tmp_0 = float4(weight, weight, weight, 1.0f)</div></li><li class="conversion-item implicit-conversion"><span class="var-name">变量 weight:</span> <span class="conversion-types">float → float4</span> <span class="conversion-type">(scalar_broadcast)</span><div class="conversion-reason">原因: 构造函数参数转换为 float4</div><div class="conversion-operation">在: tmp_0 = float4(weight, weight, weight, 1.0f)</div></li><li class="conversion-item implicit-conversion"><span class="var-name">变量 1.0f:</span> <span class="conversion-types">float → float4</span> <span class="conversion-type">(scalar_broadcast)</span><div class="conversion-reason">原因: 构造函数参数转换为 float4</div><div class="conversion-operation">在: tmp_0 = float4(weight, weight, weight, 1.0f)</div></li></ul></div><div class="variable-types"><h4>变量类型:</h4><ul class="variable-list"><li class="variable-item"><span class="var-name">out.color</span>: <span class="var-type">unknown</span></li><li class="variable-item"><span class="var-name">weight</span>: <span class="var-type">float</span></li></ul></div>
                    </div>
                </div>
            </div>
            
            <div class="code-line " data-has-analysis="false" data-performance="normal" data-operations="0" data-conversions="0">
                <div class="line-number">25</div>
                <div class="line-content">
                    <div class="code-text">    </div>
                    <div class="analysis-info">
                        <div class="basic-stats"></div>
                        
                    </div>
                </div>
            </div>
            
            <div class="code-line " data-has-analysis="true" data-performance="normal" data-operations="0" data-conversions="0">
                <div class="line-number">26</div>
                <div class="line-content">
                    <div class="code-text">    return out;</div>
                    <div class="analysis-info">
                        <div class="basic-stats"></div>
                        <div class="variable-types"><h4>变量类型:</h4><ul class="variable-list"><li class="variable-item"><span class="var-name">out</span>: <span class="var-type">unknown</span></li></ul></div>
                    </div>
                </div>
            </div>
            
            <div class="code-line " data-has-analysis="false" data-performance="normal" data-operations="0" data-conversions="0">
                <div class="line-number">27</div>
                <div class="line-content">
                    <div class="code-text">}</div>
                    <div class="analysis-info">
                        <div class="basic-stats"></div>
                        
                    </div>
                </div>
            </div>
            
            <div class="code-line " data-has-analysis="false" data-performance="normal" data-operations="0" data-conversions="0">
                <div class="line-number">28</div>
                <div class="line-content">
                    <div class="code-text"></div>
                    <div class="analysis-info">
                        <div class="basic-stats"></div>
                        
                    </div>
                </div>
            </div>
            
        </div>
    </div>

    <script>
        // JavaScript控制显示选项
        document.addEventListener('DOMContentLoaded', function() {{
            const showNodeDetails = document.getElementById('showNodeDetails');
            const showOperationProcess = document.getElementById('showOperationProcess');
            const showTypeConversions = document.getElementById('showTypeConversions');
            const showVariableTypes = document.getElementById('showVariableTypes');
            const showTypeColors = document.getElementById('showTypeColors');
            const showOnlyAnalyzed = document.getElementById('showOnlyAnalyzed');
            const performanceFilter = document.getElementById('performanceFilter');
            const themeSelector = document.getElementById('themeSelector');
            const filterInfo = document.getElementById('filterInfo');
            const filterText = document.getElementById('filterText');

            // 主题切换功能
            function updateTheme() {{
                const theme = themeSelector.value;
                const body = document.body;
                
                if (theme === 'dark') {{
                    body.classList.add('dark-theme');
                }} else {{
                    body.classList.remove('dark-theme');
                }}
                
                // 保存主题偏好到本地存储
                localStorage.setItem('preferred-theme', theme);
            }}

            // 加载保存的主题偏好
            function loadThemePreference() {{
                const savedTheme = localStorage.getItem('preferred-theme');
                if (savedTheme) {{
                    themeSelector.value = savedTheme;
                    updateTheme();
                }}
            }}

            function updateDisplay() {{
                const codeLines = document.querySelectorAll('.code-line');
                let visibleCount = 0;
                
                codeLines.forEach(line => {{
                    const hasAnalysis = line.dataset.hasAnalysis === 'true';
                    const performance = line.dataset.performance;
                    const operations = parseInt(line.dataset.operations);
                    
                    // 详情显示控制
                    const basicStats = line.querySelector('.basic-stats');
                    if (basicStats) {{
                        basicStats.style.display = showNodeDetails.checked ? 'block' : 'none';
                    }}

                    const operationProcess = line.querySelector('.operation-process');
                    if (operationProcess) {{
                        operationProcess.style.display = showOperationProcess.checked ? 'block' : 'none';
                    }}

                    const typeConversions = line.querySelector('.type-conversions');
                    if (typeConversions) {{
                        typeConversions.style.display = showTypeConversions.checked ? 'block' : 'none';
                    }}

                    const variableTypes = line.querySelector('.variable-types');
                    if (variableTypes) {{
                        variableTypes.style.display = showVariableTypes.checked ? 'block' : 'none';
                    }}
                    
                    // 类型颜色标记控制
                    line.classList.toggle('performance-normal', showTypeColors.checked && performance === 'normal');
                    line.classList.toggle('performance-intensive', showTypeColors.checked && performance === 'intensive');
                    line.classList.toggle('performance-conversion', showTypeColors.checked && performance === 'conversion');
                    line.classList.toggle('performance-issue', showTypeColors.checked && performance === 'issue');
                    
                    // 行过滤控制
                    let shouldShow = true;
                    
                    if (showOnlyAnalyzed.checked && !hasAnalysis) {{
                        shouldShow = false;
                    }}
                    
                    if (performanceFilter.value !== 'all' && performance !== performanceFilter.value) {{
                        shouldShow = false;
                    }}
                    
                    line.style.display = shouldShow ? 'flex' : 'none';
                    if (shouldShow) visibleCount++;
                }});
                
                // 更新过滤信息
                const totalLines = codeLines.length;
                if (visibleCount < totalLines) {{
                    filterInfo.style.display = 'block';
                    filterText.textContent = `显示 ${visibleCount.toString()} / ${totalLines.toString()} 行`;
                }} else {{
                    filterInfo.style.display = 'none';
                }}
            }}

            // 绑定事件监听器
            showNodeDetails.addEventListener('change', updateDisplay);
            showOperationProcess.addEventListener('change', updateDisplay);
            showTypeConversions.addEventListener('change', updateDisplay);
            showVariableTypes.addEventListener('change', updateDisplay);
            showTypeColors.addEventListener('change', updateDisplay);
            showOnlyAnalyzed.addEventListener('change', updateDisplay);
            performanceFilter.addEventListener('change', updateDisplay);
            themeSelector.addEventListener('change', function() {
                updateTheme();
                updateDisplay();
            });
            
            // 初始化显示和主题
            loadThemePreference();
            updateDisplay();
        }});
    </script>
</body>
</html>
