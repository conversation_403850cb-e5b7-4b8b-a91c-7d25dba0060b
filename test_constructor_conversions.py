#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试构造函数参数的精确类型转换检测
"""

from Process.Analysis.syntax_tree_builder import SyntaxTreeAnalyzer

def test_constructor_conversions():
    """测试构造函数参数的类型转换检测"""
    
    analyzer = SyntaxTreeAnalyzer()
    
    # 测试代码 - 模拟真实的着色器代码场景
    test_code = '''
    float4 main() {
        half _9176 = 1.0h;
        half _9199 = 2.0h;
        int _1234 = 42;
        float _5678 = 3.14f;
        
        // 构造函数中的标量广播
        half3 result1 = half3(_9176);           // _9176: half → half (scalar_broadcast)
        float3 result2 = float3(_9176);         // _9176: half → float (precision_promotion)
        float4 result3 = float4(_1234);         // _1234: int → float (precision_promotion)
        
        // 多参数构造函数
        half3 result4 = half3(_9176, _9199, _9176);  // 每个参数的转换
        float3 result5 = float3(_9176, _5678, _1234); // 混合类型参数
        
        // 向量到向量的转换
        half2 vec2 = half2(_9176, _9199);
        float4 result6 = float4(vec2, _5678, _1234);  // vec2: half2 → float4, 其他参数也有转换
        
        return result6;
    }
    '''
    
    print("=== 构造函数参数类型转换检测 ===")
    print("测试代码场景：模拟真实着色器中的变量名和构造函数调用")
    print("\n" + "="*60)
    
    # 分析代码
    result = analyzer.analyze_shader_with_syntax_trees(test_code)
    
    # 输出转换信息
    conversion_count = 0
    constructor_lines = []
    
    for line_analysis in result['line_analyses']:
        if line_analysis.type_conversions:
            line_content = line_analysis.line_content.strip()
            
            # 只关注构造函数相关的行
            if any(keyword in line_content for keyword in ['half3', 'float3', 'float4', 'half2']):
                constructor_lines.append((line_analysis.line_number, line_content, line_analysis.type_conversions))
    
    print("=== 构造函数中的参数转换 ===")
    for line_num, line_content, conversions in constructor_lines:
        print(f"\n第 {line_num} 行: {line_content}")
        
        for conversion in conversions:
            conversion_count += 1
            variable = conversion.get('variable', 'unknown')
            from_type = conversion.get('from_type', 'unknown')
            to_type = conversion.get('to_type', 'unknown')
            conv_type = conversion.get('conversion_type', 'unknown')
            reason = conversion.get('reason', 'unknown')
            
            # 特别标注变量名
            print(f"  🔄 变量 {variable}: {from_type} → {to_type} ({conv_type})")
            if 'scalar_broadcast' in conv_type:
                print(f"     💡 标量广播：{variable} 被广播到向量类型")
            elif 'precision_promotion' in conv_type:
                print(f"     ⬆️  精度提升：{variable} 从 {from_type} 提升到 {to_type}")
            elif 'precision_demotion' in conv_type:
                print(f"     ⬇️  精度降级：{variable} 从 {from_type} 降级到 {to_type}")
    
    # 统计分析
    print(f"\n=== 转换统计 ===")
    print(f"构造函数相关转换: {conversion_count} 次")
    
    # 按变量统计
    variable_conversions = {}
    for _, _, conversions in constructor_lines:
        for conv in conversions:
            var_name = conv.get('variable', 'unknown')
            variable_conversions[var_name] = variable_conversions.get(var_name, 0) + 1
    
    print("\n变量转换频率:")
    for var_name, count in sorted(variable_conversions.items(), key=lambda x: x[1], reverse=True):
        print(f"  {var_name}: {count} 次转换")
    
    # 转换类型统计
    conversion_types = {}
    for _, _, conversions in constructor_lines:
        for conv in conversions:
            conv_type = conv.get('conversion_type', 'unknown')
            conversion_types[conv_type] = conversion_types.get(conv_type, 0) + 1
    
    print("\n转换类型分布:")
    for conv_type, count in sorted(conversion_types.items()):
        print(f"  {conv_type}: {count} 次")
    
    return conversion_count > 0

if __name__ == "__main__":
    success = test_constructor_conversions()
    if success:
        print("\n✅ 构造函数参数转换检测功能正常！")
        print("💡 现在可以精确识别每个参数变量的类型转换")
    else:
        print("\n❌ 未检测到预期的构造函数参数转换")
