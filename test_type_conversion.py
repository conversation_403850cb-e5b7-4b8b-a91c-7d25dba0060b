#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的类型转换检查功能
"""

from Process.Analysis.syntax_tree_builder import SyntaxTreeAnalyzer

def test_type_conversion_detection():
    """测试类型转换检测功能"""
    
    analyzer = SyntaxTreeAnalyzer()
    
    # 测试代码：包含各种类型转换场景
    test_code = '''
    float4 main() {
        // 精度提升：half + float
        half a = 1.0h;
        float b = 2.0f;
        float c = a + b;  // a 会被提升为 float
        
        // 向量运算中的精度提升
        half2 vec_a = half2(1.0h, 2.0h);
        float2 vec_b = float2(3.0f, 4.0f);
        float2 vec_c = vec_a + vec_b;  // vec_a 会被提升为 float2
        
        // 标量与向量运算
        half scalar = 0.5h;
        float3 vector = float3(1.0f, 2.0f, 3.0f);
        float3 result = scalar + vector;  // scalar 会被提升为 float 并广播
        
        // 赋值转换
        float d = a;  // half a 赋值给 float d
        
        return float4(c, vec_c.x, result.x, d);
    }
    '''
    
    print("=== 测试类型转换检测 ===")
    print("测试代码:")
    print(test_code)
    print("\n" + "="*50)
    
    # 分析代码
    result = analyzer.analyze_shader_with_syntax_trees(test_code)
    
    # 统计类型转换
    total_conversions = 0
    conversion_stats = {}
    
    print("\n=== 检测到的类型转换 ===")
    for i, line_analysis in enumerate(result['line_analyses']):
        if line_analysis.type_conversions:
            print(f"\n第 {line_analysis.line_number} 行: {line_analysis.line_content.strip()}")
            for conversion in line_analysis.type_conversions:
                total_conversions += 1
                conversion_type = conversion.get('conversion_type', 'unknown')
                conversion_stats[conversion_type] = conversion_stats.get(conversion_type, 0) + 1
                
                print(f"  变量: {conversion.get('variable', 'unknown')}")
                print(f"  转换: {conversion.get('from_type', 'unknown')} -> {conversion.get('to_type', 'unknown')}")
                print(f"  类型: {conversion_type}")
                print(f"  原因: {conversion.get('reason', 'unknown')}")
                print(f"  运算: {conversion.get('operation', 'unknown')}")
                print()
    
    print(f"\n=== 转换统计 ===")
    print(f"总转换次数: {total_conversions}")
    print("转换类型分布:")
    for conv_type, count in conversion_stats.items():
        print(f"  {conv_type}: {count} 次")
    
    return total_conversions > 0

if __name__ == "__main__":
    success = test_type_conversion_detection()
    if success:
        print("\n✅ 类型转换检测功能正常工作！")
    else:
        print("\n❌ 未检测到预期的类型转换")
