=== 语法树分析日志 ===

第4行: using namespace metal;
语法树结构:
declaration(metal:unknown)
--------------------------------------------------
第8行: float4 AerialPerspectiveExt;
语法树结构:
declaration(AerialPerspectiveExt:float4)
--------------------------------------------------
第9行: float4 AerialPerspectiveMie;
语法树结构:
declaration(AerialPerspectiveMie:float4)
--------------------------------------------------
第10行: float4 AerialPerspectiveRay;
语法树结构:
declaration(AerialPerspectiveRay:float4)
--------------------------------------------------
第11行: float4 AmbientColor;
语法树结构:
declaration(AmbientColor:float4)
--------------------------------------------------
第12行: float4 CSMCacheIndexs;
语法树结构:
declaration(CSMCacheIndexs:float4)
--------------------------------------------------
第13行: float4 CSMShadowBiases;
语法树结构:
declaration(CSMShadowBiases:float4)
--------------------------------------------------
第14行: float4 CameraInfo;
语法树结构:
declaration(CameraInfo:float4)
--------------------------------------------------
第15行: float4 CameraPos;
语法树结构:
declaration(CameraPos:float4)
--------------------------------------------------
第16行: float4 DiyLightingInfo;
语法树结构:
declaration(DiyLightingInfo:float4)
--------------------------------------------------
第17行: float4 EnvInfo;
语法树结构:
declaration(EnvInfo:float4)
--------------------------------------------------
第18行: float4 FogColor;
语法树结构:
declaration(FogColor:float4)
--------------------------------------------------
第19行: float4 FogInfo;
语法树结构:
declaration(FogInfo:float4)
--------------------------------------------------
第20行: float4 GIInfo;
语法树结构:
declaration(GIInfo:float4)
--------------------------------------------------
第21行: float4 HexRenderOptionData[4];
语法树结构:
variable(float4:unknown)
--------------------------------------------------
第22行: float4 LightDataBuffer[65];
语法树结构:
variable(float4:unknown)
--------------------------------------------------
第23行: float3x4 Local;
语法树结构:
declaration(Local:unknown)
--------------------------------------------------
第24行: float4 OriginSunDir;
语法树结构:
declaration(OriginSunDir:float4)
--------------------------------------------------
第25行: float4 PlayerPos;
语法树结构:
declaration(PlayerPos:float4)
--------------------------------------------------
第26行: float4 ReflectionProbeBBMin;
语法树结构:
declaration(ReflectionProbeBBMin:float4)
--------------------------------------------------
第27行: float4 SHAOParam;
语法树结构:
declaration(SHAOParam:float4)
--------------------------------------------------
第28行: float4 SHGIParam;
语法树结构:
declaration(SHGIParam:float4)
--------------------------------------------------
第29行: float4 SHGIParam2;
语法树结构:
declaration(SHGIParam2:float4)
--------------------------------------------------
第30行: float4 ScreenInfo;
语法树结构:
declaration(ScreenInfo:float4)
--------------------------------------------------
第31行: float4 ScreenMotionGray;
语法树结构:
declaration(ScreenMotionGray:float4)
--------------------------------------------------
第32行: float4x4 ShadowViewProjTexs0;
语法树结构:
declaration(ShadowViewProjTexs0:float4x4)
--------------------------------------------------
第33行: float4x4 ShadowViewProjTexs1;
语法树结构:
declaration(ShadowViewProjTexs1:float4x4)
--------------------------------------------------
第34行: float4x4 ShadowViewProjTexs2;
语法树结构:
declaration(ShadowViewProjTexs2:float4x4)
--------------------------------------------------
第35行: float4 SunColor;
语法树结构:
declaration(SunColor:float4)
--------------------------------------------------
第36行: float4 SunDirection;
语法树结构:
declaration(SunDirection:float4)
--------------------------------------------------
第37行: float4 SunFogColor;
语法树结构:
declaration(SunFogColor:float4)
--------------------------------------------------
第38行: float4 TimeOfDayInfos;
语法树结构:
declaration(TimeOfDayInfos:float4)
--------------------------------------------------
第39行: float3x4 World;
语法树结构:
declaration(World:unknown)
--------------------------------------------------
第40行: float4 WorldProbeInfo;
语法树结构:
declaration(WorldProbeInfo:float4)
--------------------------------------------------
第41行: float4 cCIFadeTime;
语法树结构:
declaration(cCIFadeTime:float4)
--------------------------------------------------
第42行: float4 cCISnowData;
语法树结构:
declaration(cCISnowData:float4)
--------------------------------------------------
第43行: float4 cCISwitchData;
语法树结构:
declaration(cCISwitchData:float4)
--------------------------------------------------
第44行: float4 cLocalVirtualLitColor;
语法树结构:
declaration(cLocalVirtualLitColor:float4)
--------------------------------------------------
第45行: float4 cLocalVirtualLitCustom;
语法树结构:
declaration(cLocalVirtualLitCustom:float4)
--------------------------------------------------
第46行: float4 cLocalVirtualLitPos;
语法树结构:
declaration(cLocalVirtualLitPos:float4)
--------------------------------------------------
第47行: float4 cSHCoefficients[7];
语法树结构:
variable(float4:unknown)
--------------------------------------------------
第48行: float4 cShadowBias;
语法树结构:
declaration(cShadowBias:float4)
--------------------------------------------------
第49行: float4 cVirtualLitColor;
语法树结构:
declaration(cVirtualLitColor:float4)
--------------------------------------------------
第50行: float4 cVirtualLitParam;
语法树结构:
declaration(cVirtualLitParam:float4)
--------------------------------------------------
第51行: float4 cVisibilitySH[2];
语法树结构:
variable(float4:unknown)
--------------------------------------------------
第52行: packed_float3 cBaseColor;
语法树结构:
declaration(cBaseColor:unknown)
--------------------------------------------------
第53行: float eIsPlayerOverride;
语法树结构:
declaration(eIsPlayerOverride:float)
--------------------------------------------------
第54行: packed_float3 cCIMudBuff;
语法树结构:
declaration(cCIMudBuff:unknown)
--------------------------------------------------
第55行: float eFresnelPower;
语法树结构:
declaration(eFresnelPower:float)
--------------------------------------------------
第56行: packed_float3 cEmissionColor;
语法树结构:
declaration(cEmissionColor:unknown)
--------------------------------------------------
第57行: float eFresnelMinIntensity;
语法树结构:
declaration(eFresnelMinIntensity:float)
--------------------------------------------------
第58行: packed_float3 eFresnelColor;
语法树结构:
declaration(eFresnelColor:unknown)
--------------------------------------------------
第59行: float eFresnelIntensity;
语法树结构:
declaration(eFresnelIntensity:float)
--------------------------------------------------
第60行: float cAOoffset;
语法树结构:
declaration(cAOoffset:float)
--------------------------------------------------
第61行: float cBiasFarAwayShadow;
语法树结构:
declaration(cBiasFarAwayShadow:float)
--------------------------------------------------
第62行: float cEmissionScale;
语法树结构:
declaration(cEmissionScale:float)
--------------------------------------------------
第63行: float cFurFadeInt;
语法树结构:
declaration(cFurFadeInt:float)
--------------------------------------------------
第64行: float cMicroShadow;
语法树结构:
declaration(cMicroShadow:float)
--------------------------------------------------
第65行: float cNoise1Scale;
语法树结构:
declaration(cNoise1Scale:float)
--------------------------------------------------
第66行: float cNoise2Bias;
语法树结构:
declaration(cNoise2Bias:float)
--------------------------------------------------
第67行: float cNoise2Scale;
语法树结构:
declaration(cNoise2Scale:float)
--------------------------------------------------
第68行: float cNormalMapStrength;
语法树结构:
declaration(cNormalMapStrength:float)
--------------------------------------------------
第69行: float cSaturation;
语法树结构:
declaration(cSaturation:float)
--------------------------------------------------
第70行: float eDynamicFresnelIntensity;
语法树结构:
declaration(eDynamicFresnelIntensity:float)
--------------------------------------------------
第71行: float eFresnelAlphaAdd;
语法树结构:
declaration(eFresnelAlphaAdd:float)
--------------------------------------------------
第74行: constant half3 _18526 = {};
语法树结构:
assignment(=:half3)
  declaration(_18526:half3)
  literal({}:unknown)
--------------------------------------------------
第75行: constant float3 _19185 = {};
语法树结构:
assignment(=:float3)
  declaration(_19185:float3)
  literal({}:unknown)
--------------------------------------------------
第76行: constant half _19493 = {};
语法树结构:
assignment(=:half)
  declaration(_19493:half)
  literal({}:unknown)
--------------------------------------------------
第77行: constant float _19585 = {};
语法树结构:
assignment(=:float)
  declaration(_19585:float)
  literal({}:unknown)
--------------------------------------------------
第78行: constant int _19621 = {};
语法树结构:
assignment(=:int)
  declaration(_19621:int)
  literal({}:unknown)
--------------------------------------------------
第79行: constant float3 _21234 = {};
语法树结构:
assignment(=:float3)
  declaration(_21234:float3)
  literal({}:unknown)
--------------------------------------------------
第80行: constant float3 _21295 = {};
语法树结构:
assignment(=:float3)
  declaration(_21295:float3)
  literal({}:unknown)
--------------------------------------------------
第81行: constant half4 _21296 = {};
语法树结构:
assignment(=:half4)
  declaration(_21296:half4)
  literal({}:unknown)
--------------------------------------------------
第82行: constant half3 _21297 = {};
语法树结构:
assignment(=:half3)
  declaration(_21297:half3)
  literal({}:unknown)
--------------------------------------------------
第86行: float4 _Ret [[color(0)]];
语法树结构:
declaration(_Ret:float4)
--------------------------------------------------
第91行: float4 IN_TexCoord [[user(locn0)]];
语法树结构:
declaration(IN_TexCoord:float4)
--------------------------------------------------
第92行: float4 IN_WorldPosition [[user(locn1)]];
语法树结构:
declaration(IN_WorldPosition:float4)
--------------------------------------------------
第93行: half4 IN_WorldNormal [[user(locn2)]];
语法树结构:
declaration(IN_WorldNormal:half4)
--------------------------------------------------
第94行: half4 IN_WorldTangent [[user(locn3)]];
语法树结构:
declaration(IN_WorldTangent:half4)
--------------------------------------------------
第95行: half4 IN_WorldBinormal [[user(locn4)]];
语法树结构:
declaration(IN_WorldBinormal:half4)
--------------------------------------------------
第96行: half4 IN_TintColor [[user(locn5)]];
语法树结构:
declaration(IN_TintColor:half4)
--------------------------------------------------
第97行: float IN_LinearZ [[user(locn6)]];
语法树结构:
declaration(IN_LinearZ:float)
--------------------------------------------------
第98行: half3 IN_LocalPosition [[user(locn7)]];
语法树结构:
declaration(IN_LocalPosition:half3)
--------------------------------------------------
第99行: half4 IN_StaticWorldNormal [[user(locn8)]];
语法树结构:
declaration(IN_StaticWorldNormal:half4)
--------------------------------------------------
第105行: constexpr sampler sShadowMapArraySamplerSmplr(filter::linear, mip_filter::linear, compare_func::greater);
语法树结构:
declaration(sampler:unknown)
--------------------------------------------------
第106行: main0_out out = {};
语法树结构:
assignment(=:custom_struct)
  declaration(out:custom_struct)
  literal({}:unknown)
--------------------------------------------------
第107行: half _9176 = half(0);
语法树结构:
assignment(=:half)
  declaration(_9176:half)
  function(half:unknown)
    literal(0:int)
--------------------------------------------------
第108行: half3 _9179 = half3(_9176);
语法树结构:
assignment(=:half3)
  declaration(_9179:half3)
  function(half3:unknown)
    variable(_9176:half)
--------------------------------------------------
第109行: half _9199 = half(1);
语法树结构:
assignment(=:half)
  declaration(_9199:half)
  function(half:unknown)
    literal(1:int)
--------------------------------------------------
第110行: float3 _8921 = fast::normalize(_Block1.CameraPos.xyz - in.IN_WorldPosition.xyz);
语法树结构:
assignment(=:float3)
  declaration(_8921:float3)
  function(fast::normalize:unknown)
    operator(-:unknown)
      member_access(_Block1.CameraPos.xyz:float3)
      member_access(in.IN_WorldPosition.xyz:float3)
--------------------------------------------------
第111行: float3 _8925 = float3(in.IN_WorldNormal.xyz);
语法树结构:
assignment(=:float3)
  declaration(_8925:float3)
  function(float3:unknown)
    member_access(in.IN_WorldNormal.xyz:half3)
--------------------------------------------------
第112行: half _8929 = half(fast::clamp(dot(_8925, _8921), 0.0, 1.0));
语法树结构:
assignment(=:half)
  declaration(_8929:half)
  function(half:unknown)
    function(fast::clamp:unknown)
      function(dot:unknown)
        variable(_8925:float3)
        variable(_8921:float3)
      literal(0.0:float)
      literal(1.0:float)
--------------------------------------------------
第113行: half4 _8939 = sBaseSampler.sample(sBaseSamplerSmplr, in.IN_TexCoord.xy);
语法树结构:
assignment(=:half4)
  declaration(_8939:half4)
  function(sBaseSampler.sample:unknown)
    variable(sBaseSamplerSmplr:sampler)
    member_access(in.IN_TexCoord.xy:float2)
--------------------------------------------------
第114行: half3 _8941 = _8939.xyz;
语法树结构:
assignment(=:half3)
  declaration(_8941:half3)
  member_access(_8939.xyz:half3)
--------------------------------------------------
第115行: half3 _8949 = half3(float3(_8941 * _8941) * float3(_Block1.cBaseColor));
语法树结构:
assignment(=:half3)
  declaration(_8949:half3)
  function(half3:unknown)
    operator(*:unknown)
      function(float3:unknown)
        operator(*:unknown)
          variable(_8941:half3)
          variable(_8941:half3)
      function(float3:unknown)
        member_access(_Block1.cBaseColor:float3)
--------------------------------------------------
第116行: float4 _8973 = sNoiseSampler.sample(sNoiseSamplerSmplr, (in.IN_TexCoord.xy * _Block1.cNoise1Scale));
语法树结构:
assignment(=:float4)
  declaration(_8973:float4)
  function(sNoiseSampler.sample:unknown)
    variable(sNoiseSamplerSmplr:sampler)
    operator(*:unknown)
      member_access(in.IN_TexCoord.xy:float2)
      member_access(_Block1.cNoise1Scale:float)
--------------------------------------------------
第117行: half4 _8974 = half4(_8973);
语法树结构:
assignment(=:half4)
  declaration(_8974:half4)
  function(half4:unknown)
    variable(_8973:float4)
--------------------------------------------------
第118行: float4 _8982 = sNoiseSampler.sample(sNoiseSamplerSmplr, (in.IN_TexCoord.xy * _Block1.cNoise2Scale));
语法树结构:
assignment(=:float4)
  declaration(_8982:float4)
  function(sNoiseSampler.sample:unknown)
    variable(sNoiseSamplerSmplr:sampler)
    operator(*:unknown)
      member_access(in.IN_TexCoord.xy:float2)
      member_access(_Block1.cNoise2Scale:float)
--------------------------------------------------
第119行: half4 _8983 = half4(_8982);
语法树结构:
assignment(=:half4)
  declaration(_8983:half4)
  function(half4:unknown)
    variable(_8982:float4)
--------------------------------------------------
第120行: float _8991 = fast::clamp(powr(float(max(in.IN_TintColor.w, half(9.9956989288330078125e-05))), _Block1.cNoise2Bias), 0.0, 1.0);
语法树结构:
assignment(=:float)
  declaration(_8991:float)
  function(fast::clamp:unknown)
    function(powr:unknown)
      function(float:unknown)
        function(max:unknown)
          member_access(in.IN_TintColor.w:half)
          function(half:unknown)
            literal(9.9956989288330078125:float)
--------------------------------------------------
第121行: half _8994 = _8974.x;
语法树结构:
assignment(=:half)
  declaration(_8994:half)
  member_access(_8974.x:half)
--------------------------------------------------
第122行: half _8996 = _8983.x;
语法树结构:
assignment(=:half)
  declaration(_8996:half)
  member_access(_8983.x:half)
--------------------------------------------------
第123行: float _9014 = 1.0 - _8991;
语法树结构:
assignment(=:float)
  declaration(_9014:float)
  operator(-:unknown)
    literal(1.0:float)
    variable(_8991:float)
--------------------------------------------------
第124行: half _9019 = half(1.0);
语法树结构:
assignment(=:half)
  declaration(_9019:half)
  function(half:unknown)
    literal(1.0:float)
--------------------------------------------------
第125行: if ((float((_8994 * _8996) - half(mix(0.0, 0.5, powr(float(max(_9199 - _8929, half(9.9956989288330078125e-05))), 3.0)))) - 0.100000001490116119384765625) < 0.0)
语法树结构:
function(float:unknown)
  operator(*:unknown)
    variable(_8994:half)
    variable(_8996:half)
--------------------------------------------------
第127行: discard_fragment();
语法树结构:
function(discard_fragment:unknown)
--------------------------------------------------
第129行: half4 _9248 = sNormalSampler.sample(sNormalSamplerSmplr, in.IN_TexCoord.xy);
语法树结构:
assignment(=:half4)
  declaration(_9248:half4)
  function(sNormalSampler.sample:unknown)
    variable(sNormalSamplerSmplr:sampler)
    member_access(in.IN_TexCoord.xy:float2)
--------------------------------------------------
第130行: half _9251 = half(2);
语法树结构:
assignment(=:half)
  declaration(_9251:half)
  function(half:unknown)
    literal(2:int)
--------------------------------------------------
第131行: half3 _9254 = half3(_9199);
语法树结构:
assignment(=:half3)
  declaration(_9254:half3)
  function(half3:unknown)
    variable(_9199:half)
--------------------------------------------------
第132行: half3 _9255 = (_9248.xyz * _9251) - _9254;
语法树结构:
assignment(=:half3)
  declaration(_9255:half3)
  operator(-:unknown)
    operator(*:unknown)
      member_access(_9248.xyz:half3)
      variable(_9251:half)
    variable(_9254:half3)
--------------------------------------------------
第133行: half _9257 = _9255.x;
语法树结构:
assignment(=:half)
  declaration(_9257:half)
  member_access(_9255.x:half)
--------------------------------------------------
第134行: half _9263 = _9255.y;
语法树结构:
assignment(=:half)
  declaration(_9263:half)
  member_access(_9255.y:half)
--------------------------------------------------
第135行: half _9270 = _9255.z;
语法树结构:
assignment(=:half)
  declaration(_9270:half)
  member_access(_9255.z:half)
--------------------------------------------------
第136行: float3 _9279 = float3(((in.IN_WorldTangent.xyz * _9257) + (in.IN_WorldBinormal.xyz * _9263)) + (in.IN_WorldNormal.xyz * _9270));
语法树结构:
assignment(=:float3)
  declaration(_9279:float3)
  function(float3:unknown)
    operator(+:unknown)
      operator(+:unknown)
        operator(*:unknown)
          member_access(in.IN_WorldTangent.xyz:half3)
          variable(_9257:half)
        operator(*:unknown)
          member_access(in.IN_WorldBinormal.xyz:half3)
          variable(_9263:half)
      operator(*:unknown)
        member_access(in.IN_WorldNormal.xyz:half3)
        variable(_9270:half)
--------------------------------------------------
第137行: float3 _9286 = float3(in.IN_StaticWorldNormal.xyz);
语法树结构:
assignment(=:float3)
  declaration(_9286:float3)
  function(float3:unknown)
    member_access(in.IN_StaticWorldNormal.xyz:half3)
--------------------------------------------------
第138行: float3 _9331 = (((float4(float3(in.IN_WorldTangent.xyz), 0.0) * _Block1.Local) * float(_9257)) + ((float4(float3(in.IN_WorldBinormal.xyz), 0.0) * _Block1.Local) * float(_9263))) + (_9286 * float(_9270));
语法树结构:
assignment(=:float3)
  declaration(_9331:float3)
  operator(+:unknown)
    operator(+:unknown)
      operator(*:unknown)
        operator(*:unknown)
          function(float4:unknown)
            function(float3:unknown)
              member_access(in.IN_WorldTangent.xyz:half3)
            literal(0.0:float)
          member_access(_Block1.Local:float3x4)
        function(float:unknown)
          variable(_9257:half)
      operator(*:unknown)
        operator(*:unknown)
          function(float4:unknown)
            function(float3:unknown)
              member_access(in.IN_WorldBinormal.xyz:half3)
            literal(0.0:float)
          member_access(_Block1.Local:float3x4)
        function(float:unknown)
          variable(_9263:half)
    operator(*:unknown)
      variable(_9286:float3)
      function(float:unknown)
        variable(_9270:half)
--------------------------------------------------
第139行: half _9334 = half((_9331 * rsqrt(fast::max(dot(_9331, _9331), 9.9999997473787516355514526367188e-06))).y);
语法树结构:
assignment(=:half)
  declaration(_9334:half)
  function(half:unknown)
    operator(*:unknown)
      variable(_9331:float3)
      function(rsqrt:unknown)
        function(fast::max:unknown)
          function(dot:unknown)
            variable(_9331:float3)
            variable(_9331:float3)
          literal(9.9999997473787516355514526367188:float)
--------------------------------------------------
第140行: half3 _9064 = mix(in.IN_WorldNormal.xyz, half3(_9279 * rsqrt(fast::max(dot(_9279, _9279), 9.9999997473787516355514526367188e-06))), half3(half(_Block1.cNormalMapStrength)));
语法树结构:
assignment(=:half3)
  declaration(_9064:half3)
  function(mix:unknown)
    member_access(in.IN_WorldNormal.xyz:half3)
    function(half3:unknown)
      operator(*:unknown)
        variable(_9279:float3)
        function(rsqrt:unknown)
          function(fast::max:unknown)
            function(dot:unknown)
              variable(_9279:float3)
              variable(_9279:float3)
            literal(9.9999997473787516355514526367188:float)
--------------------------------------------------
第141行: half4 _9074 = sMixSampler.sample(sMixSamplerSmplr, in.IN_TexCoord.xy);
语法树结构:
assignment(=:half4)
  declaration(_9074:half4)
  function(sMixSampler.sample:unknown)
    variable(sMixSamplerSmplr:sampler)
    member_access(in.IN_TexCoord.xy:float2)
--------------------------------------------------
第142行: half _9079 = _9074.y;
语法树结构:
assignment(=:half)
  declaration(_9079:half)
  member_access(_9074.y:half)
--------------------------------------------------
第143行: half _9096 = half(mix(1.0, float(mix(half3(_9019), half3(half(fast::clamp(_9014 + _Block1.cAOoffset, 0.0, 1.0))), in.IN_TintColor.xxx).x * _9074.z), float(_8929)));
语法树结构:
assignment(=:half)
  declaration(_9096:half)
  function(half:unknown)
    function(mix:unknown)
      literal(1.0:float)
      function(float:unknown)
        operator(*:unknown)
          member_access(result_mix.x:unknown)
            function(mix:unknown)
              function(half3:unknown)
                variable(_9019:half)
              function(half3:unknown)
                function(half:unknown)
                  function(fast::clamp:unknown)
                    operator(+:unknown)
                      variable(_9014:float)
                      member_access(_Block1.cAOoffset:float)
                    literal(0.0:float)
                    literal(1.0:float)
              member_access(in.IN_TintColor.xxx:half3)
          member_access(_9074.z:half)
      function(float:unknown)
        variable(_8929:half)
--------------------------------------------------
第144行: float _9100 = float(_9096);
语法树结构:
assignment(=:float)
  declaration(_9100:float)
  function(float:unknown)
    variable(_9096:half)
--------------------------------------------------
第145行: float3 _9109 = float3(_9064);
语法树结构:
assignment(=:float3)
  declaration(_9109:float3)
  function(float3:unknown)
    variable(_9064:half3)
--------------------------------------------------
第146行: half4 _9130 = sEmissionMapSampler.sample(sEmissionMapSamplerSmplr, in.IN_TexCoord.xy);
语法树结构:
assignment(=:half4)
  declaration(_9130:half4)
  function(sEmissionMapSampler.sample:unknown)
    variable(sEmissionMapSamplerSmplr:sampler)
    member_access(in.IN_TexCoord.xy:float2)
--------------------------------------------------
第147行: half3 _9145 = _9130.xyz * half3((float3(_Block1.cEmissionColor) * _Block1.cEmissionScale) * float3(_8949));
语法树结构:
assignment(=:half3)
  declaration(_9145:half3)
  operator(*:unknown)
    member_access(_9130.xyz:half3)
    function(half3:unknown)
      operator(*:unknown)
        operator(*:unknown)
          function(float3:unknown)
            member_access(_Block1.cEmissionColor:float3)
          member_access(_Block1.cEmissionScale:float)
        function(float3:unknown)
          variable(_8949:half3)
--------------------------------------------------
第148行: half _18217;
语法树结构:
declaration(_18217:half)
--------------------------------------------------
第149行: if (!gl_FrontFacing)
语法树结构:
operator(!:unknown)
  variable(gl_FrontFacing:bool)
--------------------------------------------------
第151行: _18217 = _9334 * half(-1);
语法树结构:
assignment(=:half)
  variable(_18217:half)
  operator(*:unknown)
    variable(_9334:half)
    function(half:unknown)
      operator(-:unknown)
        literal(1:int)
--------------------------------------------------
第155行: _18217 = _9334;
语法树结构:
assignment(=:half)
  variable(_18217:half)
  variable(_9334:half)
--------------------------------------------------
第157行: float3 _9698 = float3(_9179);
语法树结构:
assignment(=:float3)
  declaration(_9698:float3)
  function(float3:unknown)
    variable(_9179:half3)
--------------------------------------------------
第158行: float _9408 = mix(_Block1.cCIFadeTime.y, _Block1.cCIFadeTime.w, fast::clamp((_Block1.CameraPos.w - _Block1.cCIFadeTime.x) * _Block1.cCIFadeTime.z, 0.0, 1.0));
语法树结构:
assignment(=:float)
  declaration(_9408:float)
  function(mix:unknown)
    member_access(_Block1.cCIFadeTime.y:float)
    member_access(_Block1.cCIFadeTime.w:float)
    function(fast::clamp:unknown)
      operator(*:unknown)
        operator(-:unknown)
          member_access(_Block1.CameraPos.w:float)
          member_access(_Block1.cCIFadeTime.x:float)
        member_access(_Block1.cCIFadeTime.z:float)
      literal(0.0:float)
      literal(1.0:float)
--------------------------------------------------
第159行: float _9721 = fast::clamp(dot(_9286, float3(half3(_9176, _9176, _9199))), 0.0, 1.0);
语法树结构:
assignment(=:float)
  declaration(_9721:float)
  function(fast::clamp:unknown)
    function(dot:unknown)
      variable(_9286:float3)
      function(float3:unknown)
        function(half3:unknown)
          variable(_9176:half)
          variable(_9176:half)
          variable(_9199:half)
    literal(0.0:float)
    literal(1.0:float)
--------------------------------------------------
第160行: float _9734 = (_9721 * _9721) * float(half(fast::clamp((_Block1.cCIMudBuff[2u] - 1.0) * 0.699999988079071044921875, 0.0, 1.0)));
语法树结构:
assignment(=:float)
  declaration(_9734:float)
  operator(*:unknown)
    operator(*:unknown)
      variable(_9721:float)
      variable(_9721:float)
    function(float:unknown)
      function(half:unknown)
        function(fast::clamp:unknown)
          operator(*:unknown)
            operator(-:unknown)
              member_access(_Block1.cCIMudBuff[2u]:float3)
              literal(1.0:float)
            literal(0.699999988079071044921875:float)
          literal(0.0:float)
          literal(1.0:float)
--------------------------------------------------
第161行: half3 _9761 = mix(_8949, half3(half(0.20700000226497650146484375), half(0.18400000035762786865234375), half(0.1369999945163726806640625)), half3(half(_9734 * _9408)));
语法树结构:
assignment(=:half3)
  declaration(_9761:half3)
  function(mix:unknown)
    variable(_8949:half3)
    function(half3:unknown)
      function(half:unknown)
        literal(0.20700000226497650146484375:float)
      function(half:unknown)
        literal(0.18400000035762786865234375:float)
      function(half:unknown)
        literal(0.1369999945163726806640625:float)
    function(half3:unknown)
      function(half:unknown)
        operator(*:unknown)
          variable(_9734:float)
          variable(_9408:float)
--------------------------------------------------
第162行: half _9772 = half(mix(float(_9199 - _9074.x), 0.89999997615814208984375, _9734 * float(half(mix(_9408 - (0.5 * _Block1.cCIFadeTime.x), _9408, _Block1.cCIFadeTime.y)))));
语法树结构:
assignment(=:half)
  declaration(_9772:half)
  function(half:unknown)
    function(mix:unknown)
      function(float:unknown)
        operator(-:unknown)
          variable(_9199:half)
          member_access(_9074.x:half)
      literal(0.89999997615814208984375:float)
      operator(*:unknown)
        variable(_9734:float)
        function(float:unknown)
          function(half:unknown)
            function(mix:unknown)
              operator(-:unknown)
                variable(_9408:float)
                operator(*:unknown)
                  literal(0.5:float)
                  member_access(_Block1.cCIFadeTime.x:float)
              variable(_9408:float)
              member_access(_Block1.cCIFadeTime.y:float)
--------------------------------------------------
第163行: float _9429 = (float(half(_9014)) * _9408) * fast::clamp(1.0 + _Block1.EnvInfo.y, 0.0, 1.0);
语法树结构:
assignment(=:float)
  declaration(_9429:float)
  operator(*:unknown)
    operator(*:unknown)
      function(float:unknown)
        function(half:unknown)
          variable(_9014:float)
      variable(_9408:float)
    function(fast::clamp:unknown)
      operator(+:unknown)
        literal(1.0:float)
        member_access(_Block1.EnvInfo.y:float)
      literal(0.0:float)
      literal(1.0:float)
--------------------------------------------------
第164行: float4 _9443 = sCharInteractionSampler.sample(sCharInteractionSamplerSmplr, ((in.IN_TexCoord.xy * _Block1.cCISnowData.x) * 12.0));
语法树结构:
assignment(=:float4)
  declaration(_9443:float4)
  function(sCharInteractionSampler.sample:unknown)
    variable(sCharInteractionSamplerSmplr:sampler)
    operator(*:unknown)
      operator(*:unknown)
        member_access(in.IN_TexCoord.xy:float2)
        member_access(_Block1.cCISnowData.x:float)
      literal(12.0:float)
--------------------------------------------------
第165行: half3 _18225;
语法树结构:
declaration(_18225:half3)
--------------------------------------------------
第166行: half _18234;
语法树结构:
declaration(_18234:half)
--------------------------------------------------
第167行: half _18236;
语法树结构:
declaration(_18236:half)
--------------------------------------------------
第168行: half3 _18268;
语法树结构:
declaration(_18268:half3)
--------------------------------------------------
第169行: half _18272;
语法树结构:
declaration(_18272:half)
--------------------------------------------------
第170行: if (_Block1.cCISwitchData.x > 0.0)
语法树结构:
operator(>:unknown)
  member_access(_Block1.cCISwitchData.x:float)
  literal(0.0:float)
--------------------------------------------------
第172行: float _9460 = float(half(fast::max(0.0, _Block1.EnvInfo.y)));
语法树结构:
assignment(=:float)
  declaration(_9460:float)
  function(float:unknown)
    function(half:unknown)
      function(fast::max:unknown)
        literal(0.0:float)
        member_access(_Block1.EnvInfo.y:float)
--------------------------------------------------
第173行: float _9462 = fast::min(2.0, fast::clamp(_Block1.cCISnowData.z, 0.0, 1.0) + _9460);
语法树结构:
assignment(=:float)
  declaration(_9462:float)
  function(fast::min:unknown)
    literal(2.0:float)
    operator(+:unknown)
      function(fast::clamp:unknown)
        member_access(_Block1.cCISnowData.z:float)
        literal(0.0:float)
        literal(1.0:float)
      variable(_9460:float)
--------------------------------------------------
第174行: float _9499 = 1.0 - _9429;
语法树结构:
assignment(=:float)
  declaration(_9499:float)
  operator(-:unknown)
    literal(1.0:float)
    variable(_9429:float)
--------------------------------------------------
第175行: float _9505 = _9443.y;
语法树结构:
assignment(=:float)
  declaration(_9505:float)
  member_access(_9443.y:float)
--------------------------------------------------
第176行: float _9510 = float(half(fast::clamp(((float2(0.800000011920928955078125, 0.5) * _9429).x * (1.0 - powr(float(half(powr(float(clamp(_18217, half(0.0), half(1.0))) + fast::clamp(0.20000000298023223876953125 - fast::clamp((-1.0) * _Block1.cCISnowData.z, 0.0, 1.0), 0.0, 1.0), 2.0 - _9462) * float(in.IN_StaticWorldNormal.w))), 0.800000011920928955078125 - (_9462 * 0.4000000059604644775390625)))) + ((_9499 * _9499) * _9499), 0.0, 1.0)));
语法树结构:
assignment(=:float)
  declaration(_9510:float)
  function(float:unknown)
    function(half:unknown)
      function(fast::clamp:unknown)
        operator(+:unknown)
          operator(*:unknown)
            member_access(*.x:unknown)
              operator(*:unknown)
                function(float2:unknown)
                  literal(0.800000011920928955078125:float)
                  literal(0.5:float)
                variable(_9429:float)
            operator(-:unknown)
              literal(1.0:float)
              function(powr:unknown)
                function(float:unknown)
                  function(half:unknown)
                    operator(*:unknown)
                      function(powr:unknown)
                        operator(+:unknown)
                          function(float:unknown)
                            function(clamp:unknown)
                              variable(_18217:half)
                              function(half:unknown)
                                literal(0.0:float)
                              function(half:unknown)
                                literal(1.0:float)
                          function(fast::clamp:unknown)
                            operator(-:unknown)
                              literal(0.20000000298023223876953125:float)
                              function(fast::clamp:unknown)
                                operator(*:unknown)
                                  operator(-:unknown)
                                    literal(1.0:float)
                                  member_access(_Block1.cCISnowData.z:float)
                                literal(0.0:float)
                                literal(1.0:float)
                            literal(0.0:float)
                            literal(1.0:float)
                        operator(-:unknown)
                          literal(2.0:float)
                          variable(_9462:float)
                      function(float:unknown)
                        member_access(in.IN_StaticWorldNormal.w:half)
                operator(-:unknown)
                  literal(0.800000011920928955078125:float)
                  operator(*:unknown)
                    variable(_9462:float)
                    literal(0.4000000059604644775390625:float)
          operator(*:unknown)
            operator(*:unknown)
              variable(_9499:float)
              variable(_9499:float)
            variable(_9499:float)
        literal(0.0:float)
        literal(1.0:float)
--------------------------------------------------
第177行: float _9519 = float(half(9.9956989288330078125e-05));
语法树结构:
assignment(=:float)
  declaration(_9519:float)
  function(float:unknown)
    function(half:unknown)
      literal(9.9956989288330078125:float)
--------------------------------------------------
第178行: half _9535 = half(1.0 - fast::clamp((float(in.IN_LocalPosition.y) * (2.0 - _9460)) * _Block1.cCISnowData.y, 0.0, 1.0));
语法树结构:
assignment(=:half)
  declaration(_9535:half)
  function(half:unknown)
    operator(-:unknown)
      literal(1.0:float)
      function(fast::clamp:unknown)
        operator(*:unknown)
          operator(*:unknown)
            function(float:unknown)
              member_access(in.IN_LocalPosition.y:half)
            operator(-:unknown)
              literal(2.0:float)
              variable(_9460:float)
          member_access(_Block1.cCISnowData.y:float)
        literal(0.0:float)
        literal(1.0:float)
--------------------------------------------------
第179行: float _9556 = float(half((float(_9535) * fast::clamp(float(_9535 + _18217) + 0.5, 0.0, 1.0)) * _9429));
语法树结构:
assignment(=:float)
  declaration(_9556:float)
  function(float:unknown)
    function(half:unknown)
      operator(*:unknown)
        operator(*:unknown)
          function(float:unknown)
            variable(_9535:half)
          function(fast::clamp:unknown)
            operator(+:unknown)
              function(float:unknown)
                operator(+:unknown)
                  variable(_9535:half)
                  variable(_18217:half)
              literal(0.5:float)
            literal(0.0:float)
            literal(1.0:float)
        variable(_9429:float)
--------------------------------------------------
第180行: float _9557 = 1.0 - _9556;
语法树结构:
assignment(=:float)
  declaration(_9557:float)
  operator(-:unknown)
    literal(1.0:float)
    variable(_9556:float)
--------------------------------------------------
第181行: half _9585 = half((_Block1.cCISwitchData.x * fast::clamp(1.0 - _Block1.cCISnowData.w, 0.0, 1.0)) * float(max(half(fast::clamp((fast::max(_9505, _9443.w) - _9510) / fast::max(_9519, fast::clamp(_9510 + 0.1500000059604644775390625, 0.0, 1.0) - _9510), 0.0, 1.0)), half(fast::clamp((fast::max(_9505, _9443.z) - _9557) / fast::max(_9519, (1.5 - _9556) - _9557), 0.0, 1.0)))));
语法树结构:
assignment(=:half)
  declaration(_9585:half)
  function(half:unknown)
    operator(*:unknown)
      operator(*:unknown)
        member_access(_Block1.cCISwitchData.x:float)
        function(fast::clamp:unknown)
          operator(-:unknown)
            literal(1.0:float)
            member_access(_Block1.cCISnowData.w:float)
          literal(0.0:float)
          literal(1.0:float)
      function(float:unknown)
        function(max:unknown)
          function(half:unknown)
            function(fast::clamp:unknown)
              operator(/:unknown)
                operator(-:unknown)
                  function(fast::max:unknown)
                    variable(_9505:float)
                    member_access(_9443.w:float)
                  variable(_9510:float)
                function(fast::max:unknown)
                  variable(_9519:float)
                  operator(-:unknown)
                    function(fast::clamp:unknown)
                      operator(+:unknown)
                        variable(_9510:float)
                        literal(0.1500000059604644775390625:float)
                      literal(0.0:float)
                      literal(1.0:float)
                    variable(_9510:float)
              literal(0.0:float)
              literal(1.0:float)
          function(half:unknown)
            function(fast::clamp:unknown)
              operator(/:unknown)
                operator(-:unknown)
                  function(fast::max:unknown)
                    variable(_9505:float)
                    member_access(_9443.z:float)
                  variable(_9557:float)
                function(fast::max:unknown)
                  variable(_9519:float)
                  operator(-:unknown)
                    operator(-:unknown)
                      literal(1.5:float)
                      variable(_9556:float)
                    variable(_9557:float)
              literal(0.0:float)
              literal(1.0:float)
--------------------------------------------------
第182行: half _9588 = _9199 - _9585;
语法树结构:
assignment(=:half)
  declaration(_9588:half)
  operator(-:unknown)
    variable(_9199:half)
    variable(_9585:half)
--------------------------------------------------
第183行: float _9603 = float(_9585);
语法树结构:
assignment(=:float)
  declaration(_9603:float)
  function(float:unknown)
    variable(_9585:half)
--------------------------------------------------
第184行: _18272 = half(mix(float(_9772), 1.0, _9603));
语法树结构:
assignment(=:half)
  variable(_18272:half)
  function(half:unknown)
    function(mix:unknown)
      function(float:unknown)
        variable(_9772:half)
      literal(1.0:float)
      variable(_9603:float)
--------------------------------------------------
第185行: _18268 = _9145 * _9588;
语法树结构:
assignment(=:half3)
  variable(_18268:half3)
  operator(*:unknown)
    variable(_9145:half3)
    variable(_9588:half)
--------------------------------------------------
第186行: _18236 = half(mix(_9100, 1.0, _9603));
语法树结构:
assignment(=:half)
  variable(_18236:half)
  function(half:unknown)
    function(mix:unknown)
      variable(_9100:float)
      literal(1.0:float)
      variable(_9603:float)
--------------------------------------------------
第187行: _18234 = _9079 * _9588;
语法树结构:
assignment(=:half)
  variable(_18234:half)
  operator(*:unknown)
    variable(_9079:half)
    variable(_9588:half)
--------------------------------------------------
第188行: _18225 = mix(_9761, half3(half(0.61000001430511474609375), half(0.660000026226043701171875), half(0.790000021457672119140625)), half3(_9585));
语法树结构:
assignment(=:half3)
  variable(_18225:half3)
  function(mix:unknown)
    variable(_9761:half3)
    function(half3:unknown)
      function(half:unknown)
        literal(0.61000001430511474609375:float)
      function(half:unknown)
        literal(0.660000026226043701171875:float)
      function(half:unknown)
        literal(0.790000021457672119140625:float)
    function(half3:unknown)
      variable(_9585:half)
--------------------------------------------------
第192行: _18272 = _9772;
语法树结构:
assignment(=:half)
  variable(_18272:half)
  variable(_9772:half)
--------------------------------------------------
第193行: _18268 = _9145;
语法树结构:
assignment(=:half3)
  variable(_18268:half3)
  variable(_9145:half3)
--------------------------------------------------
第194行: _18236 = _9096;
语法树结构:
assignment(=:half)
  variable(_18236:half)
  variable(_9096:half)
--------------------------------------------------
第195行: _18234 = _9079;
语法树结构:
assignment(=:half)
  variable(_18234:half)
  variable(_9079:half)
--------------------------------------------------
第196行: _18225 = _9761;
语法树结构:
assignment(=:half3)
  variable(_18225:half3)
  variable(_9761:half3)
--------------------------------------------------
第198行: half _8295 = half(fast::clamp(1.0 - _Block1.HexRenderOptionData[0].x, 0.0, 1.0));
语法树结构:
assignment(=:half)
  declaration(_8295:half)
  function(half:unknown)
    function(fast::clamp:unknown)
      operator(-:unknown)
        literal(1.0:float)
        member_access(_Block1.HexRenderOptionData[0].x:float)
      literal(0.0:float)
      literal(1.0:float)
--------------------------------------------------
第199行: float _8298 = float(_8939.w * half((_8991 * float(min(_8994, _8996))) * powr(float(max(in.IN_TintColor.x, half(9.9956989288330078125e-05))), _Block1.cFurFadeInt)));
语法树结构:
assignment(=:float)
  declaration(_8298:float)
  function(float:unknown)
    operator(*:unknown)
      member_access(_8939.w:half)
      function(half:unknown)
        operator(*:unknown)
          operator(*:unknown)
            variable(_8991:float)
            function(float:unknown)
              function(min:unknown)
                variable(_8994:half)
                variable(_8996:half)
          function(powr:unknown)
            function(float:unknown)
              function(max:unknown)
                member_access(in.IN_TintColor.x:half)
                function(half:unknown)
                  literal(9.9956989288330078125:float)
--------------------------------------------------
第200行: half3 _8303 = half3(half(0.2125999927520751953125), half(0.715200006961822509765625), half(0.072200000286102294921875));
语法树结构:
assignment(=:half3)
  declaration(_8303:half3)
  function(half3:unknown)
    function(half:unknown)
      literal(0.2125999927520751953125:float)
    function(half:unknown)
      literal(0.715200006961822509765625:float)
    function(half:unknown)
      literal(0.072200000286102294921875:float)
--------------------------------------------------
第201行: half3 _8315 = mix(half3(dot(_18225, _8303)), _18225, half3(half(_Block1.cSaturation)));
语法树结构:
assignment(=:half3)
  declaration(_8315:half3)
  function(mix:unknown)
    function(half3:unknown)
      function(dot:unknown)
        variable(_18225:half3)
        variable(_8303:half3)
    variable(_18225:half3)
    function(half3:unknown)
      function(half:unknown)
        member_access(_Block1.cSaturation:float)
--------------------------------------------------
第202行: half _9787 = half(_8298);
语法树结构:
assignment(=:half)
  declaration(_9787:half)
  function(half:unknown)
    variable(_8298:float)
--------------------------------------------------
第203行: half _18230;
语法树结构:
declaration(_18230:half)
--------------------------------------------------
第204行: half3 _18255;
语法树结构:
declaration(_18255:half3)
--------------------------------------------------
第205行: if (_Block1.eDynamicFresnelIntensity > 0.0)
语法树结构:
operator(>:unknown)
  member_access(_Block1.eDynamicFresnelIntensity:float)
  literal(0.0:float)
--------------------------------------------------
第207行: float _9806 = abs(dot(_9109, -_8921));
语法树结构:
assignment(=:float)
  declaration(_9806:float)
  function(abs:unknown)
    function(dot:unknown)
      variable(_9109:float3)
      operator(-:unknown)
        variable(_8921:float3)
--------------------------------------------------
第208行: float _9813 = abs(_Block1.eFresnelPower);
语法树结构:
assignment(=:float)
  declaration(_9813:float)
  function(abs:unknown)
    member_access(_Block1.eFresnelPower:float)
--------------------------------------------------
第209行: float _9831 = fast::max(_Block1.eFresnelMinIntensity, (_Block1.eFresnelPower < 0.0) ? powr(_9806, _9813) : powr(1.0 - fast::min(float(_9199 - half(9.9956989288330078125e-05)), _9806), _9813));
语法树结构:
assignment(=:float)
  declaration(_9831:float)
  function(fast::max:unknown)
    member_access(_Block1.eFresnelMinIntensity:float)
    operator(?::unknown)
      operator(<:unknown)
        member_access(_Block1.eFresnelPower:float)
        literal(0.0:float)
      function(powr:unknown)
        variable(_9806:float)
        variable(_9813:float)
      function(powr:unknown)
        operator(-:unknown)
          literal(1.0:float)
          function(fast::min:unknown)
            function(float:unknown)
              operator(-:unknown)
                variable(_9199:half)
                function(half:unknown)
                  literal(9.9956989288330078125:float)
--------------------------------------------------
第210行: float _9846 = float(_9787 * half(_9831));
语法树结构:
assignment(=:float)
  declaration(_9846:float)
  function(float:unknown)
    operator(*:unknown)
      variable(_9787:half)
      function(half:unknown)
        variable(_9831:float)
--------------------------------------------------
第211行: _18255 = _9179 + half3((((float3(_Block1.eFresnelColor) * _9831) * _Block1.eFresnelIntensity) * 1.0) * _9846);
语法树结构:
assignment(=:half3)
  variable(_18255:half3)
  operator(+:unknown)
    variable(_9179:half3)
    function(half3:unknown)
      operator(*:unknown)
        operator(*:unknown)
          operator(*:unknown)
            operator(*:unknown)
              function(float3:unknown)
                member_access(_Block1.eFresnelColor:float3)
              variable(_9831:float)
            member_access(_Block1.eFresnelIntensity:float)
          literal(1.0:float)
        variable(_9846:float)
--------------------------------------------------
第212行: _18230 = clamp(half(fast::max(fast::clamp(_Block1.eFresnelAlphaAdd, 0.0, 1.0) * _8298, _9846)), half(0.0), half(1.0));
语法树结构:
assignment(=:half)
  variable(_18230:half)
  function(clamp:unknown)
    function(half:unknown)
      function(fast::max:unknown)
        operator(*:unknown)
          function(fast::clamp:unknown)
            member_access(_Block1.eFresnelAlphaAdd:float)
            literal(0.0:float)
            literal(1.0:float)
          variable(_8298:float)
        variable(_9846:float)
    function(half:unknown)
      literal(0.0:float)
    function(half:unknown)
      literal(1.0:float)
--------------------------------------------------
第216行: _18255 = _9179;
语法树结构:
assignment(=:half3)
  variable(_18255:half3)
  variable(_9179:half3)
--------------------------------------------------
第217行: _18230 = _9787;
语法树结构:
assignment(=:half)
  variable(_18230:half)
  variable(_9787:half)
--------------------------------------------------
第219行: float _9868 = float(_18230);
语法树结构:
assignment(=:float)
  declaration(_9868:float)
  function(float:unknown)
    variable(_18230:half)
--------------------------------------------------
第220行: half _8346 = _18236 * half(_Block1.SHAOParam.w);
语法树结构:
assignment(=:half)
  declaration(_8346:half)
  operator(*:unknown)
    variable(_18236:half)
    function(half:unknown)
      member_access(_Block1.SHAOParam.w:float)
--------------------------------------------------
第221行: float4 _9926 = float4((in.IN_WorldPosition.xyz + (_8925 * _Block1.cBiasFarAwayShadow)) - _Block1.CameraPos.xyz, 1.0) * _Block1.ShadowViewProjTexs0;
语法树结构:
assignment(=:float4)
  declaration(_9926:float4)
  operator(*:unknown)
    function(float4:unknown)
      operator(-:unknown)
        operator(+:unknown)
          member_access(in.IN_WorldPosition.xyz:float3)
          operator(*:unknown)
            variable(_8925:float3)
            member_access(_Block1.cBiasFarAwayShadow:float)
        member_access(_Block1.CameraPos.xyz:float3)
      literal(1.0:float)
    member_access(_Block1.ShadowViewProjTexs0:float4x4)
--------------------------------------------------
第222行: float4 _17899 = _9926;
语法树结构:
assignment(=:float4)
  declaration(_17899:float4)
  variable(_9926:float4)
--------------------------------------------------
第223行: _17899.z = _9926.z - _Block1.CSMShadowBiases.x;
语法树结构:
assignment(=:unknown)
  member_access(_17899.z:unknown)
  operator(-:unknown)
    member_access(_9926.z:float)
    member_access(_Block1.CSMShadowBiases.x:float)
--------------------------------------------------
第224行: float4 _9942 = float4(in.IN_WorldPosition.xyz, 1.0);
语法树结构:
assignment(=:float4)
  declaration(_9942:float4)
  function(float4:unknown)
    member_access(in.IN_WorldPosition.xyz:float3)
    literal(1.0:float)
--------------------------------------------------
第225行: float4 _9945 = _9942 * _Block1.ShadowViewProjTexs1;
语法树结构:
assignment(=:float4)
  declaration(_9945:float4)
  operator(*:unknown)
    variable(_9942:float4)
    member_access(_Block1.ShadowViewProjTexs1:float4x4)
--------------------------------------------------
第226行: float4 _17902 = _9945;
语法树结构:
assignment(=:float4)
  declaration(_17902:float4)
  variable(_9945:float4)
--------------------------------------------------
第227行: _17902.z = _9945.z - _Block1.CSMShadowBiases.y;
语法树结构:
assignment(=:unknown)
  member_access(_17902.z:unknown)
  operator(-:unknown)
    member_access(_9945.z:float)
    member_access(_Block1.CSMShadowBiases.y:float)
--------------------------------------------------
第228行: float4 _18237;
语法树结构:
declaration(_18237:float4)
--------------------------------------------------
第229行: if (_Block1.CSMCacheIndexs.z > 0.0)
语法树结构:
operator(>:unknown)
  member_access(_Block1.CSMCacheIndexs.z:float)
  literal(0.0:float)
--------------------------------------------------
第231行: float4 _9971 = _9942 * _Block1.ShadowViewProjTexs2;
语法树结构:
assignment(=:float4)
  declaration(_9971:float4)
  operator(*:unknown)
    variable(_9942:float4)
    member_access(_Block1.ShadowViewProjTexs2:float4x4)
--------------------------------------------------
第232行: _9971.z = _9971.z - _Block1.CSMShadowBiases.z;
语法树结构:
assignment(=:unknown)
  member_access(_9971.z:unknown)
  operator(-:unknown)
    member_access(_9971.z:float)
    member_access(_Block1.CSMShadowBiases.z:float)
--------------------------------------------------
第233行: _18237 = _9971;
语法树结构:
assignment(=:float4)
  variable(_18237:float4)
  variable(_9971:float4)
--------------------------------------------------
第237行: _18237 = float4(0.0, 0.0, 0.0, 1.0);
语法树结构:
assignment(=:float4)
  variable(_18237:float4)
  function(float4:unknown)
    literal(0.0:float)
    literal(0.0:float)
    literal(0.0:float)
    literal(1.0:float)
--------------------------------------------------
第239行: float3 _10033 = _17902.xyz / float3(_9945.w);
语法树结构:
assignment(=:float3)
  declaration(_10033:float3)
  operator(/:unknown)
    member_access(_17902.xyz:float3)
    function(float3:unknown)
      member_access(_9945.w:float)
--------------------------------------------------
第240行: float3 _10040 = _18237.xyz / float3(_18237.w);
语法树结构:
assignment(=:float3)
  declaration(_10040:float3)
  operator(/:unknown)
    member_access(_18237.xyz:float3)
    function(float3:unknown)
      member_access(_18237.w:float)
--------------------------------------------------
第241行: float3 _10077 = _10040 * (step(-0.100000001490116119384765625, _Block1.CSMShadowBiases.z) * float(all(_10040 > float3(0.0)) && all(_10040 < float3(1.0))));
语法树结构:
assignment(=:float3)
  declaration(_10077:float3)
  operator(*:unknown)
    variable(_10040:float3)
    operator(*:unknown)
      function(step:unknown)
        operator(-:unknown)
          literal(0.100000001490116119384765625:float)
        member_access(_Block1.CSMShadowBiases.z:float)
      function(float:unknown)
        operator(&&:unknown)
          function(all:unknown)
            operator(>:unknown)
              variable(_10040:float3)
              function(float3:unknown)
                literal(0.0:float)
          function(all:unknown)
            operator(<:unknown)
              variable(_10040:float3)
              function(float3:unknown)
                literal(1.0:float)
--------------------------------------------------
第242行: float _21135 = step(-0.100000001490116119384765625, _Block1.CSMShadowBiases.y) * float(all(_10033 > float3(0.0)) && all(_10033 < float3(1.0)));
语法树结构:
assignment(=:float)
  declaration(_21135:float)
  operator(*:unknown)
    function(step:unknown)
      operator(-:unknown)
        literal(0.100000001490116119384765625:float)
      member_access(_Block1.CSMShadowBiases.y:float)
    function(float:unknown)
      operator(&&:unknown)
        function(all:unknown)
          operator(>:unknown)
            variable(_10033:float3)
            function(float3:unknown)
              literal(0.0:float)
        function(all:unknown)
          operator(<:unknown)
            variable(_10033:float3)
            function(float3:unknown)
              literal(1.0:float)
--------------------------------------------------
第243行: float3 _21138 = _10077 + ((_10033 - _10077) * _21135);
语法树结构:
assignment(=:float3)
  declaration(_21138:float3)
  operator(+:unknown)
    variable(_10077:float3)
    operator(*:unknown)
      operator(-:unknown)
        variable(_10033:float3)
        variable(_10077:float3)
      variable(_21135:float)
--------------------------------------------------
第244行: float _10113 = _21138.z;
语法树结构:
assignment(=:float)
  declaration(_10113:float)
  member_access(_21138.z:float)
--------------------------------------------------
第245行: float2 _10120 = float2(_Block1.cShadowBias.w);
语法树结构:
assignment(=:float2)
  declaration(_10120:float2)
  function(float2:unknown)
    member_access(_Block1.cShadowBias.w:float)
--------------------------------------------------
第246行: float2 _10167 = (_21138.xy / _10120) - float2(0.5);
语法树结构:
assignment(=:float2)
  declaration(_10167:float2)
  operator(-:unknown)
    operator(/:unknown)
      member_access(_21138.xy:float2)
      variable(_10120:float2)
    function(float2:unknown)
      literal(0.5:float)
--------------------------------------------------
第247行: float2 _10169 = fract(_10167);
语法树结构:
assignment(=:float2)
  declaration(_10169:float2)
  function(fract:unknown)
    variable(_10167:float2)
--------------------------------------------------
第248行: float2 _10171 = floor(_10167);
语法树结构:
assignment(=:float2)
  declaration(_10171:float2)
  function(floor:unknown)
    variable(_10167:float2)
--------------------------------------------------
第249行: float2 _10177 = float2(2.0) - _10169;
语法树结构:
assignment(=:float2)
  declaration(_10177:float2)
  operator(-:unknown)
    function(float2:unknown)
      literal(2.0:float)
    variable(_10169:float2)
--------------------------------------------------
第250行: float2 _10181 = _10169 + float2(1.0);
语法树结构:
assignment(=:float2)
  declaration(_10181:float2)
  operator(+:unknown)
    variable(_10169:float2)
    function(float2:unknown)
      literal(1.0:float)
--------------------------------------------------
第251行: float2 _10184 = float2(1.0) / _10177;
语法树结构:
assignment(=:float2)
  declaration(_10184:float2)
  operator(/:unknown)
    function(float2:unknown)
      literal(1.0:float)
    variable(_10177:float2)
--------------------------------------------------
第252行: float2 _10187 = _10169 / _10181;
语法树结构:
assignment(=:float2)
  declaration(_10187:float2)
  operator(/:unknown)
    variable(_10169:float2)
    variable(_10181:float2)
--------------------------------------------------
第253行: float _10205 = float(int(_Block1.CSMCacheIndexs[int(2.0 + ((-1.0) * _21135))]));
语法树结构:
assignment(=:float)
  declaration(_10205:float)
  function(float:unknown)
    function(int:unknown)
      member_access(_Block1.CSMCacheIndexs[int(2.0 + ((-1.0) * _21135))]:unknown)
--------------------------------------------------
第254行: float3 _10208 = float3(((_10171 + float2(-0.5)) + _10184) * _10120, _10205);
语法树结构:
assignment(=:float3)
  declaration(_10208:float3)
  function(float3:unknown)
    operator(*:unknown)
      operator(+:unknown)
        operator(+:unknown)
          variable(_10171:float2)
          function(float2:unknown)
            operator(-:unknown)
              literal(0.5:float)
        variable(_10184:float2)
      variable(_10120:float2)
    variable(_10205:float)
--------------------------------------------------
第255行: float3 _10231 = float3(((_10171 + float2(1.5, -0.5)) + float2(_10187.x, _10184.y)) * _10120, _10205);
语法树结构:
assignment(=:float3)
  declaration(_10231:float3)
  function(float3:unknown)
    operator(*:unknown)
      operator(+:unknown)
        operator(+:unknown)
          variable(_10171:float2)
          function(float2:unknown)
            literal(1.5:float)
            operator(-:unknown)
              literal(0.5:float)
        function(float2:unknown)
          member_access(_10187.x:float)
          member_access(_10184.y:float)
      variable(_10120:float2)
    variable(_10205:float)
--------------------------------------------------
第256行: float3 _10254 = float3(((_10171 + float2(-0.5, 1.5)) + float2(_10184.x, _10187.y)) * _10120, _10205);
语法树结构:
assignment(=:float3)
  declaration(_10254:float3)
  function(float3:unknown)
    operator(*:unknown)
      operator(+:unknown)
        operator(+:unknown)
          variable(_10171:float2)
          function(float2:unknown)
            operator(-:unknown)
              literal(0.5:float)
            literal(1.5:float)
        function(float2:unknown)
          member_access(_10184.x:float)
          member_access(_10187.y:float)
      variable(_10120:float2)
    variable(_10205:float)
--------------------------------------------------
第257行: float3 _10276 = float3(((_10171 + float2(1.5)) + _10187) * _10120, _10205);
语法树结构:
assignment(=:float3)
  declaration(_10276:float3)
  function(float3:unknown)
    operator(*:unknown)
      operator(+:unknown)
        operator(+:unknown)
          variable(_10171:float2)
          function(float2:unknown)
            literal(1.5:float)
        variable(_10187:float2)
      variable(_10120:float2)
    variable(_10205:float)
--------------------------------------------------
第258行: float _10282 = _10177.x;
语法树结构:
assignment(=:float)
  declaration(_10282:float)
  member_access(_10177.x:float)
--------------------------------------------------
第259行: float _10289 = _10181.x;
语法树结构:
assignment(=:float)
  declaration(_10289:float)
  member_access(_10181.x:float)
--------------------------------------------------
第260行: float _10300 = _10181.y;
语法树结构:
assignment(=:float)
  declaration(_10300:float)
  member_access(_10181.y:float)
--------------------------------------------------
第261行: float3 _9997 = _17899.xyz / float3(_9926.w);
语法树结构:
assignment(=:float3)
  declaration(_9997:float3)
  operator(/:unknown)
    member_access(_17899.xyz:float3)
    function(float3:unknown)
      member_access(_9926.w:float)
--------------------------------------------------
第262行: float _10004 = fast::min(1.0 - float(half(10) * half(9.9956989288330078125e-05)), _9997.z);
语法树结构:
assignment(=:float)
  declaration(_10004:float)
  function(fast::min:unknown)
    operator(-:unknown)
      literal(1.0:float)
      function(float:unknown)
        operator(*:unknown)
          function(half:unknown)
            literal(10:int)
          function(half:unknown)
            literal(9.9956989288330078125:float)
--------------------------------------------------
第263行: float3 _17928 = _9997;
语法树结构:
assignment(=:float3)
  declaration(_17928:float3)
  variable(_9997:float3)
--------------------------------------------------
第264行: _17928.z = _10004;
语法树结构:
assignment(=:unknown)
  member_access(_17928.z:unknown)
  variable(_10004:float)
--------------------------------------------------
第265行: float2 _10378 = (_17928.xy / _10120) - float2(0.5);
语法树结构:
assignment(=:float2)
  declaration(_10378:float2)
  operator(-:unknown)
    operator(/:unknown)
      member_access(_17928.xy:float2)
      variable(_10120:float2)
    function(float2:unknown)
      literal(0.5:float)
--------------------------------------------------
第266行: float2 _10380 = fract(_10378);
语法树结构:
assignment(=:float2)
  declaration(_10380:float2)
  function(fract:unknown)
    variable(_10378:float2)
--------------------------------------------------
第267行: float2 _10382 = floor(_10378);
语法树结构:
assignment(=:float2)
  declaration(_10382:float2)
  function(floor:unknown)
    variable(_10378:float2)
--------------------------------------------------
第268行: float2 _10388 = float2(2.0) - _10380;
语法树结构:
assignment(=:float2)
  declaration(_10388:float2)
  operator(-:unknown)
    function(float2:unknown)
      literal(2.0:float)
    variable(_10380:float2)
--------------------------------------------------
第269行: float2 _10392 = _10380 + float2(1.0);
语法树结构:
assignment(=:float2)
  declaration(_10392:float2)
  operator(+:unknown)
    variable(_10380:float2)
    function(float2:unknown)
      literal(1.0:float)
--------------------------------------------------
第270行: float2 _10395 = float2(1.0) / _10388;
语法树结构:
assignment(=:float2)
  declaration(_10395:float2)
  operator(/:unknown)
    function(float2:unknown)
      literal(1.0:float)
    variable(_10388:float2)
--------------------------------------------------
第271行: float2 _10398 = _10380 / _10392;
语法树结构:
assignment(=:float2)
  declaration(_10398:float2)
  operator(/:unknown)
    variable(_10380:float2)
    variable(_10392:float2)
--------------------------------------------------
第272行: float _10416 = float(int(_Block1.CSMCacheIndexs.x));
语法树结构:
assignment(=:float)
  declaration(_10416:float)
  function(float:unknown)
    function(int:unknown)
      member_access(_Block1.CSMCacheIndexs.x:float)
--------------------------------------------------
第273行: float3 _10419 = float3(((_10382 + float2(-0.5)) + _10395) * _10120, _10416);
语法树结构:
assignment(=:float3)
  declaration(_10419:float3)
  function(float3:unknown)
    operator(*:unknown)
      operator(+:unknown)
        operator(+:unknown)
          variable(_10382:float2)
          function(float2:unknown)
            operator(-:unknown)
              literal(0.5:float)
        variable(_10395:float2)
      variable(_10120:float2)
    variable(_10416:float)
--------------------------------------------------
第274行: float3 _10442 = float3(((_10382 + float2(1.5, -0.5)) + float2(_10398.x, _10395.y)) * _10120, _10416);
语法树结构:
assignment(=:float3)
  declaration(_10442:float3)
  function(float3:unknown)
    operator(*:unknown)
      operator(+:unknown)
        operator(+:unknown)
          variable(_10382:float2)
          function(float2:unknown)
            literal(1.5:float)
            operator(-:unknown)
              literal(0.5:float)
        function(float2:unknown)
          member_access(_10398.x:float)
          member_access(_10395.y:float)
      variable(_10120:float2)
    variable(_10416:float)
--------------------------------------------------
第275行: float3 _10465 = float3(((_10382 + float2(-0.5, 1.5)) + float2(_10395.x, _10398.y)) * _10120, _10416);
语法树结构:
assignment(=:float3)
  declaration(_10465:float3)
  function(float3:unknown)
    operator(*:unknown)
      operator(+:unknown)
        operator(+:unknown)
          variable(_10382:float2)
          function(float2:unknown)
            operator(-:unknown)
              literal(0.5:float)
            literal(1.5:float)
        function(float2:unknown)
          member_access(_10395.x:float)
          member_access(_10398.y:float)
      variable(_10120:float2)
    variable(_10416:float)
--------------------------------------------------
第276行: float3 _10487 = float3(((_10382 + float2(1.5)) + _10398) * _10120, _10416);
语法树结构:
assignment(=:float3)
  declaration(_10487:float3)
  function(float3:unknown)
    operator(*:unknown)
      operator(+:unknown)
        operator(+:unknown)
          variable(_10382:float2)
          function(float2:unknown)
            literal(1.5:float)
        variable(_10398:float2)
      variable(_10120:float2)
    variable(_10416:float)
--------------------------------------------------
第277行: float _10493 = _10388.x;
语法树结构:
assignment(=:float)
  declaration(_10493:float)
  member_access(_10388.x:float)
--------------------------------------------------
第278行: float _10500 = _10392.x;
语法树结构:
assignment(=:float)
  declaration(_10500:float)
  member_access(_10392.x:float)
--------------------------------------------------
第279行: float _10511 = _10392.y;
语法树结构:
assignment(=:float)
  declaration(_10511:float)
  member_access(_10392.y:float)
--------------------------------------------------
第280行: half _8351 = max(half(mix(0.0, 1.0 - fast::clamp((abs(dot(_9109, _Block1.SunDirection.xyz)) + ((2.0 * _9100) * _9100)) - 1.0, 0.0, 1.0), _Block1.cMicroShadow)), max(half(((((_10388.y * ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10419.xy, uint(rint(_10419.z)), _10004, level(0.0)) * _10493) + (sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10442.xy, uint(rint(_10442.z)), _10004, level(0.0)) * _10500))) + ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10465.xy, uint(rint(_10465.z)), _10004, level(0.0)) * _10493) * _10511)) + ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10487.xy, uint(rint(_10487.z)), _10004, level(0.0)) * _10500) * _10511)) * 0.111111097037792205810546875) * float(all(_17928 > float3(0.0)) && all(_17928 < float3(1.0)))), half(fast::min(1.0, float(half(((((_10177.y * ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10208.xy, uint(rint(_10208.z)), _10113, level(0.0)) * _10282) + (sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10231.xy, uint(rint(_10231.z)), _10113, level(0.0)) * _10289))) + ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10254.xy, uint(rint(_10254.z)), _10113, level(0.0)) * _10282) * _10300)) + ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10276.xy, uint(rint(_10276.z)), _10113, level(0.0)) * _10289) * _10300)) * 0.111111097037792205810546875) * float(all(_21138 > float3(0.0)) && all(_21138 < float3(1.0)))))))));
语法树结构:
assignment(=:half)
  declaration(_8351:half)
  function(max:unknown)
    function(half:unknown)
      function(mix:unknown)
        literal(0.0:float)
        operator(-:unknown)
          literal(1.0:float)
          function(fast::clamp:unknown)
            operator(-:unknown)
              operator(+:unknown)
                function(abs:unknown)
                  function(dot:unknown)
                    variable(_9109:float3)
                    member_access(_Block1.SunDirection.xyz:float3)
                operator(*:unknown)
                  operator(*:unknown)
                    literal(2.0:float)
                    variable(_9100:float)
                  variable(_9100:float)
              literal(1.0:float)
            literal(0.0:float)
            literal(1.0:float)
        member_access(_Block1.cMicroShadow:float)
    function(max:unknown)
      function(half:unknown)
        operator(*:unknown)
          operator(*:unknown)
            operator(+:unknown)
              operator(+:unknown)
                operator(*:unknown)
                  member_access(_10388.y:float)
                  operator(+:unknown)
                    operator(*:unknown)
                      function(sShadowMapArraySampler.sample_compare:unknown)
                        variable(sShadowMapArraySamplerSmplr:sampler)
                        member_access(_10419.xy:float2)
                        function(uint:unknown)
                          function(rint:unknown)
                            member_access(_10419.z:float)
                        variable(_10004:float)
                        function(level:unknown)
                          literal(0.0:float)
                      variable(_10493:float)
                    operator(*:unknown)
                      function(sShadowMapArraySampler.sample_compare:unknown)
                        variable(sShadowMapArraySamplerSmplr:sampler)
                        member_access(_10442.xy:float2)
                        function(uint:unknown)
                          function(rint:unknown)
                            member_access(_10442.z:float)
                        variable(_10004:float)
                        function(level:unknown)
                          literal(0.0:float)
                      variable(_10500:float)
                operator(*:unknown)
                  operator(*:unknown)
                    function(sShadowMapArraySampler.sample_compare:unknown)
                      variable(sShadowMapArraySamplerSmplr:sampler)
                      member_access(_10465.xy:float2)
                      function(uint:unknown)
                        function(rint:unknown)
                          member_access(_10465.z:float)
                      variable(_10004:float)
                      function(level:unknown)
                        literal(0.0:float)
                    variable(_10493:float)
                  variable(_10511:float)
              operator(*:unknown)
                operator(*:unknown)
                  function(sShadowMapArraySampler.sample_compare:unknown)
                    variable(sShadowMapArraySamplerSmplr:sampler)
                    member_access(_10487.xy:float2)
                    function(uint:unknown)
                      function(rint:unknown)
                        member_access(_10487.z:float)
                    variable(_10004:float)
                    function(level:unknown)
                      literal(0.0:float)
                  variable(_10500:float)
                variable(_10511:float)
            literal(0.111111097037792205810546875:float)
          function(float:unknown)
            operator(&&:unknown)
              function(all:unknown)
                operator(>:unknown)
                  variable(_17928:float3)
                  function(float3:unknown)
                    literal(0.0:float)
              function(all:unknown)
                operator(<:unknown)
                  variable(_17928:float3)
                  function(float3:unknown)
                    literal(1.0:float)
      function(half:unknown)
        function(fast::min:unknown)
          literal(1.0:float)
          function(float:unknown)
            function(half:unknown)
              operator(*:unknown)
                operator(*:unknown)
                  operator(+:unknown)
                    operator(+:unknown)
                      operator(*:unknown)
                        member_access(_10177.y:float)
                        operator(+:unknown)
                          operator(*:unknown)
                            function(sShadowMapArraySampler.sample_compare:unknown)
                              variable(sShadowMapArraySamplerSmplr:sampler)
                              member_access(_10208.xy:float2)
                              function(uint:unknown)
                                function(rint:unknown)
                                  member_access(_10208.z:float)
                              variable(_10113:float)
                              function(level:unknown)
                                literal(0.0:float)
                            variable(_10282:float)
                          operator(*:unknown)
                            function(sShadowMapArraySampler.sample_compare:unknown)
                              variable(sShadowMapArraySamplerSmplr:sampler)
                              member_access(_10231.xy:float2)
                              function(uint:unknown)
                                function(rint:unknown)
                                  member_access(_10231.z:float)
                              variable(_10113:float)
                              function(level:unknown)
                                literal(0.0:float)
                            variable(_10289:float)
                      operator(*:unknown)
                        operator(*:unknown)
                          function(sShadowMapArraySampler.sample_compare:unknown)
                            variable(sShadowMapArraySamplerSmplr:sampler)
                            member_access(_10254.xy:float2)
                            function(uint:unknown)
                              function(rint:unknown)
                                member_access(_10254.z:float)
                            variable(_10113:float)
                            function(level:unknown)
                              literal(0.0:float)
                          variable(_10282:float)
                        variable(_10300:float)
                    operator(*:unknown)
                      operator(*:unknown)
                        function(sShadowMapArraySampler.sample_compare:unknown)
                          variable(sShadowMapArraySamplerSmplr:sampler)
                          member_access(_10276.xy:float2)
                          function(uint:unknown)
                            function(rint:unknown)
                              member_access(_10276.z:float)
                          variable(_10113:float)
                          function(level:unknown)
                            literal(0.0:float)
                        variable(_10289:float)
                      variable(_10300:float)
                  literal(0.111111097037792205810546875:float)
                function(float:unknown)
                  operator(&&:unknown)
                    function(all:unknown)
                      operator(>:unknown)
                        variable(_21138:float3)
                        function(float3:unknown)
                          literal(0.0:float)
                    function(all:unknown)
                      operator(<:unknown)
                        variable(_21138:float3)
                        function(float3:unknown)
                          literal(1.0:float)
--------------------------------------------------
第281行: float3 _8370 = in.IN_WorldPosition.xyz - _Block1.CameraPos.xyz;
语法树结构:
assignment(=:float3)
  declaration(_8370:float3)
  operator(-:unknown)
    member_access(in.IN_WorldPosition.xyz:float3)
    member_access(_Block1.CameraPos.xyz:float3)
--------------------------------------------------
第282行: float3 _8373 = fast::normalize(-_8370);
语法树结构:
assignment(=:float3)
  declaration(_8373:float3)
  function(fast::normalize:unknown)
    operator(-:unknown)
      variable(_8370:float3)
--------------------------------------------------
第283行: float _8378 = dot(_9109, _8373);
语法树结构:
assignment(=:float)
  declaration(_8378:float)
  function(dot:unknown)
    variable(_9109:float3)
    variable(_8373:float3)
--------------------------------------------------
第284行: half3 _10557 = mix(half3(half(0.039999999105930328369140625)), _8315, half3(_18234));
语法树结构:
assignment(=:half3)
  declaration(_10557:half3)
  function(mix:unknown)
    function(half3:unknown)
      function(half:unknown)
        literal(0.039999999105930328369140625:float)
    variable(_8315:half3)
    function(half3:unknown)
      variable(_18234:half)
--------------------------------------------------
第285行: half3 _10569 = half3(float3(_8315 - (_8315 * _18234)) * float3(0.3183098733425140380859375));
语法树结构:
assignment(=:half3)
  declaration(_10569:half3)
  function(half3:unknown)
    operator(*:unknown)
      function(float3:unknown)
        operator(-:unknown)
          variable(_8315:half3)
          operator(*:unknown)
            variable(_8315:half3)
            variable(_18234:half)
      function(float3:unknown)
        literal(0.3183098733425140380859375:float)
--------------------------------------------------
第286行: float3 _10588 = float3(_Block1.EnvInfo.z);
语法树结构:
assignment(=:float3)
  declaration(_10588:float3)
  function(float3:unknown)
    member_access(_Block1.EnvInfo.z:float)
--------------------------------------------------
第287行: half3 _8393 = half3(half(0.0));
语法树结构:
assignment(=:half3)
  declaration(_8393:half3)
  function(half3:unknown)
    function(half:unknown)
      literal(0.0:float)
--------------------------------------------------
第288行: uint _8397 = as_type<uint>(_Block1.SHGIParam.w);
语法树结构:
assignment(=:uint)
  declaration(_8397:uint)
  function(as_type<uint>:unknown)
    member_access(_Block1.SHGIParam.w:float)
--------------------------------------------------
第289行: bool _8401 = (_8397 & 63u) > 0u;
语法树结构:
assignment(=:bool)
  declaration(_8401:bool)
  operator(>:unknown)
    operator(&:unknown)
      variable(_8397:uint)
      literal(63u:int)
    literal(0u:int)
--------------------------------------------------
第290行: half _18329;
语法树结构:
declaration(_18329:half)
--------------------------------------------------
第291行: if (_8401)
语法树结构:
variable(_8401:bool)
--------------------------------------------------
第293行: float3 _8435 = select(_Block1.PlayerPos.xyz, _Block1.CameraPos.xyz, bool3((_8397 & 524288u) > 0u));
语法树结构:
assignment(=:float3)
  declaration(_8435:float3)
  function(select:unknown)
    member_access(_Block1.PlayerPos.xyz:float3)
    member_access(_Block1.CameraPos.xyz:float3)
    function(bool3:unknown)
      operator(>:unknown)
        operator(&:unknown)
          variable(_8397:uint)
          literal(524288u:int)
        literal(0u:int)
--------------------------------------------------
第294行: half _18306;
语法树结构:
declaration(_18306:half)
--------------------------------------------------
第295行: if (_8401)
语法树结构:
variable(_8401:bool)
--------------------------------------------------
第297行: half _18304;
语法树结构:
declaration(_18304:half)
--------------------------------------------------
第298行: if ((_8397 & 8u) != 0u)
语法树结构:
operator(&:unknown)
  variable(_8397:uint)
  literal(8u:int)
--------------------------------------------------
第300行: float3 _10686 = (in.IN_WorldPosition.xyz + (_8373 * 0.100000001490116119384765625)) * float3(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125);
语法树结构:
assignment(=:float3)
  declaration(_10686:float3)
  operator(*:unknown)
    operator(+:unknown)
      member_access(in.IN_WorldPosition.xyz:float3)
      operator(*:unknown)
        variable(_8373:float3)
        literal(0.100000001490116119384765625:float)
    function(float3:unknown)
      literal(0.0041666668839752674102783203125:float)
      literal(0.0078125:float)
      literal(0.0041666668839752674102783203125:float)
--------------------------------------------------
第301行: float3 _10762 = _10686 - floor(_10686);
语法树结构:
assignment(=:float3)
  declaration(_10762:float3)
  operator(-:unknown)
    variable(_10686:float3)
    function(floor:unknown)
      variable(_10686:float3)
--------------------------------------------------
第302行: float3 _10789 = _8435 * float3(0.125);
语法树结构:
assignment(=:float3)
  declaration(_10789:float3)
  operator(*:unknown)
    variable(_8435:float3)
    function(float3:unknown)
      literal(0.125:float)
--------------------------------------------------
第303行: float _10797 = _10789.x;
语法树结构:
assignment(=:float)
  declaration(_10797:float)
  member_access(_10789.x:float)
--------------------------------------------------
第304行: float _10799 = floor(_10797);
语法树结构:
assignment(=:float)
  declaration(_10799:float)
  function(floor:unknown)
    variable(_10797:float)
--------------------------------------------------
第305行: float3 _21191;
语法树结构:
declaration(_21191:float3)
--------------------------------------------------
第306行: _21191.x = _10799 - 15.0;
语法树结构:
assignment(=:unknown)
  member_access(_21191.x:unknown)
  operator(-:unknown)
    variable(_10799:float)
    literal(15.0:float)
--------------------------------------------------
第307行: float3 _21235;
语法树结构:
declaration(_21235:float3)
--------------------------------------------------
第308行: if ((_10797 - _10799) > 0.5)
语法树结构:
operator(-:unknown)
  variable(_10797:float)
  variable(_10799:float)
--------------------------------------------------
第310行: float3 _21194 = _21191;
语法树结构:
assignment(=:float3)
  declaration(_21194:float3)
  variable(_21191:float3)
--------------------------------------------------
第311行: _21194.x = _10799 + (-14.0);
语法树结构:
assignment(=:unknown)
  member_access(_21194.x:unknown)
  operator(+:unknown)
    variable(_10799:float)
    operator(-:unknown)
      literal(14.0:float)
--------------------------------------------------
第312行: _21235 = _21194;
语法树结构:
assignment(=:float3)
  variable(_21235:float3)
  variable(_21194:float3)
--------------------------------------------------
第316行: _21235 = _21191;
语法树结构:
assignment(=:float3)
  variable(_21235:float3)
  variable(_21191:float3)
--------------------------------------------------
第318行: float _21078 = _10789.y;
语法树结构:
assignment(=:float)
  declaration(_21078:float)
  member_access(_10789.y:float)
--------------------------------------------------
第319行: float _21079 = floor(_21078);
语法树结构:
assignment(=:float)
  declaration(_21079:float)
  function(floor:unknown)
    variable(_21078:float)
--------------------------------------------------
第320行: float3 _21198 = _21235;
语法树结构:
assignment(=:float3)
  declaration(_21198:float3)
  variable(_21235:float3)
--------------------------------------------------
第321行: _21198.y = _21079 - 8.0;
语法树结构:
assignment(=:unknown)
  member_access(_21198.y:unknown)
  operator(-:unknown)
    variable(_21079:float)
    literal(8.0:float)
--------------------------------------------------
第322行: float3 _21236;
语法树结构:
declaration(_21236:float3)
--------------------------------------------------
第323行: if ((_21078 - _21079) > 0.5)
语法树结构:
operator(-:unknown)
  variable(_21078:float)
  variable(_21079:float)
--------------------------------------------------
第325行: float3 _21201 = _21198;
语法树结构:
assignment(=:float3)
  declaration(_21201:float3)
  variable(_21198:float3)
--------------------------------------------------
第326行: _21201.y = _21079 + (-7.0);
语法树结构:
assignment(=:unknown)
  member_access(_21201.y:unknown)
  operator(+:unknown)
    variable(_21079:float)
    operator(-:unknown)
      literal(7.0:float)
--------------------------------------------------
第327行: _21236 = _21201;
语法树结构:
assignment(=:float3)
  variable(_21236:float3)
  variable(_21201:float3)
--------------------------------------------------
第331行: _21236 = _21198;
语法树结构:
assignment(=:float3)
  variable(_21236:float3)
  variable(_21198:float3)
--------------------------------------------------
第333行: float _21100 = _10789.z;
语法树结构:
assignment(=:float)
  declaration(_21100:float)
  member_access(_10789.z:float)
--------------------------------------------------
第334行: float _21101 = floor(_21100);
语法树结构:
assignment(=:float)
  declaration(_21101:float)
  function(floor:unknown)
    variable(_21100:float)
--------------------------------------------------
第335行: float3 _21205 = _21236;
语法树结构:
assignment(=:float3)
  declaration(_21205:float3)
  variable(_21236:float3)
--------------------------------------------------
第336行: _21205.z = _21101 - 15.0;
语法树结构:
assignment(=:unknown)
  member_access(_21205.z:unknown)
  operator(-:unknown)
    variable(_21101:float)
    literal(15.0:float)
--------------------------------------------------
第337行: float3 _21237;
语法树结构:
declaration(_21237:float3)
--------------------------------------------------
第338行: if ((_21100 - _21101) > 0.5)
语法树结构:
operator(-:unknown)
  variable(_21100:float)
  variable(_21101:float)
--------------------------------------------------
第340行: float3 _21208 = _21205;
语法树结构:
assignment(=:float3)
  declaration(_21208:float3)
  variable(_21205:float3)
--------------------------------------------------
第341行: _21208.z = _21101 + (-14.0);
语法树结构:
assignment(=:unknown)
  member_access(_21208.z:unknown)
  operator(+:unknown)
    variable(_21101:float)
    operator(-:unknown)
      literal(14.0:float)
--------------------------------------------------
第342行: _21237 = _21208;
语法树结构:
assignment(=:float3)
  variable(_21237:float3)
  variable(_21208:float3)
--------------------------------------------------
第346行: _21237 = _21205;
语法树结构:
assignment(=:float3)
  variable(_21237:float3)
  variable(_21205:float3)
--------------------------------------------------
第348行: float3 _10822 = _21237 * 8.0;
语法树结构:
assignment(=:float3)
  declaration(_10822:float3)
  operator(*:unknown)
    variable(_21237:float3)
    literal(8.0:float)
--------------------------------------------------
第349行: half _18305;
语法树结构:
declaration(_18305:half)
--------------------------------------------------
第350行: if (all(in.IN_WorldPosition.xyz >= _10822) && all(in.IN_WorldPosition.xyz < (_10822 + float3(240.0, 128.0, 240.0))))
语法树结构:
function(all:unknown)
  operator(>=:unknown)
    member_access(in.IN_WorldPosition.xyz:float3)
    variable(_10822:float3)
--------------------------------------------------
第352行: uint _10704 = (_8397 & 251658240u) >> 24u;
语法树结构:
assignment(=:uint)
  declaration(_10704:uint)
  operator(>>:unknown)
    operator(&:unknown)
      variable(_8397:uint)
      literal(251658240u:int)
    literal(24u:int)
--------------------------------------------------
第353行: float _10887 = 3.0 - float((_8397 & 458752u) >> 16u);
语法树结构:
assignment(=:float)
  declaration(_10887:float)
  operator(-:unknown)
    literal(3.0:float)
    function(float:unknown)
      operator(>>:unknown)
        operator(&:unknown)
          variable(_8397:uint)
          literal(458752u:int)
        literal(16u:int)
--------------------------------------------------
第354行: half _18297;
语法树结构:
declaration(_18297:half)
--------------------------------------------------
第355行: if (_10704 <= 3u)
语法树结构:
operator(<=:unknown)
  variable(_10704:uint)
  literal(3u:int)
--------------------------------------------------
第357行: float _10900 = 3.0 - float(_10704);
语法树结构:
assignment(=:float)
  declaration(_10900:float)
  operator(-:unknown)
    literal(3.0:float)
    function(float:unknown)
      variable(_10704:uint)
--------------------------------------------------
第358行: float2 _10994 = ((_10762.xz - float2(0.5)) * 0.9375) + float2(0.5);
语法树结构:
assignment(=:float2)
  declaration(_10994:float2)
  operator(+:unknown)
    operator(*:unknown)
      operator(-:unknown)
        member_access(_10762.xz:float2)
        function(float2:unknown)
          literal(0.5:float)
      literal(0.9375:float)
    function(float2:unknown)
      literal(0.5:float)
--------------------------------------------------
第359行: float _11001 = _10822.x * 0.0041666668839752674102783203125;
语法树结构:
assignment(=:float)
  declaration(_11001:float)
  operator(*:unknown)
    member_access(_10822.x:float)
    literal(0.0041666668839752674102783203125:float)
--------------------------------------------------
第360行: float _11005 = ((_11001 - floor(_11001)) - 0.5) * 0.9375;
语法树结构:
assignment(=:float)
  declaration(_11005:float)
  operator(*:unknown)
    operator(-:unknown)
      operator(-:unknown)
        variable(_11001:float)
        function(floor:unknown)
          variable(_11001:float)
      literal(0.5:float)
    literal(0.9375:float)
--------------------------------------------------
第361行: float _11011 = _10822.z * 0.0041666668839752674102783203125;
语法树结构:
assignment(=:float)
  declaration(_11011:float)
  operator(*:unknown)
    member_access(_10822.z:float)
    literal(0.0041666668839752674102783203125:float)
--------------------------------------------------
第362行: float _11015 = ((_11011 - floor(_11011)) - 0.5) * 0.9375;
语法树结构:
assignment(=:float)
  declaration(_11015:float)
  operator(*:unknown)
    operator(-:unknown)
      operator(-:unknown)
        variable(_11011:float)
        function(floor:unknown)
          variable(_11011:float)
      literal(0.5:float)
    literal(0.9375:float)
--------------------------------------------------
第363行: float _11020 = _10994.x;
语法树结构:
assignment(=:float)
  declaration(_11020:float)
  member_access(_10994.x:float)
--------------------------------------------------
第364行: float3 _17954;
语法树结构:
declaration(_17954:float3)
--------------------------------------------------
第365行: _17954.x = (_11020 < (_11005 + 0.5)) ? fast::min(_11020, _11005 + 0.49609375) : fast::max(_11020, _11005 + 0.50390625);
语法树结构:
assignment(=:unknown)
  member_access(_17954.x:unknown)
  operator(?::unknown)
    operator(<:unknown)
      variable(_11020:float)
      operator(+:unknown)
        variable(_11005:float)
        literal(0.5:float)
    function(fast::min:unknown)
      variable(_11020:float)
      operator(+:unknown)
        variable(_11005:float)
        literal(0.49609375:float)
    function(fast::max:unknown)
      variable(_11020:float)
      operator(+:unknown)
        variable(_11005:float)
        literal(0.50390625:float)
--------------------------------------------------
第366行: float _11038 = _10994.y;
语法树结构:
assignment(=:float)
  declaration(_11038:float)
  member_access(_10994.y:float)
--------------------------------------------------
第367行: _17954.z = (_11038 < (_11015 + 0.5)) ? fast::min(_11038, _11015 + 0.49609375) : fast::max(_11038, _11015 + 0.50390625);
语法树结构:
assignment(=:unknown)
  member_access(_17954.z:unknown)
  operator(?::unknown)
    operator(<:unknown)
      variable(_11038:float)
      operator(+:unknown)
        variable(_11015:float)
        literal(0.5:float)
    function(fast::min:unknown)
      variable(_11038:float)
      operator(+:unknown)
        variable(_11015:float)
        literal(0.49609375:float)
    function(fast::max:unknown)
      variable(_11038:float)
      operator(+:unknown)
        variable(_11015:float)
        literal(0.50390625:float)
--------------------------------------------------
第368行: float _11059 = (_10762.y * 64.0) - 0.5;
语法树结构:
assignment(=:float)
  declaration(_11059:float)
  operator(-:unknown)
    operator(*:unknown)
      member_access(_10762.y:float)
      literal(64.0:float)
    literal(0.5:float)
--------------------------------------------------
第369行: float _11064 = floor(_11059);
语法树结构:
assignment(=:float)
  declaration(_11064:float)
  function(floor:unknown)
    variable(_11059:float)
--------------------------------------------------
第370行: uint _11067 = (_11059 < 0.0) ? 63u : uint(_11064);
语法树结构:
assignment(=:uint)
  declaration(_11067:uint)
  operator(?::unknown)
    operator(<:unknown)
      variable(_11059:float)
      literal(0.0:float)
    literal(63u:int)
    function(uint:unknown)
      variable(_11064:float)
--------------------------------------------------
第371行: uint _11070 = _11067 + 1u;
语法树结构:
assignment(=:uint)
  declaration(_11070:uint)
  operator(+:unknown)
    variable(_11067:uint)
    literal(1u:int)
--------------------------------------------------
第372行: uint _21301 = (_11070 >= 64u) ? 0u : _11070;
语法树结构:
assignment(=:uint)
  declaration(_21301:uint)
  operator(?::unknown)
    operator(>=:unknown)
      variable(_11070:uint)
      literal(64u:int)
    literal(0u:int)
    variable(_11070:uint)
--------------------------------------------------
第373行: float2 _11097 = (float2(float(_11067 & 7u), float(_11067 >> 3u)) + _17954.xz) * 0.125;
语法树结构:
assignment(=:float2)
  declaration(_11097:float2)
  operator(*:unknown)
    operator(+:unknown)
      function(float2:unknown)
        function(float:unknown)
          operator(&:unknown)
            variable(_11067:uint)
            literal(7u:int)
        function(float:unknown)
          operator(>>:unknown)
            variable(_11067:uint)
            literal(3u:int)
      member_access(_17954.xz:float2)
    literal(0.125:float)
--------------------------------------------------
第374行: float _11100 = _11097.x;
语法树结构:
assignment(=:float)
  declaration(_11100:float)
  member_access(_11097.x:float)
--------------------------------------------------
第375行: float3 _11102 = float3(_11100, _11097.y, _10887);
语法树结构:
assignment(=:float3)
  declaration(_11102:float3)
  function(float3:unknown)
    variable(_11100:float)
    member_access(_11097.y:float)
    variable(_10887:float)
--------------------------------------------------
第376行: half4 _17962;
语法树结构:
declaration(_17962:half4)
--------------------------------------------------
第377行: _17962.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11102.xy, uint(rint(_11102.z)), level(0.0)).x);
语法树结构:
assignment(=:unknown)
  member_access(_17962.w:unknown)
  function(half:unknown)
    member_access(result_sSHAOAlphaVTSampler.sample.x:unknown)
      function(sSHAOAlphaVTSampler.sample:unknown)
        variable(sSHAOAlphaVTSamplerSmplr:sampler)
        member_access(_11102.xy:float2)
        function(uint:unknown)
          function(rint:unknown)
            member_access(_11102.z:float)
        function(level:unknown)
          literal(0.0:float)
--------------------------------------------------
第378行: float3 _11113 = float3(_11100, _11097.y, _10900);
语法树结构:
assignment(=:float3)
  declaration(_11113:float3)
  function(float3:unknown)
    variable(_11100:float)
    member_access(_11097.y:float)
    variable(_10900:float)
--------------------------------------------------
第379行: half3 _11118 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _11113.xy, uint(rint(_11113.z)), level(0.0)).xyz);
语法树结构:
assignment(=:half3)
  declaration(_11118:half3)
  function(half3:unknown)
    member_access(result_sSHAORGBVTSampler.sample.xyz:unknown)
      function(sSHAORGBVTSampler.sample:unknown)
        variable(sSHAORGBVTSamplerSmplr:sampler)
        member_access(_11113.xy:float2)
        function(uint:unknown)
          function(rint:unknown)
            member_access(_11113.z:float)
        function(level:unknown)
          literal(0.0:float)
--------------------------------------------------
第380行: float2 _11135 = (float2(float(_21301 & 7u), float(_21301 >> 3u)) + _17954.xz) * 0.125;
语法树结构:
assignment(=:float2)
  declaration(_11135:float2)
  operator(*:unknown)
    operator(+:unknown)
      function(float2:unknown)
        function(float:unknown)
          operator(&:unknown)
            variable(_21301:uint)
            literal(7u:int)
        function(float:unknown)
          operator(>>:unknown)
            variable(_21301:uint)
            literal(3u:int)
      member_access(_17954.xz:float2)
    literal(0.125:float)
--------------------------------------------------
第381行: float _11138 = _11135.x;
语法树结构:
assignment(=:float)
  declaration(_11138:float)
  member_access(_11135.x:float)
--------------------------------------------------
第382行: float3 _11140 = float3(_11138, _11135.y, _10887);
语法树结构:
assignment(=:float3)
  declaration(_11140:float3)
  function(float3:unknown)
    variable(_11138:float)
    member_access(_11135.y:float)
    variable(_10887:float)
--------------------------------------------------
第383行: half4 _17964;
语法树结构:
declaration(_17964:half4)
--------------------------------------------------
第384行: _17964.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11140.xy, uint(rint(_11140.z)), level(0.0)).x);
语法树结构:
assignment(=:unknown)
  member_access(_17964.w:unknown)
  function(half:unknown)
    member_access(result_sSHAOAlphaVTSampler.sample.x:unknown)
      function(sSHAOAlphaVTSampler.sample:unknown)
        variable(sSHAOAlphaVTSamplerSmplr:sampler)
        member_access(_11140.xy:float2)
        function(uint:unknown)
          function(rint:unknown)
            member_access(_11140.z:float)
        function(level:unknown)
          literal(0.0:float)
--------------------------------------------------
第385行: float3 _11151 = float3(_11138, _11135.y, _10900);
语法树结构:
assignment(=:float3)
  declaration(_11151:float3)
  function(float3:unknown)
    variable(_11138:float)
    member_access(_11135.y:float)
    variable(_10900:float)
--------------------------------------------------
第386行: half3 _11156 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _11151.xy, uint(rint(_11151.z)), level(0.0)).xyz);
语法树结构:
assignment(=:half3)
  declaration(_11156:half3)
  function(half3:unknown)
    member_access(result_sSHAORGBVTSampler.sample.xyz:unknown)
      function(sSHAORGBVTSampler.sample:unknown)
        variable(sSHAORGBVTSamplerSmplr:sampler)
        member_access(_11151.xy:float2)
        function(uint:unknown)
          function(rint:unknown)
            member_access(_11151.z:float)
        function(level:unknown)
          literal(0.0:float)
--------------------------------------------------
第387行: half4 _11163 = mix(half4(_11118.x, _11118.y, _11118.z, _17962.w), half4(_11156.x, _11156.y, _11156.z, _17964.w), half4(half(fast::clamp(_11059 - _11064, 0.0, 1.0))));
语法树结构:
assignment(=:half4)
  declaration(_11163:half4)
  function(mix:unknown)
    function(half4:unknown)
      member_access(_11118.x:half)
      member_access(_11118.y:half)
      member_access(_11118.z:half)
      member_access(_17962.w:half)
    function(half4:unknown)
      member_access(_11156.x:half)
      member_access(_11156.y:half)
      member_access(_11156.z:half)
      member_access(_17964.w:half)
    function(half4:unknown)
      function(half:unknown)
        function(fast::clamp:unknown)
          operator(-:unknown)
            variable(_11059:float)
            variable(_11064:float)
          literal(0.0:float)
          literal(1.0:float)
--------------------------------------------------
第388行: _18297 = clamp((_11163.w * half(32.0)) + half(float(dot(half3(_8373), half3((float3(_11163.xyz) * float3(2.0)) - float3(1.0)))) * 2.0), half(0.0), half(1.0));
语法树结构:
assignment(=:half)
  variable(_18297:half)
  function(clamp:unknown)
    operator(+:unknown)
      operator(*:unknown)
        member_access(_11163.w:half)
        function(half:unknown)
          literal(32.0:float)
      function(half:unknown)
        operator(*:unknown)
          function(float:unknown)
            function(dot:unknown)
              function(half3:unknown)
                variable(_8373:float3)
              function(half3:unknown)
                operator(-:unknown)
                  operator(*:unknown)
                    function(float3:unknown)
                      member_access(_11163.xyz:half3)
                    function(float3:unknown)
                      literal(2.0:float)
                  function(float3:unknown)
                    literal(1.0:float)
          literal(2.0:float)
    function(half:unknown)
      literal(0.0:float)
    function(half:unknown)
      literal(1.0:float)
--------------------------------------------------
第392行: float2 _11233 = ((_10762.xz - float2(0.5)) * 0.9375) + float2(0.5);
语法树结构:
assignment(=:float2)
  declaration(_11233:float2)
  operator(+:unknown)
    operator(*:unknown)
      operator(-:unknown)
        member_access(_10762.xz:float2)
        function(float2:unknown)
          literal(0.5:float)
      literal(0.9375:float)
    function(float2:unknown)
      literal(0.5:float)
--------------------------------------------------
第393行: float _11240 = _10822.x * 0.0041666668839752674102783203125;
语法树结构:
assignment(=:float)
  declaration(_11240:float)
  operator(*:unknown)
    member_access(_10822.x:float)
    literal(0.0041666668839752674102783203125:float)
--------------------------------------------------
第394行: float _11244 = ((_11240 - floor(_11240)) - 0.5) * 0.9375;
语法树结构:
assignment(=:float)
  declaration(_11244:float)
  operator(*:unknown)
    operator(-:unknown)
      operator(-:unknown)
        variable(_11240:float)
        function(floor:unknown)
          variable(_11240:float)
      literal(0.5:float)
    literal(0.9375:float)
--------------------------------------------------
第395行: float _11250 = _10822.z * 0.0041666668839752674102783203125;
语法树结构:
assignment(=:float)
  declaration(_11250:float)
  operator(*:unknown)
    member_access(_10822.z:float)
    literal(0.0041666668839752674102783203125:float)
--------------------------------------------------
第396行: float _11254 = ((_11250 - floor(_11250)) - 0.5) * 0.9375;
语法树结构:
assignment(=:float)
  declaration(_11254:float)
  operator(*:unknown)
    operator(-:unknown)
      operator(-:unknown)
        variable(_11250:float)
        function(floor:unknown)
          variable(_11250:float)
      literal(0.5:float)
    literal(0.9375:float)
--------------------------------------------------
第397行: float _11259 = _11233.x;
语法树结构:
assignment(=:float)
  declaration(_11259:float)
  member_access(_11233.x:float)
--------------------------------------------------
第398行: float3 _17977;
语法树结构:
declaration(_17977:float3)
--------------------------------------------------
第399行: _17977.x = (_11259 < (_11244 + 0.5)) ? fast::min(_11259, _11244 + 0.49609375) : fast::max(_11259, _11244 + 0.50390625);
语法树结构:
assignment(=:unknown)
  member_access(_17977.x:unknown)
  operator(?::unknown)
    operator(<:unknown)
      variable(_11259:float)
      operator(+:unknown)
        variable(_11244:float)
        literal(0.5:float)
    function(fast::min:unknown)
      variable(_11259:float)
      operator(+:unknown)
        variable(_11244:float)
        literal(0.49609375:float)
    function(fast::max:unknown)
      variable(_11259:float)
      operator(+:unknown)
        variable(_11244:float)
        literal(0.50390625:float)
--------------------------------------------------
第400行: float _11277 = _11233.y;
语法树结构:
assignment(=:float)
  declaration(_11277:float)
  member_access(_11233.y:float)
--------------------------------------------------
第401行: _17977.z = (_11277 < (_11254 + 0.5)) ? fast::min(_11277, _11254 + 0.49609375) : fast::max(_11277, _11254 + 0.50390625);
语法树结构:
assignment(=:unknown)
  member_access(_17977.z:unknown)
  operator(?::unknown)
    operator(<:unknown)
      variable(_11277:float)
      operator(+:unknown)
        variable(_11254:float)
        literal(0.5:float)
    function(fast::min:unknown)
      variable(_11277:float)
      operator(+:unknown)
        variable(_11254:float)
        literal(0.49609375:float)
    function(fast::max:unknown)
      variable(_11277:float)
      operator(+:unknown)
        variable(_11254:float)
        literal(0.50390625:float)
--------------------------------------------------
第402行: float _11298 = (_10762.y * 64.0) - 0.5;
语法树结构:
assignment(=:float)
  declaration(_11298:float)
  operator(-:unknown)
    operator(*:unknown)
      member_access(_10762.y:float)
      literal(64.0:float)
    literal(0.5:float)
--------------------------------------------------
第403行: float _11303 = floor(_11298);
语法树结构:
assignment(=:float)
  declaration(_11303:float)
  function(floor:unknown)
    variable(_11298:float)
--------------------------------------------------
第404行: uint _11306 = (_11298 < 0.0) ? 63u : uint(_11303);
语法树结构:
assignment(=:uint)
  declaration(_11306:uint)
  operator(?::unknown)
    operator(<:unknown)
      variable(_11298:float)
      literal(0.0:float)
    literal(63u:int)
    function(uint:unknown)
      variable(_11303:float)
--------------------------------------------------
第405行: uint _11309 = _11306 + 1u;
语法树结构:
assignment(=:uint)
  declaration(_11309:uint)
  operator(+:unknown)
    variable(_11306:uint)
    literal(1u:int)
--------------------------------------------------
第406行: uint _21300 = (_11309 >= 64u) ? 0u : _11309;
语法树结构:
assignment(=:uint)
  declaration(_21300:uint)
  operator(?::unknown)
    operator(>=:unknown)
      variable(_11309:uint)
      literal(64u:int)
    literal(0u:int)
    variable(_11309:uint)
--------------------------------------------------
第407行: float3 _11340 = float3((float2(float(_11306 & 7u), float(_11306 >> 3u)) + _17977.xz) * 0.125, _10887);
语法树结构:
assignment(=:float3)
  declaration(_11340:float3)
  function(float3:unknown)
    operator(*:unknown)
      operator(+:unknown)
        function(float2:unknown)
          function(float:unknown)
            operator(&:unknown)
              variable(_11306:uint)
              literal(7u:int)
          function(float:unknown)
            operator(>>:unknown)
              variable(_11306:uint)
              literal(3u:int)
        member_access(_17977.xz:float2)
      literal(0.125:float)
    variable(_10887:float)
--------------------------------------------------
第408行: float3 _11365 = float3((float2(float(_21300 & 7u), float(_21300 >> 3u)) + _17977.xz) * 0.125, _10887);
语法树结构:
assignment(=:float3)
  declaration(_11365:float3)
  function(float3:unknown)
    operator(*:unknown)
      operator(+:unknown)
        function(float2:unknown)
          function(float:unknown)
            operator(&:unknown)
              variable(_21300:uint)
              literal(7u:int)
          function(float:unknown)
            operator(>>:unknown)
              variable(_21300:uint)
              literal(3u:int)
        member_access(_17977.xz:float2)
      literal(0.125:float)
    variable(_10887:float)
--------------------------------------------------
第409行: _18297 = half(mix(float(half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11340.xy, uint(rint(_11340.z)), level(0.0)).x)), float(half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11365.xy, uint(rint(_11365.z)), level(0.0)).x)), fast::clamp(_11298 - _11303, 0.0, 1.0))) * half(32.0);
语法树结构:
assignment(=:half)
  variable(_18297:half)
  operator(*:unknown)
    function(half:unknown)
      function(mix:unknown)
        function(float:unknown)
          function(half:unknown)
            member_access(result_sSHAOAlphaVTSampler.sample.x:unknown)
              function(sSHAOAlphaVTSampler.sample:unknown)
                variable(sSHAOAlphaVTSamplerSmplr:sampler)
                member_access(_11340.xy:float2)
                function(uint:unknown)
                  function(rint:unknown)
                    member_access(_11340.z:float)
                function(level:unknown)
                  literal(0.0:float)
        function(float:unknown)
          function(half:unknown)
            member_access(result_sSHAOAlphaVTSampler.sample.x:unknown)
              function(sSHAOAlphaVTSampler.sample:unknown)
                variable(sSHAOAlphaVTSamplerSmplr:sampler)
                member_access(_11365.xy:float2)
                function(uint:unknown)
                  function(rint:unknown)
                    member_access(_11365.z:float)
                function(level:unknown)
                  literal(0.0:float)
        function(fast::clamp:unknown)
          operator(-:unknown)
            variable(_11298:float)
            variable(_11303:float)
          literal(0.0:float)
          literal(1.0:float)
    function(half:unknown)
      literal(32.0:float)
--------------------------------------------------
第411行: float3 _11404 = (((in.IN_WorldPosition.xyz - _10822) * float3(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125)) * 2.0) - float3(1.0);
语法树结构:
assignment(=:float3)
  declaration(_11404:float3)
  operator(-:unknown)
    operator(*:unknown)
      operator(*:unknown)
        operator(-:unknown)
          member_access(in.IN_WorldPosition.xyz:float3)
          variable(_10822:float3)
        function(float3:unknown)
          literal(0.0041666668839752674102783203125:float)
          literal(0.0078125:float)
          literal(0.0041666668839752674102783203125:float)
      literal(2.0:float)
    function(float3:unknown)
      literal(1.0:float)
--------------------------------------------------
第412行: float3 _11407 = _11404 * _11404;
语法树结构:
assignment(=:float3)
  declaration(_11407:float3)
  operator(*:unknown)
    variable(_11404:float3)
    variable(_11404:float3)
--------------------------------------------------
第413行: float3 _11410 = _11407 * _11407;
语法树结构:
assignment(=:float3)
  declaration(_11410:float3)
  operator(*:unknown)
    variable(_11407:float3)
    variable(_11407:float3)
--------------------------------------------------
第414行: half _18303;
语法树结构:
declaration(_18303:half)
--------------------------------------------------
第415行: if ((!((_8397 & 4u) != 0u)) && ((_8397 & 32768u) > 0u))
语法树结构:
operator(!:unknown)
  operator(&:unknown)
    variable(_8397:uint)
    literal(4u:int)
--------------------------------------------------
第417行: _18303 = half(mix(float(_18297), 1.0, fast::clamp(fast::max(_11410.x, fast::max(_11410.y, _11410.z)), 0.0, 1.0)));
语法树结构:
assignment(=:half)
  variable(_18303:half)
  function(half:unknown)
    function(mix:unknown)
      function(float:unknown)
        variable(_18297:half)
      literal(1.0:float)
      function(fast::clamp:unknown)
        function(fast::max:unknown)
          member_access(_11410.x:float)
          function(fast::max:unknown)
            member_access(_11410.y:float)
            member_access(_11410.z:float)
        literal(0.0:float)
        literal(1.0:float)
--------------------------------------------------
第421行: _18303 = _18297;
语法树结构:
assignment(=:half)
  variable(_18303:half)
  variable(_18297:half)
--------------------------------------------------
第423行: _18305 = _18303;
语法树结构:
assignment(=:half)
  variable(_18305:half)
  variable(_18303:half)
--------------------------------------------------
第427行: _18305 = _9019;
语法树结构:
assignment(=:half)
  variable(_18305:half)
  variable(_9019:half)
--------------------------------------------------
第429行: _18304 = _18305;
语法树结构:
assignment(=:half)
  variable(_18304:half)
  variable(_18305:half)
--------------------------------------------------
第433行: _18304 = _9019;
语法树结构:
assignment(=:half)
  variable(_18304:half)
  variable(_9019:half)
--------------------------------------------------
第435行: float _11467 = _Block1.SHAOParam.z * _Block1.SHAOParam.z;
语法树结构:
assignment(=:float)
  declaration(_11467:float)
  operator(*:unknown)
    member_access(_Block1.SHAOParam.z:float)
    member_access(_Block1.SHAOParam.z:float)
--------------------------------------------------
第436行: float3 _11470 = in.IN_WorldPosition.xyz - _8435;
语法树结构:
assignment(=:float3)
  declaration(_11470:float3)
  operator(-:unknown)
    member_access(in.IN_WorldPosition.xyz:float3)
    variable(_8435:float3)
--------------------------------------------------
第437行: float _11479 = fast::clamp((_11467 - dot(_11470, _11470)) / _11467, 0.0, 1.0);
语法树结构:
assignment(=:float)
  declaration(_11479:float)
  function(fast::clamp:unknown)
    operator(/:unknown)
      operator(-:unknown)
        variable(_11467:float)
        function(dot:unknown)
          variable(_11470:float3)
          variable(_11470:float3)
      variable(_11467:float)
    literal(0.0:float)
    literal(1.0:float)
--------------------------------------------------
第438行: _18306 = half(1.0 - fast::clamp((1.0 - float(half(fast::clamp(1.0 - float(half(powr(float(half(fast::clamp(1.0 - float(_18304), 0.0, 1.0))), fast::max(mix(6.0, 0.0, fast::clamp(_Block1.SHGIParam2.w, 0.0, 1.0)), 0.5)))), 0.0, 1.0)))) * float(half(mix(_Block1.SHAOParam.x, _Block1.SHAOParam.y, 1.0 - (_11479 * _11479)))), 0.0, 1.0));
语法树结构:
assignment(=:half)
  variable(_18306:half)
  function(half:unknown)
    operator(-:unknown)
      literal(1.0:float)
      function(fast::clamp:unknown)
        operator(*:unknown)
          operator(-:unknown)
            literal(1.0:float)
            function(float:unknown)
              function(half:unknown)
                function(fast::clamp:unknown)
                  operator(-:unknown)
                    literal(1.0:float)
                    function(float:unknown)
                      function(half:unknown)
                        function(powr:unknown)
                          function(float:unknown)
                            function(half:unknown)
                              function(fast::clamp:unknown)
                                operator(-:unknown)
                                  literal(1.0:float)
                                  function(float:unknown)
                                    variable(_18304:half)
                                literal(0.0:float)
                                literal(1.0:float)
                          function(fast::max:unknown)
                            function(mix:unknown)
                              literal(6.0:float)
                              literal(0.0:float)
                              function(fast::clamp:unknown)
                                member_access(_Block1.SHGIParam2.w:float)
                                literal(0.0:float)
                                literal(1.0:float)
                            literal(0.5:float)
                  literal(0.0:float)
                  literal(1.0:float)
          function(float:unknown)
            function(half:unknown)
              function(mix:unknown)
                member_access(_Block1.SHAOParam.x:float)
                member_access(_Block1.SHAOParam.y:float)
                operator(-:unknown)
                  literal(1.0:float)
                  operator(*:unknown)
                    variable(_11479:float)
                    variable(_11479:float)
        literal(0.0:float)
        literal(1.0:float)
--------------------------------------------------
第442行: _18306 = _9019;
语法树结构:
assignment(=:half)
  variable(_18306:half)
  variable(_9019:half)
--------------------------------------------------
第444行: half _18330;
语法树结构:
declaration(_18330:half)
--------------------------------------------------
第445行: if (!((_8397 & 64u) > 0u))
语法树结构:
operator(!:unknown)
  operator(&:unknown)
    variable(_8397:uint)
    literal(64u:int)
--------------------------------------------------
第447行: _18330 = _8346 * _18306;
语法树结构:
assignment(=:half)
  variable(_18330:half)
  operator(*:unknown)
    variable(_8346:half)
    variable(_18306:half)
--------------------------------------------------
第451行: _18330 = _8346;
语法树结构:
assignment(=:half)
  variable(_18330:half)
  variable(_8346:half)
--------------------------------------------------
第453行: _18329 = _18330;
语法树结构:
assignment(=:half)
  variable(_18329:half)
  variable(_18330:half)
--------------------------------------------------
第457行: _18329 = _8346;
语法树结构:
assignment(=:half)
  variable(_18329:half)
  variable(_8346:half)
--------------------------------------------------
第459行: float3 _11517 = float3(half3(_8373));
语法树结构:
assignment(=:float3)
  declaration(_11517:float3)
  function(float3:unknown)
    function(half3:unknown)
      variable(_8373:float3)
--------------------------------------------------
第460行: float3 _11600 = _Block1.CameraPos.xyz + (fast::normalize(float3(_Block1.SunDirection.x, fast::min(_Block1.SunDirection.y, 1.0), _Block1.SunDirection.z)) * 200000.0);
语法树结构:
assignment(=:float3)
  declaration(_11600:float3)
  operator(+:unknown)
    member_access(_Block1.CameraPos.xyz:float3)
    operator(*:unknown)
      function(fast::normalize:unknown)
        function(float3:unknown)
          member_access(_Block1.SunDirection.x:float)
          function(fast::min:unknown)
            member_access(_Block1.SunDirection.y:float)
            literal(1.0:float)
          member_access(_Block1.SunDirection.z:float)
      literal(200000.0:float)
--------------------------------------------------
第461行: float3 _11604 = reflect(-_11517, _9109);
语法树结构:
assignment(=:float3)
  declaration(_11604:float3)
  function(reflect:unknown)
    operator(-:unknown)
      variable(_11517:float3)
    variable(_9109:float3)
--------------------------------------------------
第462行: float3 _11611 = (_11604 * dot(_11600, _11604)) - _11600;
语法树结构:
assignment(=:float3)
  declaration(_11611:float3)
  operator(-:unknown)
    operator(*:unknown)
      variable(_11604:float3)
      function(dot:unknown)
        variable(_11600:float3)
        variable(_11604:float3)
    variable(_11600:float3)
--------------------------------------------------
第463行: float3 _11622 = fast::normalize(_11600 + (_11611 * fast::clamp(4500.0 / length(_11611), 0.0, 1.0)));
语法树结构:
assignment(=:float3)
  declaration(_11622:float3)
  function(fast::normalize:unknown)
    operator(+:unknown)
      variable(_11600:float3)
      operator(*:unknown)
        variable(_11611:float3)
        function(fast::clamp:unknown)
          operator(/:unknown)
            literal(4500.0:float)
            function(length:unknown)
              variable(_11611:float3)
          literal(0.0:float)
          literal(1.0:float)
--------------------------------------------------
第464行: half _11536 = clamp(half(dot(_9109, _11622)), half(0.0), half(1.0));
语法树结构:
assignment(=:half)
  declaration(_11536:half)
  function(clamp:unknown)
    function(half:unknown)
      function(dot:unknown)
        variable(_9109:float3)
        variable(_11622:float3)
    function(half:unknown)
      literal(0.0:float)
    function(half:unknown)
      literal(1.0:float)
--------------------------------------------------
第465行: float _11560 = float(half(fast::max(0.119999997317790985107421875, float(_18272))));
语法树结构:
assignment(=:float)
  declaration(_11560:float)
  function(float:unknown)
    function(half:unknown)
      function(fast::max:unknown)
        literal(0.119999997317790985107421875:float)
        function(float:unknown)
          variable(_18272:half)
--------------------------------------------------
第466行: float _11629 = fast::max(0.00999999977648258209228515625, fast::clamp((4.125 * _11560) - 0.319999992847442626953125, 0.0, 1.0));
语法树结构:
assignment(=:float)
  declaration(_11629:float)
  function(fast::max:unknown)
    literal(0.00999999977648258209228515625:float)
    function(fast::clamp:unknown)
      operator(-:unknown)
        operator(*:unknown)
          literal(4.125:float)
          variable(_11560:float)
        literal(0.319999992847442626953125:float)
      literal(0.0:float)
      literal(1.0:float)
--------------------------------------------------
第467行: float _11919 = float(_11536);
语法树结构:
assignment(=:float)
  declaration(_11919:float)
  function(float:unknown)
    variable(_11536:half)
--------------------------------------------------
第468行: float _11925 = dot(_9109, _11517);
语法树结构:
assignment(=:float)
  declaration(_11925:float)
  function(dot:unknown)
    variable(_9109:float3)
    variable(_11517:float3)
--------------------------------------------------
第469行: float3 _11940 = fast::normalize(_11517 + _11622);
语法树结构:
assignment(=:float3)
  declaration(_11940:float3)
  function(fast::normalize:unknown)
    operator(+:unknown)
      variable(_11517:float3)
      variable(_11622:float3)
--------------------------------------------------
第470行: float _11945 = fast::clamp(dot(_9109, _11940), 0.0, 1.0);
语法树结构:
assignment(=:float)
  declaration(_11945:float)
  function(fast::clamp:unknown)
    function(dot:unknown)
      variable(_9109:float3)
      variable(_11940:float3)
    literal(0.0:float)
    literal(1.0:float)
--------------------------------------------------
第471行: float _11951 = fast::clamp(dot(_11517, _11940), 0.0, 1.0);
语法树结构:
assignment(=:float)
  declaration(_11951:float)
  function(fast::clamp:unknown)
    function(dot:unknown)
      variable(_11517:float3)
      variable(_11940:float3)
    literal(0.0:float)
    literal(1.0:float)
--------------------------------------------------
第472行: float _11736 = fast::clamp(abs(fast::clamp(_11925, 0.0, 1.0)) + 9.9999997473787516355514526367188e-06, 0.0, 1.0);
语法树结构:
assignment(=:float)
  declaration(_11736:float)
  function(fast::clamp:unknown)
    operator(+:unknown)
      function(abs:unknown)
        function(fast::clamp:unknown)
          variable(_11925:float)
          literal(0.0:float)
          literal(1.0:float)
      literal(9.9999997473787516355514526367188:float)
--------------------------------------------------
第473行: half4 _11959 = half4(half(0.60000002384185791015625));
语法树结构:
assignment(=:half4)
  declaration(_11959:half4)
  function(half4:unknown)
    function(half:unknown)
      literal(0.60000002384185791015625:float)
--------------------------------------------------
第474行: half4 _11967 = _11959 * _11959;
语法树结构:
assignment(=:half4)
  declaration(_11967:half4)
  operator(*:unknown)
    variable(_11959:half4)
    variable(_11959:half4)
--------------------------------------------------
第475行: half4 _11970 = half4(half(1.0)) - _11967;
语法树结构:
assignment(=:half4)
  declaration(_11970:half4)
  operator(-:unknown)
    function(half4:unknown)
      function(half:unknown)
        literal(1.0:float)
    variable(_11967:half4)
--------------------------------------------------
第476行: half4 _11973 = half4(half(1.0)) + _11967;
语法树结构:
assignment(=:half4)
  declaration(_11973:half4)
  operator(+:unknown)
    function(half4:unknown)
      function(half:unknown)
        literal(1.0:float)
    variable(_11967:half4)
--------------------------------------------------
第477行: half4 _11975 = _11959 * half(2.0);
语法树结构:
assignment(=:half4)
  declaration(_11975:half4)
  operator(*:unknown)
    variable(_11959:half4)
    function(half:unknown)
      literal(2.0:float)
--------------------------------------------------
第478行: half4 _11982 = half4(half(1.5));
语法树结构:
assignment(=:half4)
  declaration(_11982:half4)
  function(half4:unknown)
    function(half:unknown)
      literal(1.5:float)
--------------------------------------------------
第479行: float _11756 = exp2((((-5.554729938507080078125) * _11951) - 6.9831600189208984375) * _11951);
语法树结构:
assignment(=:float)
  declaration(_11756:float)
  function(exp2:unknown)
    operator(*:unknown)
      operator(-:unknown)
        operator(*:unknown)
          operator(-:unknown)
            literal(5.554729938507080078125:float)
          variable(_11951:float)
        literal(6.9831600189208984375:float)
      variable(_11951:float)
--------------------------------------------------
第480行: float _11763 = _11756 + ((1.0 - _11756) * 0.039999999105930328369140625);
语法树结构:
assignment(=:float)
  declaration(_11763:float)
  operator(+:unknown)
    variable(_11756:float)
    operator(*:unknown)
      operator(-:unknown)
        literal(1.0:float)
        variable(_11756:float)
      literal(0.039999999105930328369140625:float)
--------------------------------------------------
第481行: half _11764 = half(0.699999988079071044921875);
语法树结构:
assignment(=:half)
  declaration(_11764:half)
  function(half:unknown)
    literal(0.699999988079071044921875:float)
--------------------------------------------------
第482行: half _11768 = half(float(_11764) + 0.100000001490116119384765625);
语法树结构:
assignment(=:half)
  declaration(_11768:half)
  function(half:unknown)
    operator(+:unknown)
      function(float:unknown)
        variable(_11764:half)
      literal(0.100000001490116119384765625:float)
--------------------------------------------------
第483行: half _11772 = _9199 - _8351;
语法树结构:
assignment(=:half)
  declaration(_11772:half)
  operator(-:unknown)
    variable(_9199:half)
    variable(_8351:half)
--------------------------------------------------
第484行: half _11777 = _9199 + _11768;
语法树结构:
assignment(=:half)
  declaration(_11777:half)
  operator(+:unknown)
    variable(_9199:half)
    variable(_11768:half)
--------------------------------------------------
第485行: half _11790 = _9199 + _11764;
语法树结构:
assignment(=:half)
  declaration(_11790:half)
  operator(+:unknown)
    variable(_9199:half)
    variable(_11764:half)
--------------------------------------------------
第486行: half3 _11812 = half3((float3(half3(_Block1.SunColor.xyz)) * _11919) * float(clamp(((_11772 + _11768) / _11777) * _11777, half(0.0), half(1.0)) * clamp(((_11536 + _11764) / _11790) * _11790, half(0.0), half(1.0))));
语法树结构:
assignment(=:half3)
  declaration(_11812:half3)
  function(half3:unknown)
    operator(*:unknown)
      operator(*:unknown)
        function(float3:unknown)
          function(half3:unknown)
            member_access(_Block1.SunColor.xyz:float3)
        variable(_11919:float)
      function(float:unknown)
        operator(*:unknown)
          function(clamp:unknown)
            operator(*:unknown)
              operator(/:unknown)
                operator(+:unknown)
                  variable(_11772:half)
                  variable(_11768:half)
                variable(_11777:half)
              variable(_11777:half)
            function(half:unknown)
              literal(0.0:float)
            function(half:unknown)
              literal(1.0:float)
          function(clamp:unknown)
            operator(*:unknown)
              operator(/:unknown)
                operator(+:unknown)
                  variable(_11536:half)
                  variable(_11764:half)
                variable(_11790:half)
              variable(_11790:half)
            function(half:unknown)
              literal(0.0:float)
            function(half:unknown)
              literal(1.0:float)
--------------------------------------------------
第487行: half _11815 = half(10.0);
语法树结构:
assignment(=:half)
  declaration(_11815:half)
  function(half:unknown)
    literal(10.0:float)
--------------------------------------------------
第488行: half _11827 = clamp(dot(half3(fast::normalize(_11622 + _11517)), _9064), half(0.0), half(1.0));
语法树结构:
assignment(=:half)
  declaration(_11827:half)
  function(clamp:unknown)
    function(dot:unknown)
      function(half3:unknown)
        function(fast::normalize:unknown)
          operator(+:unknown)
            variable(_11622:float3)
            variable(_11517:float3)
      variable(_9064:half3)
    function(half:unknown)
      literal(0.0:float)
    function(half:unknown)
      literal(1.0:float)
--------------------------------------------------
第489行: float _11858 = float(_11815) * 0.5;
语法树结构:
assignment(=:float)
  declaration(_11858:float)
  operator(*:unknown)
    function(float:unknown)
      variable(_11815:half)
    literal(0.5:float)
--------------------------------------------------
第490行: float _11862 = float(_9251 + _11815);
语法树结构:
assignment(=:float)
  declaration(_11862:float)
  function(float:unknown)
    operator(+:unknown)
      variable(_9251:half)
      variable(_11815:half)
--------------------------------------------------
第491行: float _12049 = _11560 * _11560;
语法树结构:
assignment(=:float)
  declaration(_12049:float)
  operator(*:unknown)
    variable(_11560:float)
    variable(_11560:float)
--------------------------------------------------
第492行: float _12062 = _12049 / (((((_11945 * _12049) * _12049) - _11945) * _11945) + 1.0);
语法树结构:
assignment(=:float)
  declaration(_12062:float)
  operator(/:unknown)
    variable(_12049:float)
    operator(+:unknown)
      operator(*:unknown)
        operator(-:unknown)
          operator(*:unknown)
            operator(*:unknown)
              variable(_11945:float)
              variable(_12049:float)
            variable(_12049:float)
          variable(_11945:float)
        variable(_11945:float)
      literal(1.0:float)
--------------------------------------------------
第493行: float _12080 = _12049 * _12049;
语法树结构:
assignment(=:float)
  declaration(_12080:float)
  operator(*:unknown)
    variable(_12049:float)
    variable(_12049:float)
--------------------------------------------------
第494行: float _12108 = float(half(9.9956989288330078125e-05));
语法树结构:
assignment(=:float)
  declaration(_12108:float)
  function(float:unknown)
    function(half:unknown)
      literal(9.9956989288330078125:float)
--------------------------------------------------
第495行: float3 _12025 = float3(_10557);
语法树结构:
assignment(=:float3)
  declaration(_12025:float3)
  function(float3:unknown)
    variable(_10557:half3)
--------------------------------------------------
第496行: float3 _12120 = float3(fast::clamp(50.0 * _12025.y, 0.0, 1.0)) - _12025;
语法树结构:
assignment(=:float3)
  declaration(_12120:float3)
  operator(-:unknown)
    function(float3:unknown)
      function(fast::clamp:unknown)
        operator(*:unknown)
          literal(50.0:float)
          member_access(_12025.y:float)
        literal(0.0:float)
        literal(1.0:float)
    variable(_12025:float3)
--------------------------------------------------
第497行: float3 _8521 = ((_9698 + (float3(mix(_9179, half3(half(((_11862 * powr(float(half(fast::max(float(_9199 - (_11827 * _11827)), 0.0078125))), _11858)) * 0.15915493667125701904296875) * float(half4(float4(_11970 / powr(max(half4(half(9.9956989288330078125e-05)), _11973 - (_11975 * (-half(dot(_11517, _11622))))), _11982)) * float4(0.0795769989490509033203125)).x))), half3(half(fast::clamp((_11763 * _11763) * float(dot(_11812, _8303)), 0.0, 1.0)))) * _11812) * float3(_11629))) + (((float3(_11812) * ((_12025 + (_12120 * _11756)) * (fast::min(1000.0, (_12062 * _12062) * 0.3183098733425140380859375) * (1.0 / fast::max((_11736 + sqrt((_11736 * (_11736 - (_11736 * _12080))) + _12080)) * (_11919 + sqrt((_11919 * (_11919 - (_11919 * _12080))) + _12080)), _12108))))) * _11629) * float(_11772))) * _10588;
语法树结构:
assignment(=:float3)
  declaration(_8521:float3)
  operator(+:unknown)
    variable(_9698:float3)
    function(float3:unknown)
      function(mix:unknown)
        variable(_9179:half3)
        function(half3:unknown)
          function(half:unknown)
            operator(*:unknown)
              operator(*:unknown)
                operator(*:unknown)
                  variable(_11862:float)
                  function(powr:unknown)
                    function(float:unknown)
                      function(half:unknown)
                        function(fast::max:unknown)
                          function(float:unknown)
                            operator(-:unknown)
                              variable(_9199:half)
                              operator(*:unknown)
                                variable(_11827:half)
                                variable(_11827:half)
                          literal(0.0078125:float)
                    variable(_11858:float)
                literal(0.15915493667125701904296875:float)
              function(float:unknown)
                function(half4:unknown)
                  function(float4:unknown)
                    operator(/:unknown)
                      variable(_11970:half4)
                      function(powr:unknown)
                        function(max:unknown)
                          function(half4:unknown)
                            function(half:unknown)
                              literal(9.9956989288330078125:float)
--------------------------------------------------
第498行: uint _12171 = uint(_Block1.LightDataBuffer[0].x);
语法树结构:
assignment(=:uint)
  declaration(_12171:uint)
  function(uint:unknown)
    member_access(_Block1.LightDataBuffer[0].x:float)
--------------------------------------------------
第499行: half3 _18429;
语法树结构:
declaration(_18429:half3)
--------------------------------------------------
第500行: float3 _18431;
语法树结构:
declaration(_18431:float3)
--------------------------------------------------
第501行: float3 _19685;
语法树结构:
declaration(_19685:float3)
--------------------------------------------------
第502行: float3 _19720;
语法树结构:
declaration(_19720:float3)
--------------------------------------------------
第503行: float3 _19755;
语法树结构:
declaration(_19755:float3)
--------------------------------------------------
第504行: half3 _19825;
语法树结构:
declaration(_19825:half3)
--------------------------------------------------
第505行: _19825 = _18526;
语法树结构:
assignment(=:half3)
  variable(_19825:half3)
  variable(_18526:half3)
--------------------------------------------------
第506行: _19755 = _19185;
语法树结构:
assignment(=:float3)
  variable(_19755:float3)
  variable(_19185:float3)
--------------------------------------------------
第507行: _19720 = _19185;
语法树结构:
assignment(=:float3)
  variable(_19720:float3)
  variable(_19185:float3)
--------------------------------------------------
第508行: _19685 = _19185;
语法树结构:
assignment(=:float3)
  variable(_19685:float3)
  variable(_19185:float3)
--------------------------------------------------
第509行: _18431 = _9698;
语法树结构:
assignment(=:float3)
  variable(_18431:float3)
  variable(_9698:float3)
--------------------------------------------------
第510行: _18429 = _9179;
语法树结构:
assignment(=:half3)
  variable(_18429:half3)
  variable(_9179:half3)
--------------------------------------------------
第511行: half3 _19923;
语法树结构:
declaration(_19923:half3)
--------------------------------------------------
第512行: float3 _19965;
语法树结构:
declaration(_19965:float3)
--------------------------------------------------
第513行: half _20833;
语法树结构:
declaration(_20833:half)
--------------------------------------------------
第514行: float _20848;
语法树结构:
declaration(_20848:float)
--------------------------------------------------
第515行: int _20863;
语法树结构:
declaration(_20863:int)
--------------------------------------------------
第516行: float3 _20893;
语法树结构:
declaration(_20893:float3)
--------------------------------------------------
第517行: float3 _20908;
语法树结构:
declaration(_20908:float3)
--------------------------------------------------
第518行: float3 _20923;
语法树结构:
declaration(_20923:float3)
--------------------------------------------------
第519行: float _20938;
语法树结构:
declaration(_20938:float)
--------------------------------------------------
第520行: half3 _20953;
语法树结构:
declaration(_20953:half3)
--------------------------------------------------
第521行: half _19485;
语法树结构:
declaration(_19485:half)
--------------------------------------------------
第522行: float _19577;
语法树结构:
declaration(_19577:float)
--------------------------------------------------
第523行: int _19613;
语法树结构:
declaration(_19613:int)
--------------------------------------------------
第524行: float _19790;
语法树结构:
declaration(_19790:float)
--------------------------------------------------
第525行: for (uint _18428 = 0u; _18428 < _12171; _19825 = _20953, _19790 = _20938, _19755 = _20923, _19720 = _20908, _19685 = _20893, _19613 = _20863, _19577 = _20848, _19485 = _20833, _18431 = _19965, _18429 = _19923, _18428++)
语法树结构:
assignment(=:uint)
  declaration(_18428:uint)
  literal(0u:int)
--------------------------------------------------
第527行: uint _12181 = _18428 * 4u;
语法树结构:
assignment(=:uint)
  declaration(_12181:uint)
  operator(*:unknown)
    variable(_18428:uint)
    literal(4u:int)
--------------------------------------------------
第528行: int _12188 = int(_12181 + 1u);
语法树结构:
assignment(=:int)
  declaration(_12188:int)
  function(int:unknown)
    operator(+:unknown)
      variable(_12181:uint)
      literal(1u:int)
--------------------------------------------------
第529行: int _12195 = int(_12181 + 2u);
语法树结构:
assignment(=:int)
  declaration(_12195:int)
  function(int:unknown)
    operator(+:unknown)
      variable(_12181:uint)
      literal(2u:int)
--------------------------------------------------
第530行: int _12202 = int(_12181 + 3u);
语法树结构:
assignment(=:int)
  declaration(_12202:int)
  function(int:unknown)
    operator(+:unknown)
      variable(_12181:uint)
      literal(3u:int)
--------------------------------------------------
第531行: int _12209 = int(_12181 + 4u);
语法树结构:
assignment(=:int)
  declaration(_12209:int)
  function(int:unknown)
    operator(+:unknown)
      variable(_12181:uint)
      literal(4u:int)
--------------------------------------------------
第532行: uint _12298 = as_type<uint>(_Block1.LightDataBuffer[_12209].x);
语法树结构:
assignment(=:uint)
  declaration(_12298:uint)
  function(as_type<uint>:unknown)
    member_access(_Block1.LightDataBuffer[_12209].x:float)
--------------------------------------------------
第533行: if (!((_12298 & 2097152u) == 2097152u))
语法树结构:
operator(!:unknown)
  operator(&:unknown)
    variable(_12298:uint)
    literal(2097152u:int)
--------------------------------------------------
第535行: _20953 = _19825;
语法树结构:
assignment(=:half3)
  variable(_20953:half3)
  variable(_19825:half3)
--------------------------------------------------
第536行: _20938 = _19790;
语法树结构:
assignment(=:float)
  variable(_20938:float)
  variable(_19790:float)
--------------------------------------------------
第537行: _20923 = _19755;
语法树结构:
assignment(=:float3)
  variable(_20923:float3)
  variable(_19755:float3)
--------------------------------------------------
第538行: _20908 = _19720;
语法树结构:
assignment(=:float3)
  variable(_20908:float3)
  variable(_19720:float3)
--------------------------------------------------
第539行: _20893 = _19685;
语法树结构:
assignment(=:float3)
  variable(_20893:float3)
  variable(_19685:float3)
--------------------------------------------------
第540行: _20863 = _19613;
语法树结构:
assignment(=:int)
  variable(_20863:int)
  variable(_19613:int)
--------------------------------------------------
第541行: _20848 = _19577;
语法树结构:
assignment(=:float)
  variable(_20848:float)
  variable(_19577:float)
--------------------------------------------------
第542行: _20833 = _19485;
语法树结构:
assignment(=:half)
  variable(_20833:half)
  variable(_19485:half)
--------------------------------------------------
第543行: _19965 = _18431;
语法树结构:
assignment(=:float3)
  variable(_19965:float3)
  variable(_18431:float3)
--------------------------------------------------
第544行: _19923 = _18429;
语法树结构:
assignment(=:half3)
  variable(_19923:half3)
  variable(_18429:half3)
--------------------------------------------------
第545行: continue;
语法树结构:
variable(continue:unknown)
--------------------------------------------------
第547行: uint _12309 = _12298 & 196608u;
语法树结构:
assignment(=:uint)
  declaration(_12309:uint)
  operator(&:unknown)
    variable(_12298:uint)
    literal(196608u:int)
--------------------------------------------------
第548行: half _19481;
语法树结构:
declaration(_19481:half)
--------------------------------------------------
第549行: float _19573;
语法树结构:
declaration(_19573:float)
--------------------------------------------------
第550行: int _19609;
语法树结构:
declaration(_19609:int)
--------------------------------------------------
第551行: float3 _19681;
语法树结构:
declaration(_19681:float3)
--------------------------------------------------
第552行: float3 _19716;
语法树结构:
declaration(_19716:float3)
--------------------------------------------------
第553行: float3 _19751;
语法树结构:
declaration(_19751:float3)
--------------------------------------------------
第554行: float _19786;
语法树结构:
declaration(_19786:float)
--------------------------------------------------
第555行: half3 _19821;
语法树结构:
declaration(_19821:half3)
--------------------------------------------------
第556行: if (_12309 == 196608u)
语法树结构:
operator(==:unknown)
  variable(_12309:uint)
  literal(196608u:int)
--------------------------------------------------
第558行: float3 _12360 = -_Block1.LightDataBuffer[_12202].xyz;
语法树结构:
assignment(=:float3)
  declaration(_12360:float3)
  operator(-:unknown)
    member_access(_Block1.LightDataBuffer[_12202].xyz:float3)
--------------------------------------------------
第559行: float3 _12378 = in.IN_WorldPosition.xyz - (_Block1.LightDataBuffer[_12188].xyz + (_12360 * (dot(in.IN_WorldPosition.xyz - _Block1.LightDataBuffer[_12188].xyz, _12360) / dot(_12360, _12360))));
语法树结构:
assignment(=:float3)
  declaration(_12378:float3)
  operator(-:unknown)
    member_access(in.IN_WorldPosition.xyz:float3)
    operator(+:unknown)
      member_access(_Block1.LightDataBuffer[_12188].xyz:float3)
      operator(*:unknown)
        variable(_12360:float3)
        operator(/:unknown)
          function(dot:unknown)
            operator(-:unknown)
              member_access(in.IN_WorldPosition.xyz:float3)
              member_access(_Block1.LightDataBuffer[_12188].xyz:float3)
            variable(_12360:float3)
          function(dot:unknown)
            variable(_12360:float3)
            variable(_12360:float3)
--------------------------------------------------
第560行: float _12381 = dot(_12378, _12378);
语法树结构:
assignment(=:float)
  declaration(_12381:float)
  function(dot:unknown)
    variable(_12378:float3)
    variable(_12378:float3)
--------------------------------------------------
第561行: float _19350;
语法树结构:
declaration(_19350:float)
--------------------------------------------------
第562行: if (_12381 > (_Block1.LightDataBuffer[_12209].y * _Block1.LightDataBuffer[_12209].y))
语法树结构:
operator(>:unknown)
  variable(_12381:float)
  operator(*:unknown)
    member_access(_Block1.LightDataBuffer[_12209].y:float)
    member_access(_Block1.LightDataBuffer[_12209].y:float)
--------------------------------------------------
第564行: float _12392 = sqrt(_12381) - _Block1.LightDataBuffer[_12209].y;
语法树结构:
assignment(=:float)
  declaration(_12392:float)
  operator(-:unknown)
    function(sqrt:unknown)
      variable(_12381:float)
    member_access(_Block1.LightDataBuffer[_12209].y:float)
--------------------------------------------------
第565行: float _12395 = _12392 * _12392;
语法树结构:
assignment(=:float)
  declaration(_12395:float)
  operator(*:unknown)
    variable(_12392:float)
    variable(_12392:float)
--------------------------------------------------
第566行: float _12398 = _12395 * abs(_Block1.LightDataBuffer[_12188].w);
语法树结构:
assignment(=:float)
  declaration(_12398:float)
  operator(*:unknown)
    variable(_12395:float)
    function(abs:unknown)
      member_access(_Block1.LightDataBuffer[_12188].w:float)
--------------------------------------------------
第567行: float _12404 = fast::clamp(1.0 - (_12398 * _12398), 0.0, 1.0);
语法树结构:
assignment(=:float)
  declaration(_12404:float)
  function(fast::clamp:unknown)
    operator(-:unknown)
      literal(1.0:float)
      operator(*:unknown)
        variable(_12398:float)
        variable(_12398:float)
    literal(0.0:float)
    literal(1.0:float)
--------------------------------------------------
第568行: _19350 = fast::min(100.0, (_12404 * _12404) / (_12395 + 1.0));
语法树结构:
assignment(=:float)
  variable(_19350:float)
  function(fast::min:unknown)
    literal(100.0:float)
    operator(/:unknown)
      operator(*:unknown)
        variable(_12404:float)
        variable(_12404:float)
      operator(+:unknown)
        variable(_12395:float)
        literal(1.0:float)
--------------------------------------------------
第572行: _19350 = 1.0;
语法树结构:
assignment(=:float)
  variable(_19350:float)
  literal(1.0:float)
--------------------------------------------------
第574行: _19821 = half3(_Block1.LightDataBuffer[_12195].xyz);
语法树结构:
assignment(=:half3)
  variable(_19821:half3)
  function(half3:unknown)
    member_access(_Block1.LightDataBuffer[_12195].xyz:float3)
--------------------------------------------------
第575行: _19786 = _19350;
语法树结构:
assignment(=:float)
  variable(_19786:float)
  variable(_19350:float)
--------------------------------------------------
第576行: _19751 = _12360;
语法树结构:
assignment(=:float3)
  variable(_19751:float3)
  variable(_12360:float3)
--------------------------------------------------
第577行: _19716 = _11517;
语法树结构:
assignment(=:float3)
  variable(_19716:float3)
  variable(_11517:float3)
--------------------------------------------------
第578行: _19681 = _9109;
语法树结构:
assignment(=:float3)
  variable(_19681:float3)
  variable(_9109:float3)
--------------------------------------------------
第579行: _19609 = 0;
语法树结构:
assignment(=:int)
  variable(_19609:int)
  literal(0:int)
--------------------------------------------------
第580行: _19573 = abs(_Block1.LightDataBuffer[_12195].w);
语法树结构:
assignment(=:float)
  variable(_19573:float)
  function(abs:unknown)
    member_access(_Block1.LightDataBuffer[_12195].w:float)
--------------------------------------------------
第581行: _19481 = clamp(half(dot(_9109, _12360)), half(0.0), half(1.0));
语法树结构:
assignment(=:half)
  variable(_19481:half)
  function(clamp:unknown)
    function(half:unknown)
      function(dot:unknown)
        variable(_9109:float3)
        variable(_12360:float3)
    function(half:unknown)
      literal(0.0:float)
    function(half:unknown)
      literal(1.0:float)
--------------------------------------------------
第585行: half _19482;
语法树结构:
declaration(_19482:half)
--------------------------------------------------
第586行: float _19574;
语法树结构:
declaration(_19574:float)
--------------------------------------------------
第587行: int _19610;
语法树结构:
declaration(_19610:int)
--------------------------------------------------
第588行: float3 _19682;
语法树结构:
declaration(_19682:float3)
--------------------------------------------------
第589行: float3 _19717;
语法树结构:
declaration(_19717:float3)
--------------------------------------------------
第590行: float3 _19752;
语法树结构:
declaration(_19752:float3)
--------------------------------------------------
第591行: float _19787;
语法树结构:
declaration(_19787:float)
--------------------------------------------------
第592行: half3 _19822;
语法树结构:
declaration(_19822:half3)
--------------------------------------------------
第593行: if (_12309 == 0u)
语法树结构:
operator(==:unknown)
  variable(_12309:uint)
  literal(0u:int)
--------------------------------------------------
第595行: uint _12741 = as_type<uint>(_Block1.LightDataBuffer[_12195].w);
语法树结构:
assignment(=:uint)
  declaration(_12741:uint)
  function(as_type<uint>:unknown)
    member_access(_Block1.LightDataBuffer[_12195].w:float)
--------------------------------------------------
第596行: float _12858 = float((_12741 >> 0u) & 65535u) * 0.0001525902189314365386962890625;
语法树结构:
assignment(=:float)
  declaration(_12858:float)
  operator(*:unknown)
    function(float:unknown)
      operator(&:unknown)
        operator(>>:unknown)
          variable(_12741:uint)
          literal(0u:int)
        literal(65535u:int)
    literal(0.0001525902189314365386962890625:float)
--------------------------------------------------
第597行: float3 _12599 = _Block1.LightDataBuffer[_12188].xyz - in.IN_WorldPosition.xyz;
语法树结构:
assignment(=:float3)
  declaration(_12599:float3)
  operator(-:unknown)
    member_access(_Block1.LightDataBuffer[_12188].xyz:float3)
    member_access(in.IN_WorldPosition.xyz:float3)
--------------------------------------------------
第598行: float _12602 = dot(_12599, _12599);
语法树结构:
assignment(=:float)
  declaration(_12602:float)
  function(dot:unknown)
    variable(_12599:float3)
    variable(_12599:float3)
--------------------------------------------------
第599行: float3 _12606 = _12599 * rsqrt(_12602);
语法树结构:
assignment(=:float3)
  declaration(_12606:float3)
  operator(*:unknown)
    variable(_12599:float3)
    function(rsqrt:unknown)
      variable(_12602:float)
--------------------------------------------------
第600行: float _12613 = _12602 * abs(_Block1.LightDataBuffer[_12188].w);
语法树结构:
assignment(=:float)
  declaration(_12613:float)
  operator(*:unknown)
    variable(_12602:float)
    function(abs:unknown)
      member_access(_Block1.LightDataBuffer[_12188].w:float)
--------------------------------------------------
第601行: float _12620 = fast::clamp(1.0 - (_12613 * _12613), 0.0, 1.0);
语法树结构:
assignment(=:float)
  declaration(_12620:float)
  function(fast::clamp:unknown)
    operator(-:unknown)
      literal(1.0:float)
      operator(*:unknown)
        variable(_12613:float)
        variable(_12613:float)
    literal(0.0:float)
    literal(1.0:float)
--------------------------------------------------
第602行: float _12631 = _12620 * _12620;
语法树结构:
assignment(=:float)
  declaration(_12631:float)
  operator(*:unknown)
    variable(_12620:float)
    variable(_12620:float)
--------------------------------------------------
第603行: float _19213;
语法树结构:
declaration(_19213:float)
--------------------------------------------------
第604行: if ((_12298 & 16777216u) == 16777216u)
语法树结构:
operator(&:unknown)
  variable(_12298:uint)
  literal(16777216u:int)
--------------------------------------------------
第606行: float _12641 = _12631 / ((_12602 * _Block1.LightDataBuffer[_12202].w) + 9.9999997473787516355514526367188e-05);
语法树结构:
assignment(=:float)
  declaration(_12641:float)
  operator(/:unknown)
    variable(_12631:float)
    operator(+:unknown)
      operator(*:unknown)
        variable(_12602:float)
        member_access(_Block1.LightDataBuffer[_12202].w:float)
      literal(9.9999997473787516355514526367188:float)
--------------------------------------------------
第607行: float _19211;
语法树结构:
declaration(_19211:float)
--------------------------------------------------
第608行: if (_12858 > 0.00999999977648258209228515625)
语法树结构:
operator(>:unknown)
  variable(_12858:float)
  literal(0.00999999977648258209228515625:float)
--------------------------------------------------
第610行: _19211 = fast::min(_12641, _12858);
语法树结构:
assignment(=:float)
  variable(_19211:float)
  function(fast::min:unknown)
    variable(_12641:float)
    variable(_12858:float)
--------------------------------------------------
第614行: _19211 = _12641;
语法树结构:
assignment(=:float)
  variable(_19211:float)
  variable(_12641:float)
--------------------------------------------------
第616行: _19213 = fast::min(100.0, _19211);
语法树结构:
assignment(=:float)
  variable(_19213:float)
  function(fast::min:unknown)
    literal(100.0:float)
    variable(_19211:float)
--------------------------------------------------
第620行: _19213 = _12631 * 0.100000001490116119384765625;
语法树结构:
assignment(=:float)
  variable(_19213:float)
  operator(*:unknown)
    variable(_12631:float)
    literal(0.100000001490116119384765625:float)
--------------------------------------------------
第622行: float _12677 = fast::clamp((dot(_Block1.LightDataBuffer[_12202].xyz, -_12606) - _Block1.LightDataBuffer[_12209].z) * _Block1.LightDataBuffer[_12209].y, 0.0, 1.0);
语法树结构:
assignment(=:float)
  declaration(_12677:float)
  function(fast::clamp:unknown)
    operator(*:unknown)
      operator(-:unknown)
        function(dot:unknown)
          member_access(_Block1.LightDataBuffer[_12202].xyz:float3)
          operator(-:unknown)
            variable(_12606:float3)
        member_access(_Block1.LightDataBuffer[_12209].z:float)
      member_access(_Block1.LightDataBuffer[_12209].y:float)
    literal(0.0:float)
    literal(1.0:float)
--------------------------------------------------
第623行: _19822 = half3(fast::min(float3(3000.0), _Block1.LightDataBuffer[_12195].xyz * _19213));
语法树结构:
assignment(=:half3)
  variable(_19822:half3)
  function(half3:unknown)
    function(fast::min:unknown)
      function(float3:unknown)
        literal(3000.0:float)
      operator(*:unknown)
        member_access(_Block1.LightDataBuffer[_12195].xyz:float3)
        variable(_19213:float)
--------------------------------------------------
第624行: _19787 = _12677 * _12677;
语法树结构:
assignment(=:float)
  variable(_19787:float)
  operator(*:unknown)
    variable(_12677:float)
    variable(_12677:float)
--------------------------------------------------
第625行: _19752 = _12606;
语法树结构:
assignment(=:float3)
  variable(_19752:float3)
  variable(_12606:float3)
--------------------------------------------------
第626行: _19717 = _11517;
语法树结构:
assignment(=:float3)
  variable(_19717:float3)
  variable(_11517:float3)
--------------------------------------------------
第627行: _19682 = _9109;
语法树结构:
assignment(=:float3)
  variable(_19682:float3)
  variable(_9109:float3)
--------------------------------------------------
第628行: _19610 = 0;
语法树结构:
assignment(=:int)
  variable(_19610:int)
  literal(0:int)
--------------------------------------------------
第629行: _19574 = float((_12741 >> 16u) & 65535u) * 0.001525902189314365386962890625;
语法树结构:
assignment(=:float)
  variable(_19574:float)
  operator(*:unknown)
    function(float:unknown)
      operator(&:unknown)
        operator(>>:unknown)
          variable(_12741:uint)
          literal(16u:int)
        literal(65535u:int)
    literal(0.001525902189314365386962890625:float)
--------------------------------------------------
第630行: _19482 = clamp(half(dot(_9109, _12606)), half(0.0), half(1.0));
语法树结构:
assignment(=:half)
  variable(_19482:half)
  function(clamp:unknown)
    function(half:unknown)
      function(dot:unknown)
        variable(_9109:float3)
        variable(_12606:float3)
    function(half:unknown)
      literal(0.0:float)
    function(half:unknown)
      literal(1.0:float)
--------------------------------------------------
第634行: half _19483;
语法树结构:
declaration(_19483:half)
--------------------------------------------------
第635行: float _19575;
语法树结构:
declaration(_19575:float)
--------------------------------------------------
第636行: int _19611;
语法树结构:
declaration(_19611:int)
--------------------------------------------------
第637行: float3 _19683;
语法树结构:
declaration(_19683:float3)
--------------------------------------------------
第638行: float3 _19718;
语法树结构:
declaration(_19718:float3)
--------------------------------------------------
第639行: float3 _19753;
语法树结构:
declaration(_19753:float3)
--------------------------------------------------
第640行: float _19788;
语法树结构:
declaration(_19788:float)
--------------------------------------------------
第641行: half3 _19823;
语法树结构:
declaration(_19823:half3)
--------------------------------------------------
第642行: if (_12309 == 65536u)
语法树结构:
operator(==:unknown)
  variable(_12309:uint)
  literal(65536u:int)
--------------------------------------------------
第644行: uint _13098 = as_type<uint>(_Block1.LightDataBuffer[_12195].w);
语法树结构:
assignment(=:uint)
  declaration(_13098:uint)
  function(as_type<uint>:unknown)
    member_access(_Block1.LightDataBuffer[_12195].w:float)
--------------------------------------------------
第645行: float3 _12933 = _Block1.LightDataBuffer[_12188].xyz - in.IN_WorldPosition.xyz;
语法树结构:
assignment(=:float3)
  declaration(_12933:float3)
  operator(-:unknown)
    member_access(_Block1.LightDataBuffer[_12188].xyz:float3)
    member_access(in.IN_WorldPosition.xyz:float3)
--------------------------------------------------
第646行: float _12936 = dot(_12933, _12933);
语法树结构:
assignment(=:float)
  declaration(_12936:float)
  function(dot:unknown)
    variable(_12933:float3)
    variable(_12933:float3)
--------------------------------------------------
第647行: float3 _12942 = _12933 / float3(sqrt(_12936));
语法树结构:
assignment(=:float3)
  declaration(_12942:float3)
  operator(/:unknown)
    variable(_12933:float3)
    function(float3:unknown)
      function(sqrt:unknown)
        variable(_12936:float)
--------------------------------------------------
第648行: float _12950 = _12936 * abs(_Block1.LightDataBuffer[_12188].w);
语法树结构:
assignment(=:float)
  declaration(_12950:float)
  operator(*:unknown)
    variable(_12936:float)
    function(abs:unknown)
      member_access(_Block1.LightDataBuffer[_12188].w:float)
--------------------------------------------------
第649行: float _12956 = fast::clamp(1.0 - (_12950 * _12950), 0.0, 1.0);
语法树结构:
assignment(=:float)
  declaration(_12956:float)
  function(fast::clamp:unknown)
    operator(-:unknown)
      literal(1.0:float)
      operator(*:unknown)
        variable(_12950:float)
        variable(_12950:float)
    literal(0.0:float)
    literal(1.0:float)
--------------------------------------------------
第650行: float _12972 = fast::min(100.0, (_12956 * _12956) / ((_12936 * _Block1.LightDataBuffer[_12209].w) + 9.9999997473787516355514526367188e-05));
语法树结构:
assignment(=:float)
  declaration(_12972:float)
  function(fast::min:unknown)
    literal(100.0:float)
    operator(/:unknown)
      operator(*:unknown)
        variable(_12956:float)
        variable(_12956:float)
      operator(+:unknown)
        operator(*:unknown)
          variable(_12936:float)
          member_access(_Block1.LightDataBuffer[_12209].w:float)
        literal(9.9999997473787516355514526367188:float)
--------------------------------------------------
第651行: float _13202 = float((_13098 >> 0u) & 65535u) * 0.0001525902189314365386962890625;
语法树结构:
assignment(=:float)
  declaration(_13202:float)
  operator(*:unknown)
    function(float:unknown)
      operator(&:unknown)
        operator(>>:unknown)
          variable(_13098:uint)
          literal(0u:int)
        literal(65535u:int)
    literal(0.0001525902189314365386962890625:float)
--------------------------------------------------
第652行: float _19070;
语法树结构:
declaration(_19070:float)
--------------------------------------------------
第653行: if (_13202 > 0.00999999977648258209228515625)
语法树结构:
operator(>:unknown)
  variable(_13202:float)
  literal(0.00999999977648258209228515625:float)
--------------------------------------------------
第655行: _19070 = fast::min(_12972, _13202);
语法树结构:
assignment(=:float)
  variable(_19070:float)
  function(fast::min:unknown)
    variable(_12972:float)
    variable(_13202:float)
--------------------------------------------------
第659行: _19070 = _12972;
语法树结构:
assignment(=:float)
  variable(_19070:float)
  variable(_12972:float)
--------------------------------------------------
第661行: _19823 = half3(fast::min(float3(3000.0), _Block1.LightDataBuffer[_12195].xyz * _19070) * (((_12298 & 16777216u) == 16777216u) ? _Block1.TimeOfDayInfos.y : 1.0));
语法树结构:
assignment(=:half3)
  variable(_19823:half3)
  function(half3:unknown)
    operator(*:unknown)
      function(fast::min:unknown)
        function(float3:unknown)
          literal(3000.0:float)
        operator(*:unknown)
          member_access(_Block1.LightDataBuffer[_12195].xyz:float3)
          variable(_19070:float)
      operator(?::unknown)
        operator(==:unknown)
          operator(&:unknown)
            variable(_12298:uint)
            literal(16777216u:int)
          literal(16777216u:int)
        member_access(_Block1.TimeOfDayInfos.y:float)
        literal(1.0:float)
--------------------------------------------------
第662行: _19788 = 1.0;
语法树结构:
assignment(=:float)
  variable(_19788:float)
  literal(1.0:float)
--------------------------------------------------
第663行: _19753 = _12942;
语法树结构:
assignment(=:float3)
  variable(_19753:float3)
  variable(_12942:float3)
--------------------------------------------------
第664行: _19718 = _11517;
语法树结构:
assignment(=:float3)
  variable(_19718:float3)
  variable(_11517:float3)
--------------------------------------------------
第665行: _19683 = _9109;
语法树结构:
assignment(=:float3)
  variable(_19683:float3)
  variable(_9109:float3)
--------------------------------------------------
第666行: _19611 = 0;
语法树结构:
assignment(=:int)
  variable(_19611:int)
  literal(0:int)
--------------------------------------------------
第667行: _19575 = float((_13098 >> 16u) & 65535u) * 0.001525902189314365386962890625;
语法树结构:
assignment(=:float)
  variable(_19575:float)
  operator(*:unknown)
    function(float:unknown)
      operator(&:unknown)
        operator(>>:unknown)
          variable(_13098:uint)
          literal(16u:int)
        literal(65535u:int)
    literal(0.001525902189314365386962890625:float)
--------------------------------------------------
第668行: _19483 = ((_12298 & 262144u) == 262144u) ? _9019 : half(fast::clamp(dot(_9109, _12942), 0.0, 1.0));
语法树结构:
assignment(=:half)
  variable(_19483:half)
  operator(?::unknown)
    operator(==:unknown)
      operator(&:unknown)
        variable(_12298:uint)
        literal(262144u:int)
      literal(262144u:int)
    variable(_9019:half)
    function(half:unknown)
      function(fast::clamp:unknown)
        function(dot:unknown)
          variable(_9109:float3)
          variable(_12942:float3)
        literal(0.0:float)
        literal(1.0:float)
--------------------------------------------------
第672行: bool _13270 = _12309 == 131072u;
语法树结构:
assignment(=:bool)
  declaration(_13270:bool)
  operator(==:unknown)
    variable(_12309:uint)
    literal(131072u:int)
--------------------------------------------------
第673行: half _19484;
语法树结构:
declaration(_19484:half)
--------------------------------------------------
第674行: float3 _19684;
语法树结构:
declaration(_19684:float3)
--------------------------------------------------
第675行: float3 _19754;
语法树结构:
declaration(_19754:float3)
--------------------------------------------------
第676行: float _19789;
语法树结构:
declaration(_19789:float)
--------------------------------------------------
第677行: half3 _19824;
语法树结构:
declaration(_19824:half3)
--------------------------------------------------
第678行: if (_13270)
语法树结构:
variable(_13270:bool)
--------------------------------------------------
第680行: float3 _13339 = _Block1.LightDataBuffer[_12188].xyz - in.IN_WorldPosition.xyz;
语法树结构:
assignment(=:float3)
  declaration(_13339:float3)
  operator(-:unknown)
    member_access(_Block1.LightDataBuffer[_12188].xyz:float3)
    member_access(in.IN_WorldPosition.xyz:float3)
--------------------------------------------------
第681行: float _13342 = dot(_13339, _13339);
语法树结构:
assignment(=:float)
  declaration(_13342:float)
  function(dot:unknown)
    variable(_13339:float3)
    variable(_13339:float3)
--------------------------------------------------
第682行: float _13348 = _13342 * abs(_Block1.LightDataBuffer[_12188].w);
语法树结构:
assignment(=:float)
  declaration(_13348:float)
  operator(*:unknown)
    variable(_13342:float)
    function(abs:unknown)
      member_access(_Block1.LightDataBuffer[_12188].w:float)
--------------------------------------------------
第683行: float _13356 = fast::clamp(1.0 - (_13348 * _13348), 0.0, 1.0);
语法树结构:
assignment(=:float)
  declaration(_13356:float)
  function(fast::clamp:unknown)
    operator(-:unknown)
      literal(1.0:float)
      operator(*:unknown)
        variable(_13348:float)
        variable(_13348:float)
    literal(0.0:float)
    literal(1.0:float)
--------------------------------------------------
第684行: float3 _13433 = fast::normalize(_11517 - (_9109 * _11925));
语法树结构:
assignment(=:float3)
  declaration(_13433:float3)
  function(fast::normalize:unknown)
    operator(-:unknown)
      variable(_11517:float3)
      operator(*:unknown)
        variable(_9109:float3)
        variable(_11925:float)
--------------------------------------------------
第685行: float3x3 _13459 = float3x3(_13433, cross(_9109, _13433), _9109);
语法树结构:
assignment(=:float3x3)
  declaration(_13459:float3x3)
  function(float3x3:unknown)
    variable(_13433:float3)
    function(cross:unknown)
      variable(_9109:float3)
      variable(_13433:float3)
    variable(_9109:float3)
--------------------------------------------------
第686行: float3 _13466 = float3(_Block1.LightDataBuffer[_12202].xyz) * _Block1.LightDataBuffer[_12195].w;
语法树结构:
assignment(=:float3)
  declaration(_13466:float3)
  operator(*:unknown)
    function(float3:unknown)
      member_access(_Block1.LightDataBuffer[_12202].xyz:float3)
    member_access(_Block1.LightDataBuffer[_12195].w:float)
--------------------------------------------------
第687行: float3 _13467 = _13339 - _13466;
语法树结构:
assignment(=:float3)
  declaration(_13467:float3)
  operator(-:unknown)
    variable(_13339:float3)
    variable(_13466:float3)
--------------------------------------------------
第688行: float3 _13472 = float3(_Block1.LightDataBuffer[_12209].yzw) * _Block1.LightDataBuffer[_12202].w;
语法树结构:
assignment(=:float3)
  declaration(_13472:float3)
  operator(*:unknown)
    function(float3:unknown)
      member_access(_Block1.LightDataBuffer[_12209].yzw:float3)
    member_access(_Block1.LightDataBuffer[_12202].w:float)
--------------------------------------------------
第689行: float3 _13484 = _13339 + _13466;
语法树结构:
assignment(=:float3)
  declaration(_13484:float3)
  operator(+:unknown)
    variable(_13339:float3)
    variable(_13466:float3)
--------------------------------------------------
第690行: float3 _13657 = fast::normalize((_13467 - _13472) * _13459);
语法树结构:
assignment(=:float3)
  declaration(_13657:float3)
  function(fast::normalize:unknown)
    operator(*:unknown)
      operator(-:unknown)
        variable(_13467:float3)
        variable(_13472:float3)
      variable(_13459:float3x3)
--------------------------------------------------
第691行: float3 _13660 = fast::normalize((_13484 - _13472) * _13459);
语法树结构:
assignment(=:float3)
  declaration(_13660:float3)
  function(fast::normalize:unknown)
    operator(*:unknown)
      operator(-:unknown)
        variable(_13484:float3)
        variable(_13472:float3)
      variable(_13459:float3x3)
--------------------------------------------------
第692行: float3 _13663 = fast::normalize((_13484 + _13472) * _13459);
语法树结构:
assignment(=:float3)
  declaration(_13663:float3)
  function(fast::normalize:unknown)
    operator(*:unknown)
      operator(+:unknown)
        variable(_13484:float3)
        variable(_13472:float3)
      variable(_13459:float3x3)
--------------------------------------------------
第693行: float3 _13666 = fast::normalize((_13467 + _13472) * _13459);
语法树结构:
assignment(=:float3)
  declaration(_13666:float3)
  function(fast::normalize:unknown)
    operator(*:unknown)
      operator(+:unknown)
        variable(_13467:float3)
        variable(_13472:float3)
      variable(_13459:float3x3)
--------------------------------------------------
第694行: float _13712 = dot(_13657, _13660);
语法树结构:
assignment(=:float)
  declaration(_13712:float)
  function(dot:unknown)
    variable(_13657:float3)
    variable(_13660:float3)
--------------------------------------------------
第695行: float _13714 = abs(_13712);
语法树结构:
assignment(=:float)
  declaration(_13714:float)
  function(abs:unknown)
    variable(_13712:float)
--------------------------------------------------
第696行: float _13728 = (0.8543984889984130859375 + ((0.4965155124664306640625 + (0.01452060043811798095703125 * _13714)) * _13714)) / (3.41759395599365234375 + ((4.1616725921630859375 + _13714) * _13714));
语法树结构:
assignment(=:float)
  declaration(_13728:float)
  operator(/:unknown)
    operator(+:unknown)
      literal(0.8543984889984130859375:float)
      operator(*:unknown)
        operator(+:unknown)
          literal(0.4965155124664306640625:float)
          operator(*:unknown)
            literal(0.01452060043811798095703125:float)
            variable(_13714:float)
        variable(_13714:float)
    operator(+:unknown)
      literal(3.41759395599365234375:float)
      operator(*:unknown)
        operator(+:unknown)
          literal(4.1616725921630859375:float)
          variable(_13714:float)
        variable(_13714:float)
--------------------------------------------------
第697行: float _13753 = dot(_13660, _13663);
语法树结构:
assignment(=:float)
  declaration(_13753:float)
  function(dot:unknown)
    variable(_13660:float3)
    variable(_13663:float3)
--------------------------------------------------
第698行: float _13755 = abs(_13753);
语法树结构:
assignment(=:float)
  declaration(_13755:float)
  function(abs:unknown)
    variable(_13753:float)
--------------------------------------------------
第699行: float _13769 = (0.8543984889984130859375 + ((0.4965155124664306640625 + (0.01452060043811798095703125 * _13755)) * _13755)) / (3.41759395599365234375 + ((4.1616725921630859375 + _13755) * _13755));
语法树结构:
assignment(=:float)
  declaration(_13769:float)
  operator(/:unknown)
    operator(+:unknown)
      literal(0.8543984889984130859375:float)
      operator(*:unknown)
        operator(+:unknown)
          literal(0.4965155124664306640625:float)
          operator(*:unknown)
            literal(0.01452060043811798095703125:float)
            variable(_13755:float)
        variable(_13755:float)
    operator(+:unknown)
      literal(3.41759395599365234375:float)
      operator(*:unknown)
        operator(+:unknown)
          literal(4.1616725921630859375:float)
          variable(_13755:float)
        variable(_13755:float)
--------------------------------------------------
第700行: float _13794 = dot(_13663, _13666);
语法树结构:
assignment(=:float)
  declaration(_13794:float)
  function(dot:unknown)
    variable(_13663:float3)
    variable(_13666:float3)
--------------------------------------------------
第701行: float _13796 = abs(_13794);
语法树结构:
assignment(=:float)
  declaration(_13796:float)
  function(abs:unknown)
    variable(_13794:float)
--------------------------------------------------
第702行: float _13810 = (0.8543984889984130859375 + ((0.4965155124664306640625 + (0.01452060043811798095703125 * _13796)) * _13796)) / (3.41759395599365234375 + ((4.1616725921630859375 + _13796) * _13796));
语法树结构:
assignment(=:float)
  declaration(_13810:float)
  operator(/:unknown)
    operator(+:unknown)
      literal(0.8543984889984130859375:float)
      operator(*:unknown)
        operator(+:unknown)
          literal(0.4965155124664306640625:float)
          operator(*:unknown)
            literal(0.01452060043811798095703125:float)
            variable(_13796:float)
        variable(_13796:float)
    operator(+:unknown)
      literal(3.41759395599365234375:float)
      operator(*:unknown)
        operator(+:unknown)
          literal(4.1616725921630859375:float)
          variable(_13796:float)
        variable(_13796:float)
--------------------------------------------------
第703行: float _13835 = dot(_13666, _13657);
语法树结构:
assignment(=:float)
  declaration(_13835:float)
  function(dot:unknown)
    variable(_13666:float3)
    variable(_13657:float3)
--------------------------------------------------
第704行: float _13837 = abs(_13835);
语法树结构:
assignment(=:float)
  declaration(_13837:float)
  function(abs:unknown)
    variable(_13835:float)
--------------------------------------------------
第705行: float _13851 = (0.8543984889984130859375 + ((0.4965155124664306640625 + (0.01452060043811798095703125 * _13837)) * _13837)) / (3.41759395599365234375 + ((4.1616725921630859375 + _13837) * _13837));
语法树结构:
assignment(=:float)
  declaration(_13851:float)
  operator(/:unknown)
    operator(+:unknown)
      literal(0.8543984889984130859375:float)
      operator(*:unknown)
        operator(+:unknown)
          literal(0.4965155124664306640625:float)
          operator(*:unknown)
            literal(0.01452060043811798095703125:float)
            variable(_13837:float)
        variable(_13837:float)
    operator(+:unknown)
      literal(3.41759395599365234375:float)
      operator(*:unknown)
        operator(+:unknown)
          literal(4.1616725921630859375:float)
          variable(_13837:float)
        variable(_13837:float)
--------------------------------------------------
第706行: float3 _13700 = cross(_13660, (_13657 * (-((_13712 > 0.0) ? _13728 : ((0.5 * rsqrt(fast::max(1.0 - (_13712 * _13712), 1.0000000116860974230803549289703e-07))) - _13728)))) + (_13663 * ((_13753 > 0.0) ? _13769 : ((0.5 * rsqrt(fast::max(1.0 - (_13753 * _13753), 1.0000000116860974230803549289703e-07))) - _13769)))) + cross(_13666, (_13657 * ((_13835 > 0.0) ? _13851 : ((0.5 * rsqrt(fast::max(1.0 - (_13835 * _13835), 1.0000000116860974230803549289703e-07))) - _13851))) + (_13663 * (-((_13794 > 0.0) ? _13810 : ((0.5 * rsqrt(fast::max(1.0 - (_13794 * _13794), 1.0000000116860974230803549289703e-07))) - _13810)))));
语法树结构:
assignment(=:float3)
  declaration(_13700:float3)
  function(cross:unknown)
    variable(_13660:float3)
    operator(*:unknown)
      variable(_13657:float3)
      operator(-:unknown)
        operator(?::unknown)
          operator(>:unknown)
            variable(_13712:float)
            literal(0.0:float)
          variable(_13728:float)
          operator(*:unknown)
            literal(0.5:float)
            function(rsqrt:unknown)
              function(fast::max:unknown)
                operator(-:unknown)
                  literal(1.0:float)
                  operator(*:unknown)
                    variable(_13712:float)
                    variable(_13712:float)
                literal(1.0000000116860974230803549289703:float)
--------------------------------------------------
第707行: float _13531 = length(_13700);
语法树结构:
assignment(=:float)
  declaration(_13531:float)
  function(length:unknown)
    variable(_13700:float3)
--------------------------------------------------
第708行: float _13539 = step(0.0, dot(cross(_Block1.LightDataBuffer[_12202].xyz, _Block1.LightDataBuffer[_12209].yzw), _13339));
语法树结构:
assignment(=:float)
  declaration(_13539:float)
  function(step:unknown)
    literal(0.0:float)
    function(dot:unknown)
      function(cross:unknown)
        member_access(_Block1.LightDataBuffer[_12202].xyz:float3)
        member_access(_Block1.LightDataBuffer[_12209].yzw:float3)
      variable(_13339:float3)
--------------------------------------------------
第709行: _19824 = half3(_Block1.LightDataBuffer[_12195].xyz * (_13356 * _13356));
语法树结构:
assignment(=:half3)
  variable(_19824:half3)
  function(half3:unknown)
    operator(*:unknown)
      member_access(_Block1.LightDataBuffer[_12195].xyz:float3)
      operator(*:unknown)
        variable(_13356:float)
        variable(_13356:float)
--------------------------------------------------
第710行: _19789 = ((!((_12298 & 67108864u) == 67108864u)) && (_13539 > 0.0)) ? 0.0 : _13531;
语法树结构:
assignment(=:float)
  variable(_19789:float)
  operator(?::unknown)
    operator(&&:unknown)
      operator(!:unknown)
        operator(==:unknown)
          operator(&:unknown)
            variable(_12298:uint)
            literal(67108864u:int)
          literal(67108864u:int)
      operator(>:unknown)
        variable(_13539:float)
        literal(0.0:float)
    literal(0.0:float)
    variable(_13531:float)
--------------------------------------------------
第711行: _19754 = _13339 * rsqrt(_13342);
语法树结构:
assignment(=:float3)
  variable(_19754:float3)
  operator(*:unknown)
    variable(_13339:float3)
    function(rsqrt:unknown)
      variable(_13342:float)
--------------------------------------------------
第712行: _19684 = _9109;
语法树结构:
assignment(=:float3)
  variable(_19684:float3)
  variable(_9109:float3)
--------------------------------------------------
第713行: _19484 = half(fast::max(((_13531 * _13531) + ((_13700 / float3(_13531)).z * ((_13539 * 2.0) - 1.0))) / (_13531 + 1.0), 0.0));
语法树结构:
assignment(=:half)
  variable(_19484:half)
  function(half:unknown)
    function(fast::max:unknown)
      operator(/:unknown)
        operator(+:unknown)
          operator(*:unknown)
            variable(_13531:float)
            variable(_13531:float)
          operator(*:unknown)
            member_access(/.z:unknown)
              operator(/:unknown)
                variable(_13700:float3)
                function(float3:unknown)
                  variable(_13531:float)
            operator(-:unknown)
              operator(*:unknown)
                variable(_13539:float)
                literal(2.0:float)
              literal(1.0:float)
        operator(+:unknown)
          variable(_13531:float)
          literal(1.0:float)
      literal(0.0:float)
--------------------------------------------------
第717行: _19824 = _19825;
语法树结构:
assignment(=:half3)
  variable(_19824:half3)
  variable(_19825:half3)
--------------------------------------------------
第718行: _19789 = _19790;
语法树结构:
assignment(=:float)
  variable(_19789:float)
  variable(_19790:float)
--------------------------------------------------
第719行: _19754 = _19755;
语法树结构:
assignment(=:float3)
  variable(_19754:float3)
  variable(_19755:float3)
--------------------------------------------------
第720行: _19684 = _19685;
语法树结构:
assignment(=:float3)
  variable(_19684:float3)
  variable(_19685:float3)
--------------------------------------------------
第721行: _19484 = _19485;
语法树结构:
assignment(=:half)
  variable(_19484:half)
  variable(_19485:half)
--------------------------------------------------
第723行: _19823 = _19824;
语法树结构:
assignment(=:half3)
  variable(_19823:half3)
  variable(_19824:half3)
--------------------------------------------------
第724行: _19788 = _19789;
语法树结构:
assignment(=:float)
  variable(_19788:float)
  variable(_19789:float)
--------------------------------------------------
第725行: _19753 = _19754;
语法树结构:
assignment(=:float3)
  variable(_19753:float3)
  variable(_19754:float3)
--------------------------------------------------
第726行: _19718 = select(_19720, _11517, bool3(_13270));
语法树结构:
assignment(=:float3)
  variable(_19718:float3)
  function(select:unknown)
    variable(_19720:float3)
    variable(_11517:float3)
    function(bool3:unknown)
      variable(_13270:bool)
--------------------------------------------------
第727行: _19683 = _19684;
语法树结构:
assignment(=:float3)
  variable(_19683:float3)
  variable(_19684:float3)
--------------------------------------------------
第728行: _19611 = _13270 ? 0 : _19613;
语法树结构:
assignment(=:int)
  variable(_19611:int)
  operator(?::unknown)
    variable(_13270:bool)
    literal(0:int)
    variable(_19613:int)
--------------------------------------------------
第729行: _19575 = _13270 ? 1.0 : _19577;
语法树结构:
assignment(=:float)
  variable(_19575:float)
  operator(?::unknown)
    variable(_13270:bool)
    literal(1.0:float)
    variable(_19577:float)
--------------------------------------------------
第730行: _19483 = _19484;
语法树结构:
assignment(=:half)
  variable(_19483:half)
  variable(_19484:half)
--------------------------------------------------
第732行: _19822 = _19823;
语法树结构:
assignment(=:half3)
  variable(_19822:half3)
  variable(_19823:half3)
--------------------------------------------------
第733行: _19787 = _19788;
语法树结构:
assignment(=:float)
  variable(_19787:float)
  variable(_19788:float)
--------------------------------------------------
第734行: _19752 = _19753;
语法树结构:
assignment(=:float3)
  variable(_19752:float3)
  variable(_19753:float3)
--------------------------------------------------
第735行: _19717 = _19718;
语法树结构:
assignment(=:float3)
  variable(_19717:float3)
  variable(_19718:float3)
--------------------------------------------------
第736行: _19682 = _19683;
语法树结构:
assignment(=:float3)
  variable(_19682:float3)
  variable(_19683:float3)
--------------------------------------------------
第737行: _19610 = _19611;
语法树结构:
assignment(=:int)
  variable(_19610:int)
  variable(_19611:int)
--------------------------------------------------
第738行: _19574 = _19575;
语法树结构:
assignment(=:float)
  variable(_19574:float)
  variable(_19575:float)
--------------------------------------------------
第739行: _19482 = _19483;
语法树结构:
assignment(=:half)
  variable(_19482:half)
  variable(_19483:half)
--------------------------------------------------
第741行: _19821 = _19822;
语法树结构:
assignment(=:half3)
  variable(_19821:half3)
  variable(_19822:half3)
--------------------------------------------------
第742行: _19786 = _19787;
语法树结构:
assignment(=:float)
  variable(_19786:float)
  variable(_19787:float)
--------------------------------------------------
第743行: _19751 = _19752;
语法树结构:
assignment(=:float3)
  variable(_19751:float3)
  variable(_19752:float3)
--------------------------------------------------
第744行: _19716 = _19717;
语法树结构:
assignment(=:float3)
  variable(_19716:float3)
  variable(_19717:float3)
--------------------------------------------------
第745行: _19681 = _19682;
语法树结构:
assignment(=:float3)
  variable(_19681:float3)
  variable(_19682:float3)
--------------------------------------------------
第746行: _19609 = _19610;
语法树结构:
assignment(=:int)
  variable(_19609:int)
  variable(_19610:int)
--------------------------------------------------
第747行: _19573 = _19574;
语法树结构:
assignment(=:float)
  variable(_19573:float)
  variable(_19574:float)
--------------------------------------------------
第748行: _19481 = _19482;
语法树结构:
assignment(=:half)
  variable(_19481:half)
  variable(_19482:half)
--------------------------------------------------
第750行: float _14176 = float(_19481);
语法树结构:
assignment(=:float)
  declaration(_14176:float)
  function(float:unknown)
    variable(_19481:half)
--------------------------------------------------
第751行: float3 _14197 = fast::normalize(_19716 + _19751);
语法树结构:
assignment(=:float3)
  declaration(_14197:float3)
  function(fast::normalize:unknown)
    operator(+:unknown)
      variable(_19716:float3)
      variable(_19751:float3)
--------------------------------------------------
第752行: float _14202 = fast::clamp(dot(_19681, _14197), 0.0, 1.0);
语法树结构:
assignment(=:float)
  declaration(_14202:float)
  function(fast::clamp:unknown)
    function(dot:unknown)
      variable(_19681:float3)
      variable(_14197:float3)
    literal(0.0:float)
    literal(1.0:float)
--------------------------------------------------
第753行: float _14208 = fast::clamp(dot(_19716, _14197), 0.0, 1.0);
语法树结构:
assignment(=:float)
  declaration(_14208:float)
  function(fast::clamp:unknown)
    function(dot:unknown)
      variable(_19716:float3)
      variable(_14197:float3)
    literal(0.0:float)
    literal(1.0:float)
--------------------------------------------------
第754行: float _13993 = fast::clamp(abs(fast::clamp(dot(_19681, _19716), 0.0, 1.0)) + 9.9999997473787516355514526367188e-06, 0.0, 1.0);
语法树结构:
assignment(=:float)
  declaration(_13993:float)
  function(fast::clamp:unknown)
    operator(+:unknown)
      function(abs:unknown)
        function(fast::clamp:unknown)
          function(dot:unknown)
            variable(_19681:float3)
            variable(_19716:float3)
          literal(0.0:float)
          literal(1.0:float)
      literal(9.9999997473787516355514526367188:float)
--------------------------------------------------
第755行: float _14013 = exp2((((-5.554729938507080078125) * _14208) - 6.9831600189208984375) * _14208);
语法树结构:
assignment(=:float)
  declaration(_14013:float)
  function(exp2:unknown)
    operator(*:unknown)
      operator(-:unknown)
        operator(*:unknown)
          operator(-:unknown)
            literal(5.554729938507080078125:float)
          variable(_14208:float)
        literal(6.9831600189208984375:float)
      variable(_14208:float)
--------------------------------------------------
第756行: float _14020 = _14013 + ((1.0 - _14013) * 0.039999999105930328369140625);
语法树结构:
assignment(=:float)
  declaration(_14020:float)
  operator(+:unknown)
    variable(_14013:float)
    operator(*:unknown)
      operator(-:unknown)
        literal(1.0:float)
        variable(_14013:float)
      literal(0.039999999105930328369140625:float)
--------------------------------------------------
第757行: half _14029 = _9199 - (_9199 - _19481);
语法树结构:
assignment(=:half)
  declaration(_14029:half)
  operator(-:unknown)
    variable(_9199:half)
    operator(-:unknown)
      variable(_9199:half)
      variable(_19481:half)
--------------------------------------------------
第758行: half3 _14069 = half3(((float3(_19821) * float3(_19786)) * _14176) * float(clamp(((_14029 + _11768) / _11777) * _11777, half(0.0), half(1.0)) * clamp(((_19481 + _11764) / _11790) * _11790, half(0.0), half(1.0))));
语法树结构:
assignment(=:half3)
  declaration(_14069:half3)
  function(half3:unknown)
    operator(*:unknown)
      operator(*:unknown)
        operator(*:unknown)
          function(float3:unknown)
            variable(_19821:half3)
          function(float3:unknown)
            variable(_19786:float)
        variable(_14176:float)
      function(float:unknown)
        operator(*:unknown)
          function(clamp:unknown)
            operator(*:unknown)
              operator(/:unknown)
                operator(+:unknown)
                  variable(_14029:half)
                  variable(_11768:half)
                variable(_11777:half)
              variable(_11777:half)
            function(half:unknown)
              literal(0.0:float)
            function(half:unknown)
              literal(1.0:float)
          function(clamp:unknown)
            operator(*:unknown)
              operator(/:unknown)
                operator(+:unknown)
                  variable(_19481:half)
                  variable(_11764:half)
                variable(_11790:half)
              variable(_11790:half)
            function(half:unknown)
              literal(0.0:float)
            function(half:unknown)
              literal(1.0:float)
--------------------------------------------------
第759行: half _14084 = clamp(dot(half3(fast::normalize(_19751 + _19716)), _9064), half(0.0), half(1.0));
语法树结构:
assignment(=:half)
  declaration(_14084:half)
  function(clamp:unknown)
    function(dot:unknown)
      function(half3:unknown)
        function(fast::normalize:unknown)
          operator(+:unknown)
            variable(_19751:float3)
            variable(_19716:float3)
      variable(_9064:half3)
    function(half:unknown)
      literal(0.0:float)
    function(half:unknown)
      literal(1.0:float)
--------------------------------------------------
第760行: float3 _19883;
语法树结构:
declaration(_19883:float3)
--------------------------------------------------
第761行: do
语法树结构:
variable(do:unknown)
--------------------------------------------------
第763行: if (_19609 >= 1)
语法树结构:
operator(>=:unknown)
  variable(_19609:int)
  literal(1:int)
--------------------------------------------------
第765行: float _14319 = _12049 / (((((_14202 * _12049) * _12049) - _14202) * _14202) + 1.0);
语法树结构:
assignment(=:float)
  declaration(_14319:float)
  operator(/:unknown)
    variable(_12049:float)
    operator(+:unknown)
      operator(*:unknown)
        operator(-:unknown)
          operator(*:unknown)
            operator(*:unknown)
              variable(_14202:float)
              variable(_12049:float)
            variable(_12049:float)
          variable(_14202:float)
        variable(_14202:float)
      literal(1.0:float)
--------------------------------------------------
第766行: _19883 = (_12025 + (_12120 * _14013)) * (fast::min(1000.0, (_14319 * _14319) * 0.3183098733425140380859375) * (1.0 / fast::max((_13993 + sqrt((_13993 * (_13993 - (_13993 * _12080))) + _12080)) * (_14176 + sqrt((_14176 * (_14176 - (_14176 * _12080))) + _12080)), _12108)));
语法树结构:
assignment(=:float3)
  variable(_19883:float3)
  operator(*:unknown)
    operator(+:unknown)
      variable(_12025:float3)
      operator(*:unknown)
        variable(_12120:float3)
        variable(_14013:float)
    operator(*:unknown)
      function(fast::min:unknown)
        literal(1000.0:float)
        operator(*:unknown)
          operator(*:unknown)
            variable(_14319:float)
            variable(_14319:float)
          literal(0.3183098733425140380859375:float)
      operator(/:unknown)
        literal(1.0:float)
        function(fast::max:unknown)
          operator(*:unknown)
            operator(+:unknown)
              variable(_13993:float)
              function(sqrt:unknown)
                operator(+:unknown)
                  operator(*:unknown)
                    variable(_13993:float)
                    operator(-:unknown)
                      variable(_13993:float)
                      operator(*:unknown)
                        variable(_13993:float)
                        variable(_12080:float)
                  variable(_12080:float)
            operator(+:unknown)
              variable(_14176:float)
              function(sqrt:unknown)
                operator(+:unknown)
                  operator(*:unknown)
                    variable(_14176:float)
                    operator(-:unknown)
                      variable(_14176:float)
                      operator(*:unknown)
                        variable(_14176:float)
                        variable(_12080:float)
                  variable(_12080:float)
          variable(_12108:float)
--------------------------------------------------
第767行: break;
语法树结构:
variable(break:unknown)
--------------------------------------------------
第771行: _19883 = float3(0.0);
语法树结构:
assignment(=:float3)
  variable(_19883:float3)
  function(float3:unknown)
    literal(0.0:float)
--------------------------------------------------
第772行: break;
语法树结构:
variable(break:unknown)
--------------------------------------------------
第774行: break; // unreachable workaround
语法树结构:
variable(break:unknown)
--------------------------------------------------
第775行: } while(false);
语法树结构:
variable(false:unknown)
--------------------------------------------------
第776行: _20953 = _19821;
语法树结构:
assignment(=:half3)
  variable(_20953:half3)
  variable(_19821:half3)
--------------------------------------------------
第777行: _20938 = _19786;
语法树结构:
assignment(=:float)
  variable(_20938:float)
  variable(_19786:float)
--------------------------------------------------
第778行: _20923 = _19751;
语法树结构:
assignment(=:float3)
  variable(_20923:float3)
  variable(_19751:float3)
--------------------------------------------------
第779行: _20908 = _19716;
语法树结构:
assignment(=:float3)
  variable(_20908:float3)
  variable(_19716:float3)
--------------------------------------------------
第780行: _20893 = _19681;
语法树结构:
assignment(=:float3)
  variable(_20893:float3)
  variable(_19681:float3)
--------------------------------------------------
第781行: _20863 = _19609;
语法树结构:
assignment(=:int)
  variable(_20863:int)
  variable(_19609:int)
--------------------------------------------------
第782行: _20848 = _19573;
语法树结构:
assignment(=:float)
  variable(_20848:float)
  variable(_19573:float)
--------------------------------------------------
第783行: _20833 = _19481;
语法树结构:
assignment(=:half)
  variable(_20833:half)
  variable(_19481:half)
--------------------------------------------------
第784行: _19965 = (_18431 + (float3(mix(_9179, half3(half(((_11862 * powr(float(half(fast::max(float(_9199 - (_14084 * _14084)), 0.0078125))), _11858)) * 0.15915493667125701904296875) * float(half4(float4(_11970 / powr(max(half4(half(9.9956989288330078125e-05)), _11973 - (_11975 * (-half(dot(_19716, _19751))))), _11982)) * float4(0.0795769989490509033203125)).x))), half3(half(fast::clamp((_14020 * _14020) * float(dot(_14069, _8303)), 0.0, 1.0)))) * _14069) * float3(_19573))) + (((float3(_14069) * _19883) * _19573) * float(_14029));
语法树结构:
assignment(=:float3)
  variable(_19965:float3)
  operator(+:unknown)
    variable(_18431:float3)
    function(float3:unknown)
      function(mix:unknown)
        variable(_9179:half3)
        function(half3:unknown)
          function(half:unknown)
            operator(*:unknown)
              operator(*:unknown)
                operator(*:unknown)
                  variable(_11862:float)
                  function(powr:unknown)
                    function(float:unknown)
                      function(half:unknown)
                        function(fast::max:unknown)
                          function(float:unknown)
                            operator(-:unknown)
                              variable(_9199:half)
                              operator(*:unknown)
                                variable(_14084:half)
                                variable(_14084:half)
                          literal(0.0078125:float)
                    variable(_11858:float)
                literal(0.15915493667125701904296875:float)
              function(float:unknown)
                function(half4:unknown)
                  function(float4:unknown)
                    operator(/:unknown)
                      variable(_11970:half4)
                      function(powr:unknown)
                        function(max:unknown)
                          function(half4:unknown)
                            function(half:unknown)
                              literal(9.9956989288330078125:float)
--------------------------------------------------
第785行: _19923 = _18429 + (_14069 * _14029);
语法树结构:
assignment(=:half3)
  variable(_19923:half3)
  operator(+:unknown)
    variable(_18429:half3)
    operator(*:unknown)
      variable(_14069:half3)
      variable(_14029:half)
--------------------------------------------------
第787行: half3 _8560 = (_9179 + (((_9179 + (_11812 * _11772)) * _9254) * half3(half(_Block1.EnvInfo.z)))) + _18429;
语法树结构:
assignment(=:half3)
  declaration(_8560:half3)
  operator(+:unknown)
    operator(+:unknown)
      variable(_9179:half3)
      operator(*:unknown)
        operator(*:unknown)
          operator(+:unknown)
            variable(_9179:half3)
            operator(*:unknown)
              variable(_11812:half3)
              variable(_11772:half)
          variable(_9254:half3)
        function(half3:unknown)
          function(half:unknown)
            member_access(_Block1.EnvInfo.z:float)
    variable(_18429:half3)
--------------------------------------------------
第788行: float3 _8565 = (_9698 + _8521) + _18431;
语法树结构:
assignment(=:float3)
  declaration(_8565:float3)
  operator(+:unknown)
    operator(+:unknown)
      variable(_9698:float3)
      variable(_8521:float3)
    variable(_18431:float3)
--------------------------------------------------
第789行: bool _8573 = (_8397 & 16128u) > 0u;
语法树结构:
assignment(=:bool)
  declaration(_8573:bool)
  operator(>:unknown)
    operator(&:unknown)
      variable(_8397:uint)
      literal(16128u:int)
    literal(0u:int)
--------------------------------------------------
第790行: float3 _18989;
语法树结构:
declaration(_18989:float3)
--------------------------------------------------
第791行: half3 _19028;
语法树结构:
declaration(_19028:half3)
--------------------------------------------------
第792行: if (_8573)
语法树结构:
variable(_8573:bool)
--------------------------------------------------
第794行: bool _8590 = (_8397 & 16384u) > 0u;
语法树结构:
assignment(=:bool)
  declaration(_8590:bool)
  operator(>:unknown)
    operator(&:unknown)
      variable(_8397:uint)
      literal(16384u:int)
    literal(0u:int)
--------------------------------------------------
第795行: half3 _18832;
语法树结构:
declaration(_18832:half3)
--------------------------------------------------
第796行: half3 _18859;
语法树结构:
declaration(_18859:half3)
--------------------------------------------------
第797行: if (_8573)
语法树结构:
variable(_8573:bool)
--------------------------------------------------
第799行: uint _14451 = (_8397 & 458752u) >> 16u;
语法树结构:
assignment(=:uint)
  declaration(_14451:uint)
  operator(>>:unknown)
    operator(&:unknown)
      variable(_8397:uint)
      literal(458752u:int)
    literal(16u:int)
--------------------------------------------------
第800行: uint _14469 = (_8397 & 4026531840u) >> 28u;
语法树结构:
assignment(=:uint)
  declaration(_14469:uint)
  operator(>>:unknown)
    operator(&:unknown)
      variable(_8397:uint)
      literal(4026531840u:int)
    literal(28u:int)
--------------------------------------------------
第801行: float _14482 = fast::clamp((_Block1.CameraInfo.y * in.IN_LinearZ) / fast::max(_Block1.SHGIParam2.x, 0.001000000047497451305389404296875), 0.0, 1.0);
语法树结构:
assignment(=:float)
  declaration(_14482:float)
  function(fast::clamp:unknown)
    operator(/:unknown)
      operator(*:unknown)
        member_access(_Block1.CameraInfo.y:float)
        member_access(in.IN_LinearZ:float)
      function(fast::max:unknown)
        member_access(_Block1.SHGIParam2.x:float)
        literal(0.001000000047497451305389404296875:float)
    literal(0.0:float)
    literal(1.0:float)
--------------------------------------------------
第802行: half3 _18824;
语法树结构:
declaration(_18824:half3)
--------------------------------------------------
第803行: half3 _18826;
语法树结构:
declaration(_18826:half3)
--------------------------------------------------
第804行: if ((_8397 & 2048u) != 0u)
语法树结构:
operator(&:unknown)
  variable(_8397:uint)
  literal(2048u:int)
--------------------------------------------------
第806行: float3 _14684 = select(_Block1.PlayerPos.xyz, _Block1.CameraPos.xyz, bool3((_8397 & 524288u) > 0u)) * float3(0.125);
语法树结构:
assignment(=:float3)
  declaration(_14684:float3)
  operator(*:unknown)
    function(select:unknown)
      member_access(_Block1.PlayerPos.xyz:float3)
      member_access(_Block1.CameraPos.xyz:float3)
      function(bool3:unknown)
        operator(>:unknown)
          operator(&:unknown)
            variable(_8397:uint)
            literal(524288u:int)
          literal(0u:int)
    function(float3:unknown)
      literal(0.125:float)
--------------------------------------------------
第807行: float _14692 = _14684.x;
语法树结构:
assignment(=:float)
  declaration(_14692:float)
  member_access(_14684.x:float)
--------------------------------------------------
第808行: float _14694 = floor(_14692);
语法树结构:
assignment(=:float)
  declaration(_14694:float)
  function(floor:unknown)
    variable(_14692:float)
--------------------------------------------------
第809行: float3 _21212;
语法树结构:
declaration(_21212:float3)
--------------------------------------------------
第810行: _21212.x = _14694 - 15.0;
语法树结构:
assignment(=:unknown)
  member_access(_21212.x:unknown)
  operator(-:unknown)
    variable(_14694:float)
    literal(15.0:float)
--------------------------------------------------
第811行: float3 _21268;
语法树结构:
declaration(_21268:float3)
--------------------------------------------------
第812行: if ((_14692 - _14694) > 0.5)
语法树结构:
operator(-:unknown)
  variable(_14692:float)
  variable(_14694:float)
--------------------------------------------------
第814行: float3 _21215 = _21212;
语法树结构:
assignment(=:float3)
  declaration(_21215:float3)
  variable(_21212:float3)
--------------------------------------------------
第815行: _21215.x = _14694 + (-14.0);
语法树结构:
assignment(=:unknown)
  member_access(_21215.x:unknown)
  operator(+:unknown)
    variable(_14694:float)
    operator(-:unknown)
      literal(14.0:float)
--------------------------------------------------
第816行: _21268 = _21215;
语法树结构:
assignment(=:float3)
  variable(_21268:float3)
  variable(_21215:float3)
--------------------------------------------------
第820行: _21268 = _21212;
语法树结构:
assignment(=:float3)
  variable(_21268:float3)
  variable(_21212:float3)
--------------------------------------------------
第822行: float _21034 = _14684.y;
语法树结构:
assignment(=:float)
  declaration(_21034:float)
  member_access(_14684.y:float)
--------------------------------------------------
第823行: float _21035 = floor(_21034);
语法树结构:
assignment(=:float)
  declaration(_21035:float)
  function(floor:unknown)
    variable(_21034:float)
--------------------------------------------------
第824行: float3 _21219 = _21268;
语法树结构:
assignment(=:float3)
  declaration(_21219:float3)
  variable(_21268:float3)
--------------------------------------------------
第825行: _21219.y = _21035 - 8.0;
语法树结构:
assignment(=:unknown)
  member_access(_21219.y:unknown)
  operator(-:unknown)
    variable(_21035:float)
    literal(8.0:float)
--------------------------------------------------
第826行: float3 _21269;
语法树结构:
declaration(_21269:float3)
--------------------------------------------------
第827行: if ((_21034 - _21035) > 0.5)
语法树结构:
operator(-:unknown)
  variable(_21034:float)
  variable(_21035:float)
--------------------------------------------------
第829行: float3 _21222 = _21219;
语法树结构:
assignment(=:float3)
  declaration(_21222:float3)
  variable(_21219:float3)
--------------------------------------------------
第830行: _21222.y = _21035 + (-7.0);
语法树结构:
assignment(=:unknown)
  member_access(_21222.y:unknown)
  operator(+:unknown)
    variable(_21035:float)
    operator(-:unknown)
      literal(7.0:float)
--------------------------------------------------
第831行: _21269 = _21222;
语法树结构:
assignment(=:float3)
  variable(_21269:float3)
  variable(_21222:float3)
--------------------------------------------------
第835行: _21269 = _21219;
语法树结构:
assignment(=:float3)
  variable(_21269:float3)
  variable(_21219:float3)
--------------------------------------------------
第837行: float _21056 = _14684.z;
语法树结构:
assignment(=:float)
  declaration(_21056:float)
  member_access(_14684.z:float)
--------------------------------------------------
第838行: float _21057 = floor(_21056);
语法树结构:
assignment(=:float)
  declaration(_21057:float)
  function(floor:unknown)
    variable(_21056:float)
--------------------------------------------------
第839行: float3 _21226 = _21269;
语法树结构:
assignment(=:float3)
  declaration(_21226:float3)
  variable(_21269:float3)
--------------------------------------------------
第840行: _21226.z = _21057 - 15.0;
语法树结构:
assignment(=:unknown)
  member_access(_21226.z:unknown)
  operator(-:unknown)
    variable(_21057:float)
    literal(15.0:float)
--------------------------------------------------
第841行: float3 _21270;
语法树结构:
declaration(_21270:float3)
--------------------------------------------------
第842行: if ((_21056 - _21057) > 0.5)
语法树结构:
operator(-:unknown)
  variable(_21056:float)
  variable(_21057:float)
--------------------------------------------------
第844行: float3 _21229 = _21226;
语法树结构:
assignment(=:float3)
  declaration(_21229:float3)
  variable(_21226:float3)
--------------------------------------------------
第845行: _21229.z = _21057 + (-14.0);
语法树结构:
assignment(=:unknown)
  member_access(_21229.z:unknown)
  operator(+:unknown)
    variable(_21057:float)
    operator(-:unknown)
      literal(14.0:float)
--------------------------------------------------
第846行: _21270 = _21229;
语法树结构:
assignment(=:float3)
  variable(_21270:float3)
  variable(_21229:float3)
--------------------------------------------------
第850行: _21270 = _21226;
语法树结构:
assignment(=:float3)
  variable(_21270:float3)
  variable(_21226:float3)
--------------------------------------------------
第852行: float3 _14717 = _21270 * 8.0;
语法树结构:
assignment(=:float3)
  declaration(_14717:float3)
  operator(*:unknown)
    variable(_21270:float3)
    literal(8.0:float)
--------------------------------------------------
第853行: half3 _18825;
语法树结构:
declaration(_18825:half3)
--------------------------------------------------
第854行: half3 _18827;
语法树结构:
declaration(_18827:half3)
--------------------------------------------------
第855行: if (all(in.IN_WorldPosition.xyz >= _14717) && all(in.IN_WorldPosition.xyz < (_14717 + float3(240.0, 128.0, 240.0))))
语法树结构:
function(all:unknown)
  operator(>=:unknown)
    member_access(in.IN_WorldPosition.xyz:float3)
    variable(_14717:float3)
--------------------------------------------------
第857行: float3 _14534 = (in.IN_WorldPosition.xyz + (_9109 * 0.100000001490116119384765625)) * float3(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125);
语法树结构:
assignment(=:float3)
  declaration(_14534:float3)
  operator(*:unknown)
    operator(+:unknown)
      member_access(in.IN_WorldPosition.xyz:float3)
      operator(*:unknown)
        variable(_9109:float3)
        literal(0.100000001490116119384765625:float)
    function(float3:unknown)
      literal(0.0041666668839752674102783203125:float)
      literal(0.0078125:float)
      literal(0.0041666668839752674102783203125:float)
--------------------------------------------------
第858行: float3 _14747 = _14534 - floor(_14534);
语法树结构:
assignment(=:float3)
  declaration(_14747:float3)
  operator(-:unknown)
    variable(_14534:float3)
    function(floor:unknown)
      variable(_14534:float3)
--------------------------------------------------
第859行: half3 _14556 = half3(_9109);
语法树结构:
assignment(=:half3)
  declaration(_14556:half3)
  function(half3:unknown)
    variable(_9109:float3)
--------------------------------------------------
第860行: float _14788 = float((_8397 & 15728640u) >> 20u);
语法树结构:
assignment(=:float)
  declaration(_14788:float)
  function(float:unknown)
    operator(>>:unknown)
      operator(&:unknown)
        variable(_8397:uint)
        literal(15728640u:int)
      literal(20u:int)
--------------------------------------------------
第861行: float _14797 = float(_14451);
语法树结构:
assignment(=:float)
  declaration(_14797:float)
  function(float:unknown)
    variable(_14451:uint)
--------------------------------------------------
第862行: float _14827 = (_14788 - _14797) + ((3.0 - _14797) * 3.0);
语法树结构:
assignment(=:float)
  declaration(_14827:float)
  operator(+:unknown)
    operator(-:unknown)
      variable(_14788:float)
      variable(_14797:float)
    operator(*:unknown)
      operator(-:unknown)
        literal(3.0:float)
        variable(_14797:float)
      literal(3.0:float)
--------------------------------------------------
第863行: float _14831 = _14827 + 1.0;
语法树结构:
assignment(=:float)
  declaration(_14831:float)
  operator(+:unknown)
    variable(_14827:float)
    literal(1.0:float)
--------------------------------------------------
第864行: float _14833 = _14827 + 2.0;
语法树结构:
assignment(=:float)
  declaration(_14833:float)
  operator(+:unknown)
    variable(_14827:float)
    literal(2.0:float)
--------------------------------------------------
第865行: half3 _18806;
语法树结构:
declaration(_18806:half3)
--------------------------------------------------
第866行: half3 _18818;
语法树结构:
declaration(_18818:half3)
--------------------------------------------------
第867行: if (3 >= int(_14469))
语法树结构:
operator(>=:unknown)
  literal(3:int)
  function(int:unknown)
    variable(_14469:uint)
--------------------------------------------------
第869行: float _14850 = (_14788 - float((_8397 & 251658240u) >> 24u)) + ((3.0 - float(_14469)) * 3.0);
语法树结构:
assignment(=:float)
  declaration(_14850:float)
  operator(+:unknown)
    operator(-:unknown)
      variable(_14788:float)
      function(float:unknown)
        operator(>>:unknown)
          operator(&:unknown)
            variable(_8397:uint)
            literal(251658240u:int)
          literal(24u:int)
    operator(*:unknown)
      operator(-:unknown)
        literal(3.0:float)
        function(float:unknown)
          variable(_14469:uint)
      literal(3.0:float)
--------------------------------------------------
第870行: float _14854 = _14850 + 1.0;
语法树结构:
assignment(=:float)
  declaration(_14854:float)
  operator(+:unknown)
    variable(_14850:float)
    literal(1.0:float)
--------------------------------------------------
第871行: float _14856 = _14850 + 2.0;
语法树结构:
assignment(=:float)
  declaration(_14856:float)
  operator(+:unknown)
    variable(_14850:float)
    literal(2.0:float)
--------------------------------------------------
第872行: float2 _14956 = ((_14747.xz - float2(0.5)) * 0.9375) + float2(0.5);
语法树结构:
assignment(=:float2)
  declaration(_14956:float2)
  operator(+:unknown)
    operator(*:unknown)
      operator(-:unknown)
        member_access(_14747.xz:float2)
        function(float2:unknown)
          literal(0.5:float)
      literal(0.9375:float)
    function(float2:unknown)
      literal(0.5:float)
--------------------------------------------------
第873行: float _14963 = _14717.x * 0.0041666668839752674102783203125;
语法树结构:
assignment(=:float)
  declaration(_14963:float)
  operator(*:unknown)
    member_access(_14717.x:float)
    literal(0.0041666668839752674102783203125:float)
--------------------------------------------------
第874行: float _14967 = ((_14963 - floor(_14963)) - 0.5) * 0.9375;
语法树结构:
assignment(=:float)
  declaration(_14967:float)
  operator(*:unknown)
    operator(-:unknown)
      operator(-:unknown)
        variable(_14963:float)
        function(floor:unknown)
          variable(_14963:float)
      literal(0.5:float)
    literal(0.9375:float)
--------------------------------------------------
第875行: float _14973 = _14717.z * 0.0041666668839752674102783203125;
语法树结构:
assignment(=:float)
  declaration(_14973:float)
  operator(*:unknown)
    member_access(_14717.z:float)
    literal(0.0041666668839752674102783203125:float)
--------------------------------------------------
第876行: float _14977 = ((_14973 - floor(_14973)) - 0.5) * 0.9375;
语法树结构:
assignment(=:float)
  declaration(_14977:float)
  operator(*:unknown)
    operator(-:unknown)
      operator(-:unknown)
        variable(_14973:float)
        function(floor:unknown)
          variable(_14973:float)
      literal(0.5:float)
    literal(0.9375:float)
--------------------------------------------------
第877行: float _14982 = _14956.x;
语法树结构:
assignment(=:float)
  declaration(_14982:float)
  member_access(_14956.x:float)
--------------------------------------------------
第878行: float3 _18095;
语法树结构:
declaration(_18095:float3)
--------------------------------------------------
第879行: _18095.x = (_14982 < (_14967 + 0.5)) ? fast::min(_14982, _14967 + 0.49609375) : fast::max(_14982, _14967 + 0.50390625);
语法树结构:
assignment(=:unknown)
  member_access(_18095.x:unknown)
  operator(?::unknown)
    operator(<:unknown)
      variable(_14982:float)
      operator(+:unknown)
        variable(_14967:float)
        literal(0.5:float)
    function(fast::min:unknown)
      variable(_14982:float)
      operator(+:unknown)
        variable(_14967:float)
        literal(0.49609375:float)
    function(fast::max:unknown)
      variable(_14982:float)
      operator(+:unknown)
        variable(_14967:float)
        literal(0.50390625:float)
--------------------------------------------------
第880行: float _15000 = _14956.y;
语法树结构:
assignment(=:float)
  declaration(_15000:float)
  member_access(_14956.y:float)
--------------------------------------------------
第881行: _18095.z = (_15000 < (_14977 + 0.5)) ? fast::min(_15000, _14977 + 0.49609375) : fast::max(_15000, _14977 + 0.50390625);
语法树结构:
assignment(=:unknown)
  member_access(_18095.z:unknown)
  operator(?::unknown)
    operator(<:unknown)
      variable(_15000:float)
      operator(+:unknown)
        variable(_14977:float)
        literal(0.5:float)
    function(fast::min:unknown)
      variable(_15000:float)
      operator(+:unknown)
        variable(_14977:float)
        literal(0.49609375:float)
    function(fast::max:unknown)
      variable(_15000:float)
      operator(+:unknown)
        variable(_14977:float)
        literal(0.50390625:float)
--------------------------------------------------
第882行: float _15021 = (_14747.y * 64.0) - 0.5;
语法树结构:
assignment(=:float)
  declaration(_15021:float)
  operator(-:unknown)
    operator(*:unknown)
      member_access(_14747.y:float)
      literal(64.0:float)
    literal(0.5:float)
--------------------------------------------------
第883行: float _15026 = floor(_15021);
语法树结构:
assignment(=:float)
  declaration(_15026:float)
  function(floor:unknown)
    variable(_15021:float)
--------------------------------------------------
第884行: uint _15029 = (_15021 < 0.0) ? 63u : uint(_15026);
语法树结构:
assignment(=:uint)
  declaration(_15029:uint)
  operator(?::unknown)
    operator(<:unknown)
      variable(_15021:float)
      literal(0.0:float)
    literal(63u:int)
    function(uint:unknown)
      variable(_15026:float)
--------------------------------------------------
第885行: uint _15032 = _15029 + 1u;
语法树结构:
assignment(=:uint)
  declaration(_15032:uint)
  operator(+:unknown)
    variable(_15029:uint)
    literal(1u:int)
--------------------------------------------------
第886行: uint _21309 = (_15032 >= 64u) ? 0u : _15032;
语法树结构:
assignment(=:uint)
  declaration(_21309:uint)
  operator(?::unknown)
    operator(>=:unknown)
      variable(_15032:uint)
      literal(64u:int)
    literal(0u:int)
    variable(_15032:uint)
--------------------------------------------------
第887行: float2 _15059 = (float2(float(_15029 & 7u), float(_15029 >> 3u)) + _18095.xz) * 0.125;
语法树结构:
assignment(=:float2)
  declaration(_15059:float2)
  operator(*:unknown)
    operator(+:unknown)
      function(float2:unknown)
        function(float:unknown)
          operator(&:unknown)
            variable(_15029:uint)
            literal(7u:int)
        function(float:unknown)
          operator(>>:unknown)
            variable(_15029:uint)
            literal(3u:int)
      member_access(_18095.xz:float2)
    literal(0.125:float)
--------------------------------------------------
第888行: float2 _15074 = (float2(float(_21309 & 7u), float(_21309 >> 3u)) + _18095.xz) * 0.125;
语法树结构:
assignment(=:float2)
  declaration(_15074:float2)
  operator(*:unknown)
    operator(+:unknown)
      function(float2:unknown)
        function(float:unknown)
          operator(&:unknown)
            variable(_21309:uint)
            literal(7u:int)
        function(float:unknown)
          operator(>>:unknown)
            variable(_21309:uint)
            literal(3u:int)
      member_access(_18095.xz:float2)
    literal(0.125:float)
--------------------------------------------------
第889行: float _15078 = _15059.x;
语法树结构:
assignment(=:float)
  declaration(_15078:float)
  member_access(_15059.x:float)
--------------------------------------------------
第890行: float3 _15080 = float3(_15078, _15059.y, _14827);
语法树结构:
assignment(=:float3)
  declaration(_15080:float3)
  function(float3:unknown)
    variable(_15078:float)
    member_access(_15059.y:float)
    variable(_14827:float)
--------------------------------------------------
第891行: half4 _18103;
语法树结构:
declaration(_18103:half4)
--------------------------------------------------
第892行: _18103.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15080.xy, uint(rint(_15080.z)), level(0.0)).x);
语法树结构:
assignment(=:unknown)
  member_access(_18103.w:unknown)
  function(half:unknown)
    member_access(result_sSHAOAlphaVTSampler.sample.x:unknown)
      function(sSHAOAlphaVTSampler.sample:unknown)
        variable(sSHAOAlphaVTSamplerSmplr:sampler)
        member_access(_15080.xy:float2)
        function(uint:unknown)
          function(rint:unknown)
            member_access(_15080.z:float)
        function(level:unknown)
          literal(0.0:float)
--------------------------------------------------
第893行: float3 _15092 = float3(_15078, _15059.y, _14850);
语法树结构:
assignment(=:float3)
  declaration(_15092:float3)
  function(float3:unknown)
    variable(_15078:float)
    member_access(_15059.y:float)
    variable(_14850:float)
--------------------------------------------------
第894行: half3 _15097 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15092.xy, uint(rint(_15092.z)), level(0.0)).xyz);
语法树结构:
assignment(=:half3)
  declaration(_15097:half3)
  function(half3:unknown)
    member_access(result_sSHAORGBVTSampler.sample.xyz:unknown)
      function(sSHAORGBVTSampler.sample:unknown)
        variable(sSHAORGBVTSamplerSmplr:sampler)
        member_access(_15092.xy:float2)
        function(uint:unknown)
          function(rint:unknown)
            member_access(_15092.z:float)
        function(level:unknown)
          literal(0.0:float)
--------------------------------------------------
第895行: float _15104 = _15074.x;
语法树结构:
assignment(=:float)
  declaration(_15104:float)
  member_access(_15074.x:float)
--------------------------------------------------
第896行: float3 _15106 = float3(_15104, _15074.y, _14827);
语法树结构:
assignment(=:float3)
  declaration(_15106:float3)
  function(float3:unknown)
    variable(_15104:float)
    member_access(_15074.y:float)
    variable(_14827:float)
--------------------------------------------------
第897行: half4 _18105;
语法树结构:
declaration(_18105:half4)
--------------------------------------------------
第898行: _18105.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15106.xy, uint(rint(_15106.z)), level(0.0)).x);
语法树结构:
assignment(=:unknown)
  member_access(_18105.w:unknown)
  function(half:unknown)
    member_access(result_sSHAOAlphaVTSampler.sample.x:unknown)
      function(sSHAOAlphaVTSampler.sample:unknown)
        variable(sSHAOAlphaVTSamplerSmplr:sampler)
        member_access(_15106.xy:float2)
        function(uint:unknown)
          function(rint:unknown)
            member_access(_15106.z:float)
        function(level:unknown)
          literal(0.0:float)
--------------------------------------------------
第899行: float3 _15118 = float3(_15104, _15074.y, _14850);
语法树结构:
assignment(=:float3)
  declaration(_15118:float3)
  function(float3:unknown)
    variable(_15104:float)
    member_access(_15074.y:float)
    variable(_14850:float)
--------------------------------------------------
第900行: half3 _15123 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15118.xy, uint(rint(_15118.z)), level(0.0)).xyz);
语法树结构:
assignment(=:half3)
  declaration(_15123:half3)
  function(half3:unknown)
    member_access(result_sSHAORGBVTSampler.sample.xyz:unknown)
      function(sSHAORGBVTSampler.sample:unknown)
        variable(sSHAORGBVTSamplerSmplr:sampler)
        member_access(_15118.xy:float2)
        function(uint:unknown)
          function(rint:unknown)
            member_access(_15118.z:float)
        function(level:unknown)
          literal(0.0:float)
--------------------------------------------------
第901行: half4 _15132 = half4(half(fast::clamp(_15021 - _15026, 0.0, 1.0)));
语法树结构:
assignment(=:half4)
  declaration(_15132:half4)
  function(half4:unknown)
    function(half:unknown)
      function(fast::clamp:unknown)
        operator(-:unknown)
          variable(_15021:float)
          variable(_15026:float)
        literal(0.0:float)
        literal(1.0:float)
--------------------------------------------------
第902行: half4 _15133 = mix(half4(_15097.x, _15097.y, _15097.z, _18103.w), half4(_15123.x, _15123.y, _15123.z, _18105.w), _15132);
语法树结构:
assignment(=:half4)
  declaration(_15133:half4)
  function(mix:unknown)
    function(half4:unknown)
      member_access(_15097.x:half)
      member_access(_15097.y:half)
      member_access(_15097.z:half)
      member_access(_18103.w:half)
    function(half4:unknown)
      member_access(_15123.x:half)
      member_access(_15123.y:half)
      member_access(_15123.z:half)
      member_access(_18105.w:half)
    variable(_15132:half4)
--------------------------------------------------
第903行: float3 _15140 = float3(_15078, _15059.y, _14831);
语法树结构:
assignment(=:float3)
  declaration(_15140:float3)
  function(float3:unknown)
    variable(_15078:float)
    member_access(_15059.y:float)
    variable(_14831:float)
--------------------------------------------------
第904行: half4 _18107;
语法树结构:
declaration(_18107:half4)
--------------------------------------------------
第905行: _18107.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15140.xy, uint(rint(_15140.z)), level(0.0)).x);
语法树结构:
assignment(=:unknown)
  member_access(_18107.w:unknown)
  function(half:unknown)
    member_access(result_sSHAOAlphaVTSampler.sample.x:unknown)
      function(sSHAOAlphaVTSampler.sample:unknown)
        variable(sSHAOAlphaVTSamplerSmplr:sampler)
        member_access(_15140.xy:float2)
        function(uint:unknown)
          function(rint:unknown)
            member_access(_15140.z:float)
        function(level:unknown)
          literal(0.0:float)
--------------------------------------------------
第906行: float3 _15152 = float3(_15078, _15059.y, _14854);
语法树结构:
assignment(=:float3)
  declaration(_15152:float3)
  function(float3:unknown)
    variable(_15078:float)
    member_access(_15059.y:float)
    variable(_14854:float)
--------------------------------------------------
第907行: half3 _15157 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15152.xy, uint(rint(_15152.z)), level(0.0)).xyz);
语法树结构:
assignment(=:half3)
  declaration(_15157:half3)
  function(half3:unknown)
    member_access(result_sSHAORGBVTSampler.sample.xyz:unknown)
      function(sSHAORGBVTSampler.sample:unknown)
        variable(sSHAORGBVTSamplerSmplr:sampler)
        member_access(_15152.xy:float2)
        function(uint:unknown)
          function(rint:unknown)
            member_access(_15152.z:float)
        function(level:unknown)
          literal(0.0:float)
--------------------------------------------------
第908行: float3 _15166 = float3(_15104, _15074.y, _14831);
语法树结构:
assignment(=:float3)
  declaration(_15166:float3)
  function(float3:unknown)
    variable(_15104:float)
    member_access(_15074.y:float)
    variable(_14831:float)
--------------------------------------------------
第909行: half4 _18109;
语法树结构:
declaration(_18109:half4)
--------------------------------------------------
第910行: _18109.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15166.xy, uint(rint(_15166.z)), level(0.0)).x);
语法树结构:
assignment(=:unknown)
  member_access(_18109.w:unknown)
  function(half:unknown)
    member_access(result_sSHAOAlphaVTSampler.sample.x:unknown)
      function(sSHAOAlphaVTSampler.sample:unknown)
        variable(sSHAOAlphaVTSamplerSmplr:sampler)
        member_access(_15166.xy:float2)
        function(uint:unknown)
          function(rint:unknown)
            member_access(_15166.z:float)
        function(level:unknown)
          literal(0.0:float)
--------------------------------------------------
第911行: float3 _15178 = float3(_15104, _15074.y, _14854);
语法树结构:
assignment(=:float3)
  declaration(_15178:float3)
  function(float3:unknown)
    variable(_15104:float)
    member_access(_15074.y:float)
    variable(_14854:float)
--------------------------------------------------
第912行: half3 _15183 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15178.xy, uint(rint(_15178.z)), level(0.0)).xyz);
语法树结构:
assignment(=:half3)
  declaration(_15183:half3)
  function(half3:unknown)
    member_access(result_sSHAORGBVTSampler.sample.xyz:unknown)
      function(sSHAORGBVTSampler.sample:unknown)
        variable(sSHAORGBVTSamplerSmplr:sampler)
        member_access(_15178.xy:float2)
        function(uint:unknown)
          function(rint:unknown)
            member_access(_15178.z:float)
        function(level:unknown)
          literal(0.0:float)
--------------------------------------------------
第913行: half4 _15193 = mix(half4(_15157.x, _15157.y, _15157.z, _18107.w), half4(_15183.x, _15183.y, _15183.z, _18109.w), _15132);
语法树结构:
assignment(=:half4)
  declaration(_15193:half4)
  function(mix:unknown)
    function(half4:unknown)
      member_access(_15157.x:half)
      member_access(_15157.y:half)
      member_access(_15157.z:half)
      member_access(_18107.w:half)
    function(half4:unknown)
      member_access(_15183.x:half)
      member_access(_15183.y:half)
      member_access(_15183.z:half)
      member_access(_18109.w:half)
    variable(_15132:half4)
--------------------------------------------------
第914行: float3 _15200 = float3(_15078, _15059.y, _14833);
语法树结构:
assignment(=:float3)
  declaration(_15200:float3)
  function(float3:unknown)
    variable(_15078:float)
    member_access(_15059.y:float)
    variable(_14833:float)
--------------------------------------------------
第915行: half4 _18111;
语法树结构:
declaration(_18111:half4)
--------------------------------------------------
第916行: _18111.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15200.xy, uint(rint(_15200.z)), level(0.0)).x);
语法树结构:
assignment(=:unknown)
  member_access(_18111.w:unknown)
  function(half:unknown)
    member_access(result_sSHAOAlphaVTSampler.sample.x:unknown)
      function(sSHAOAlphaVTSampler.sample:unknown)
        variable(sSHAOAlphaVTSamplerSmplr:sampler)
        member_access(_15200.xy:float2)
        function(uint:unknown)
          function(rint:unknown)
            member_access(_15200.z:float)
        function(level:unknown)
          literal(0.0:float)
--------------------------------------------------
第917行: float3 _15212 = float3(_15078, _15059.y, _14856);
语法树结构:
assignment(=:float3)
  declaration(_15212:float3)
  function(float3:unknown)
    variable(_15078:float)
    member_access(_15059.y:float)
    variable(_14856:float)
--------------------------------------------------
第918行: half3 _15217 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15212.xy, uint(rint(_15212.z)), level(0.0)).xyz);
语法树结构:
assignment(=:half3)
  declaration(_15217:half3)
  function(half3:unknown)
    member_access(result_sSHAORGBVTSampler.sample.xyz:unknown)
      function(sSHAORGBVTSampler.sample:unknown)
        variable(sSHAORGBVTSamplerSmplr:sampler)
        member_access(_15212.xy:float2)
        function(uint:unknown)
          function(rint:unknown)
            member_access(_15212.z:float)
        function(level:unknown)
          literal(0.0:float)
--------------------------------------------------
第919行: float3 _15226 = float3(_15104, _15074.y, _14833);
语法树结构:
assignment(=:float3)
  declaration(_15226:float3)
  function(float3:unknown)
    variable(_15104:float)
    member_access(_15074.y:float)
    variable(_14833:float)
--------------------------------------------------
第920行: half4 _18113;
语法树结构:
declaration(_18113:half4)
--------------------------------------------------
第921行: _18113.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15226.xy, uint(rint(_15226.z)), level(0.0)).x);
语法树结构:
assignment(=:unknown)
  member_access(_18113.w:unknown)
  function(half:unknown)
    member_access(result_sSHAOAlphaVTSampler.sample.x:unknown)
      function(sSHAOAlphaVTSampler.sample:unknown)
        variable(sSHAOAlphaVTSamplerSmplr:sampler)
        member_access(_15226.xy:float2)
        function(uint:unknown)
          function(rint:unknown)
            member_access(_15226.z:float)
        function(level:unknown)
          literal(0.0:float)
--------------------------------------------------
第922行: float3 _15238 = float3(_15104, _15074.y, _14856);
语法树结构:
assignment(=:float3)
  declaration(_15238:float3)
  function(float3:unknown)
    variable(_15104:float)
    member_access(_15074.y:float)
    variable(_14856:float)
--------------------------------------------------
第923行: half3 _15243 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15238.xy, uint(rint(_15238.z)), level(0.0)).xyz);
语法树结构:
assignment(=:half3)
  declaration(_15243:half3)
  function(half3:unknown)
    member_access(result_sSHAORGBVTSampler.sample.xyz:unknown)
      function(sSHAORGBVTSampler.sample:unknown)
        variable(sSHAORGBVTSamplerSmplr:sampler)
        member_access(_15238.xy:float2)
        function(uint:unknown)
          function(rint:unknown)
            member_access(_15238.z:float)
        function(level:unknown)
          literal(0.0:float)
--------------------------------------------------
第924行: half4 _15253 = mix(half4(_15217.x, _15217.y, _15217.z, _18111.w), half4(_15243.x, _15243.y, _15243.z, _18113.w), _15132);
语法树结构:
assignment(=:half4)
  declaration(_15253:half4)
  function(mix:unknown)
    function(half4:unknown)
      member_access(_15217.x:half)
      member_access(_15217.y:half)
      member_access(_15217.z:half)
      member_access(_18111.w:half)
    function(half4:unknown)
      member_access(_15243.x:half)
      member_access(_15243.y:half)
      member_access(_15243.z:half)
      member_access(_18113.w:half)
    variable(_15132:half4)
--------------------------------------------------
第925行: half _15255 = half(32.0);
语法树结构:
assignment(=:half)
  declaration(_15255:half)
  function(half:unknown)
    literal(32.0:float)
--------------------------------------------------
第926行: half _15258 = _15133.w * _15255;
语法树结构:
assignment(=:half)
  declaration(_15258:half)
  operator(*:unknown)
    member_access(_15133.w:half)
    variable(_15255:half)
--------------------------------------------------
第927行: half _15262 = _15193.w * _15255;
语法树结构:
assignment(=:half)
  declaration(_15262:half)
  operator(*:unknown)
    member_access(_15193.w:half)
    variable(_15255:half)
--------------------------------------------------
第928行: half _15266 = _15253.w * _15255;
语法树结构:
assignment(=:half)
  declaration(_15266:half)
  operator(*:unknown)
    member_access(_15253.w:half)
    variable(_15255:half)
--------------------------------------------------
第929行: half3 _15343 = half3(((float3(_15133.xyz) * float3(2.0)) - float3(1.0)) * float(_15258)).xyz;
语法树结构:
assignment(=:half3)
  declaration(_15343:half3)
  member_access(result_half3.xyz:unknown)
    function(half3:unknown)
      operator(*:unknown)
        operator(-:unknown)
          operator(*:unknown)
            function(float3:unknown)
              member_access(_15133.xyz:half3)
            function(float3:unknown)
              literal(2.0:float)
          function(float3:unknown)
            literal(1.0:float)
        function(float:unknown)
          variable(_15258:half)
--------------------------------------------------
第930行: half3 _18130;
语法树结构:
declaration(_18130:half3)
--------------------------------------------------
第931行: _18130.x = half(float(dot(_14556, _15343)) * 2.0);
语法树结构:
assignment(=:unknown)
  member_access(_18130.x:unknown)
  function(half:unknown)
    operator(*:unknown)
      function(float:unknown)
        function(dot:unknown)
          variable(_14556:half3)
          variable(_15343:half3)
      literal(2.0:float)
--------------------------------------------------
第932行: half3 _15352 = half3(((float3(_15193.xyz) * float3(2.0)) - float3(1.0)) * float(_15262)).xyz;
语法树结构:
assignment(=:half3)
  declaration(_15352:half3)
  member_access(result_half3.xyz:unknown)
    function(half3:unknown)
      operator(*:unknown)
        operator(-:unknown)
          operator(*:unknown)
            function(float3:unknown)
              member_access(_15193.xyz:half3)
            function(float3:unknown)
              literal(2.0:float)
          function(float3:unknown)
            literal(1.0:float)
        function(float:unknown)
          variable(_15262:half)
--------------------------------------------------
第933行: _18130.y = half(float(dot(_14556, _15352)) * 2.0);
语法树结构:
assignment(=:unknown)
  member_access(_18130.y:unknown)
  function(half:unknown)
    operator(*:unknown)
      function(float:unknown)
        function(dot:unknown)
          variable(_14556:half3)
          variable(_15352:half3)
      literal(2.0:float)
--------------------------------------------------
第934行: half3 _15361 = half3(((float3(_15253.xyz) * float3(2.0)) - float3(1.0)) * float(_15266)).xyz;
语法树结构:
assignment(=:half3)
  declaration(_15361:half3)
  member_access(result_half3.xyz:unknown)
    function(half3:unknown)
      operator(*:unknown)
        operator(-:unknown)
          operator(*:unknown)
            function(float3:unknown)
              member_access(_15253.xyz:half3)
            function(float3:unknown)
              literal(2.0:float)
          function(float3:unknown)
            literal(1.0:float)
        function(float:unknown)
          variable(_15266:half)
--------------------------------------------------
第935行: _18130.z = half(float(dot(_14556, _15361)) * 2.0);
语法树结构:
assignment(=:unknown)
  member_access(_18130.z:unknown)
  function(half:unknown)
    operator(*:unknown)
      function(float:unknown)
        function(dot:unknown)
          variable(_14556:half3)
          variable(_15361:half3)
      literal(2.0:float)
--------------------------------------------------
第936行: half3 _18819;
语法树结构:
declaration(_18819:half3)
--------------------------------------------------
第937行: if (_8590)
语法树结构:
variable(_8590:bool)
--------------------------------------------------
第939行: _18819 = half3(((float3(_15343) * float3(0.21199999749660491943359375)) + (float3(_15352) * float3(0.714999973773956298828125))) + (float3(_15361) * float3(0.0719999969005584716796875)));
语法树结构:
assignment(=:half3)
  variable(_18819:half3)
  function(half3:unknown)
    operator(+:unknown)
      operator(+:unknown)
        operator(*:unknown)
          function(float3:unknown)
            variable(_15343:half3)
          function(float3:unknown)
            literal(0.21199999749660491943359375:float)
        operator(*:unknown)
          function(float3:unknown)
            variable(_15352:half3)
          function(float3:unknown)
            literal(0.714999973773956298828125:float)
      operator(*:unknown)
        function(float3:unknown)
          variable(_15361:half3)
        function(float3:unknown)
          literal(0.0719999969005584716796875:float)
--------------------------------------------------
第943行: _18819 = _8393;
语法树结构:
assignment(=:half3)
  variable(_18819:half3)
  variable(_8393:half3)
--------------------------------------------------
第945行: _18818 = _18819;
语法树结构:
assignment(=:half3)
  variable(_18818:half3)
  variable(_18819:half3)
--------------------------------------------------
第946行: _18806 = max(half3(_15258, _15262, _15266) + (_18130 * half(mix(_Block1.SHGIParam2.z, 1.0, _14482))), _8393);
语法树结构:
assignment(=:half3)
  variable(_18806:half3)
  function(max:unknown)
    operator(+:unknown)
      function(half3:unknown)
        variable(_15258:half)
        variable(_15262:half)
        variable(_15266:half)
      operator(*:unknown)
        variable(_18130:half3)
        function(half:unknown)
          function(mix:unknown)
            member_access(_Block1.SHGIParam2.z:float)
            literal(1.0:float)
            variable(_14482:float)
    variable(_8393:half3)
--------------------------------------------------
第950行: float2 _15427 = ((_14747.xz - float2(0.5)) * 0.9375) + float2(0.5);
语法树结构:
assignment(=:float2)
  declaration(_15427:float2)
  operator(+:unknown)
    operator(*:unknown)
      operator(-:unknown)
        member_access(_14747.xz:float2)
        function(float2:unknown)
          literal(0.5:float)
      literal(0.9375:float)
    function(float2:unknown)
      literal(0.5:float)
--------------------------------------------------
第951行: float _15434 = _14717.x * 0.0041666668839752674102783203125;
语法树结构:
assignment(=:float)
  declaration(_15434:float)
  operator(*:unknown)
    member_access(_14717.x:float)
    literal(0.0041666668839752674102783203125:float)
--------------------------------------------------
第952行: float _15438 = ((_15434 - floor(_15434)) - 0.5) * 0.9375;
语法树结构:
assignment(=:float)
  declaration(_15438:float)
  operator(*:unknown)
    operator(-:unknown)
      operator(-:unknown)
        variable(_15434:float)
        function(floor:unknown)
          variable(_15434:float)
      literal(0.5:float)
    literal(0.9375:float)
--------------------------------------------------
第953行: float _15444 = _14717.z * 0.0041666668839752674102783203125;
语法树结构:
assignment(=:float)
  declaration(_15444:float)
  operator(*:unknown)
    member_access(_14717.z:float)
    literal(0.0041666668839752674102783203125:float)
--------------------------------------------------
第954行: float _15448 = ((_15444 - floor(_15444)) - 0.5) * 0.9375;
语法树结构:
assignment(=:float)
  declaration(_15448:float)
  operator(*:unknown)
    operator(-:unknown)
      operator(-:unknown)
        variable(_15444:float)
        function(floor:unknown)
          variable(_15444:float)
      literal(0.5:float)
    literal(0.9375:float)
--------------------------------------------------
第955行: float _15453 = _15427.x;
语法树结构:
assignment(=:float)
  declaration(_15453:float)
  member_access(_15427.x:float)
--------------------------------------------------
第956行: float3 _18143;
语法树结构:
declaration(_18143:float3)
--------------------------------------------------
第957行: _18143.x = (_15453 < (_15438 + 0.5)) ? fast::min(_15453, _15438 + 0.49609375) : fast::max(_15453, _15438 + 0.50390625);
语法树结构:
assignment(=:unknown)
  member_access(_18143.x:unknown)
  operator(?::unknown)
    operator(<:unknown)
      variable(_15453:float)
      operator(+:unknown)
        variable(_15438:float)
        literal(0.5:float)
    function(fast::min:unknown)
      variable(_15453:float)
      operator(+:unknown)
        variable(_15438:float)
        literal(0.49609375:float)
    function(fast::max:unknown)
      variable(_15453:float)
      operator(+:unknown)
        variable(_15438:float)
        literal(0.50390625:float)
--------------------------------------------------
第958行: float _15471 = _15427.y;
语法树结构:
assignment(=:float)
  declaration(_15471:float)
  member_access(_15427.y:float)
--------------------------------------------------
第959行: _18143.z = (_15471 < (_15448 + 0.5)) ? fast::min(_15471, _15448 + 0.49609375) : fast::max(_15471, _15448 + 0.50390625);
语法树结构:
assignment(=:unknown)
  member_access(_18143.z:unknown)
  operator(?::unknown)
    operator(<:unknown)
      variable(_15471:float)
      operator(+:unknown)
        variable(_15448:float)
        literal(0.5:float)
    function(fast::min:unknown)
      variable(_15471:float)
      operator(+:unknown)
        variable(_15448:float)
        literal(0.49609375:float)
    function(fast::max:unknown)
      variable(_15471:float)
      operator(+:unknown)
        variable(_15448:float)
        literal(0.50390625:float)
--------------------------------------------------
第960行: float _15492 = (_14747.y * 64.0) - 0.5;
语法树结构:
assignment(=:float)
  declaration(_15492:float)
  operator(-:unknown)
    operator(*:unknown)
      member_access(_14747.y:float)
      literal(64.0:float)
    literal(0.5:float)
--------------------------------------------------
第961行: float _15497 = floor(_15492);
语法树结构:
assignment(=:float)
  declaration(_15497:float)
  function(floor:unknown)
    variable(_15492:float)
--------------------------------------------------
第962行: uint _15500 = (_15492 < 0.0) ? 63u : uint(_15497);
语法树结构:
assignment(=:uint)
  declaration(_15500:uint)
  operator(?::unknown)
    operator(<:unknown)
      variable(_15492:float)
      literal(0.0:float)
    literal(63u:int)
    function(uint:unknown)
      variable(_15497:float)
--------------------------------------------------
第963行: uint _15503 = _15500 + 1u;
语法树结构:
assignment(=:uint)
  declaration(_15503:uint)
  operator(+:unknown)
    variable(_15500:uint)
    literal(1u:int)
--------------------------------------------------
第964行: uint _21308 = (_15503 >= 64u) ? 0u : _15503;
语法树结构:
assignment(=:uint)
  declaration(_21308:uint)
  operator(?::unknown)
    operator(>=:unknown)
      variable(_15503:uint)
      literal(64u:int)
    literal(0u:int)
    variable(_15503:uint)
--------------------------------------------------
第965行: float2 _15530 = (float2(float(_15500 & 7u), float(_15500 >> 3u)) + _18143.xz) * 0.125;
语法树结构:
assignment(=:float2)
  declaration(_15530:float2)
  operator(*:unknown)
    operator(+:unknown)
      function(float2:unknown)
        function(float:unknown)
          operator(&:unknown)
            variable(_15500:uint)
            literal(7u:int)
        function(float:unknown)
          operator(>>:unknown)
            variable(_15500:uint)
            literal(3u:int)
      member_access(_18143.xz:float2)
    literal(0.125:float)
--------------------------------------------------
第966行: float2 _15545 = (float2(float(_21308 & 7u), float(_21308 >> 3u)) + _18143.xz) * 0.125;
语法树结构:
assignment(=:float2)
  declaration(_15545:float2)
  operator(*:unknown)
    operator(+:unknown)
      function(float2:unknown)
        function(float:unknown)
          operator(&:unknown)
            variable(_21308:uint)
            literal(7u:int)
        function(float:unknown)
          operator(>>:unknown)
            variable(_21308:uint)
            literal(3u:int)
      member_access(_18143.xz:float2)
    literal(0.125:float)
--------------------------------------------------
第967行: float _15549 = _15530.x;
语法树结构:
assignment(=:float)
  declaration(_15549:float)
  member_access(_15530.x:float)
--------------------------------------------------
第968行: float3 _15551 = float3(_15549, _15530.y, _14827);
语法树结构:
assignment(=:float3)
  declaration(_15551:float3)
  function(float3:unknown)
    variable(_15549:float)
    member_access(_15530.y:float)
    variable(_14827:float)
--------------------------------------------------
第969行: half3 _18151;
语法树结构:
declaration(_18151:half3)
--------------------------------------------------
第970行: _18151.x = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15551.xy, uint(rint(_15551.z)), level(0.0)).x);
语法树结构:
assignment(=:unknown)
  member_access(_18151.x:unknown)
  function(half:unknown)
    member_access(result_sSHAOAlphaVTSampler.sample.x:unknown)
      function(sSHAOAlphaVTSampler.sample:unknown)
        variable(sSHAOAlphaVTSamplerSmplr:sampler)
        member_access(_15551.xy:float2)
        function(uint:unknown)
          function(rint:unknown)
            member_access(_15551.z:float)
        function(level:unknown)
          literal(0.0:float)
--------------------------------------------------
第971行: float _15561 = _15545.x;
语法树结构:
assignment(=:float)
  declaration(_15561:float)
  member_access(_15545.x:float)
--------------------------------------------------
第972行: float3 _15563 = float3(_15561, _15545.y, _14827);
语法树结构:
assignment(=:float3)
  declaration(_15563:float3)
  function(float3:unknown)
    variable(_15561:float)
    member_access(_15545.y:float)
    variable(_14827:float)
--------------------------------------------------
第973行: half3 _18153;
语法树结构:
declaration(_18153:half3)
--------------------------------------------------
第974行: _18153.x = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15563.xy, uint(rint(_15563.z)), level(0.0)).x);
语法树结构:
assignment(=:unknown)
  member_access(_18153.x:unknown)
  function(half:unknown)
    member_access(result_sSHAOAlphaVTSampler.sample.x:unknown)
      function(sSHAOAlphaVTSampler.sample:unknown)
        variable(sSHAOAlphaVTSamplerSmplr:sampler)
        member_access(_15563.xy:float2)
        function(uint:unknown)
          function(rint:unknown)
            member_access(_15563.z:float)
        function(level:unknown)
          literal(0.0:float)
--------------------------------------------------
第975行: float3 _15575 = float3(_15549, _15530.y, _14831);
语法树结构:
assignment(=:float3)
  declaration(_15575:float3)
  function(float3:unknown)
    variable(_15549:float)
    member_access(_15530.y:float)
    variable(_14831:float)
--------------------------------------------------
第976行: _18151.y = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15575.xy, uint(rint(_15575.z)), level(0.0)).x);
语法树结构:
assignment(=:unknown)
  member_access(_18151.y:unknown)
  function(half:unknown)
    member_access(result_sSHAOAlphaVTSampler.sample.x:unknown)
      function(sSHAOAlphaVTSampler.sample:unknown)
        variable(sSHAOAlphaVTSamplerSmplr:sampler)
        member_access(_15575.xy:float2)
        function(uint:unknown)
          function(rint:unknown)
            member_access(_15575.z:float)
        function(level:unknown)
          literal(0.0:float)
--------------------------------------------------
第977行: float3 _15587 = float3(_15561, _15545.y, _14831);
语法树结构:
assignment(=:float3)
  declaration(_15587:float3)
  function(float3:unknown)
    variable(_15561:float)
    member_access(_15545.y:float)
    variable(_14831:float)
--------------------------------------------------
第978行: _18153.y = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15587.xy, uint(rint(_15587.z)), level(0.0)).x);
语法树结构:
assignment(=:unknown)
  member_access(_18153.y:unknown)
  function(half:unknown)
    member_access(result_sSHAOAlphaVTSampler.sample.x:unknown)
      function(sSHAOAlphaVTSampler.sample:unknown)
        variable(sSHAOAlphaVTSamplerSmplr:sampler)
        member_access(_15587.xy:float2)
        function(uint:unknown)
          function(rint:unknown)
            member_access(_15587.z:float)
        function(level:unknown)
          literal(0.0:float)
--------------------------------------------------
第979行: float3 _15599 = float3(_15549, _15530.y, _14833);
语法树结构:
assignment(=:float3)
  declaration(_15599:float3)
  function(float3:unknown)
    variable(_15549:float)
    member_access(_15530.y:float)
    variable(_14833:float)
--------------------------------------------------
第980行: _18151.z = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15599.xy, uint(rint(_15599.z)), level(0.0)).x);
语法树结构:
assignment(=:unknown)
  member_access(_18151.z:unknown)
  function(half:unknown)
    member_access(result_sSHAOAlphaVTSampler.sample.x:unknown)
      function(sSHAOAlphaVTSampler.sample:unknown)
        variable(sSHAOAlphaVTSamplerSmplr:sampler)
        member_access(_15599.xy:float2)
        function(uint:unknown)
          function(rint:unknown)
            member_access(_15599.z:float)
        function(level:unknown)
          literal(0.0:float)
--------------------------------------------------
第981行: float3 _15611 = float3(_15561, _15545.y, _14833);
语法树结构:
assignment(=:float3)
  declaration(_15611:float3)
  function(float3:unknown)
    variable(_15561:float)
    member_access(_15545.y:float)
    variable(_14833:float)
--------------------------------------------------
第982行: _18153.z = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15611.xy, uint(rint(_15611.z)), level(0.0)).x);
语法树结构:
assignment(=:unknown)
  member_access(_18153.z:unknown)
  function(half:unknown)
    member_access(result_sSHAOAlphaVTSampler.sample.x:unknown)
      function(sSHAOAlphaVTSampler.sample:unknown)
        variable(sSHAOAlphaVTSamplerSmplr:sampler)
        member_access(_15611.xy:float2)
        function(uint:unknown)
          function(rint:unknown)
            member_access(_15611.z:float)
        function(level:unknown)
          literal(0.0:float)
--------------------------------------------------
第983行: half3 _15622 = mix(_18151, _18153, half3(half(fast::clamp(_15492 - _15497, 0.0, 1.0))));
语法树结构:
assignment(=:half3)
  declaration(_15622:half3)
  function(mix:unknown)
    variable(_18151:half3)
    variable(_18153:half3)
    function(half3:unknown)
      function(half:unknown)
        function(fast::clamp:unknown)
          operator(-:unknown)
            variable(_15492:float)
            variable(_15497:float)
          literal(0.0:float)
          literal(1.0:float)
--------------------------------------------------
第984行: half _15623 = half(32.0);
语法树结构:
assignment(=:half)
  declaration(_15623:half)
  function(half:unknown)
    literal(32.0:float)
--------------------------------------------------
第985行: half3 _18164;
语法树结构:
declaration(_18164:half3)
--------------------------------------------------
第986行: _18164.x = _15622.x * _15623;
语法树结构:
assignment(=:unknown)
  member_access(_18164.x:unknown)
  operator(*:unknown)
    member_access(_15622.x:half)
    variable(_15623:half)
--------------------------------------------------
第987行: _18164.y = _15622.y * _15623;
语法树结构:
assignment(=:unknown)
  member_access(_18164.y:unknown)
  operator(*:unknown)
    member_access(_15622.y:half)
    variable(_15623:half)
--------------------------------------------------
第988行: _18164.z = _15622.z * _15623;
语法树结构:
assignment(=:unknown)
  member_access(_18164.z:unknown)
  operator(*:unknown)
    member_access(_15622.z:half)
    variable(_15623:half)
--------------------------------------------------
第989行: _18818 = _8393;
语法树结构:
assignment(=:half3)
  variable(_18818:half3)
  variable(_8393:half3)
--------------------------------------------------
第990行: _18806 = _18164;
语法树结构:
assignment(=:half3)
  variable(_18806:half3)
  variable(_18164:half3)
--------------------------------------------------
第992行: float3 _15658 = (((in.IN_WorldPosition.xyz - _14717) * float3(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125)) * 2.0) - float3(1.0);
语法树结构:
assignment(=:float3)
  declaration(_15658:float3)
  operator(-:unknown)
    operator(*:unknown)
      operator(*:unknown)
        operator(-:unknown)
          member_access(in.IN_WorldPosition.xyz:float3)
          variable(_14717:float3)
        function(float3:unknown)
          literal(0.0041666668839752674102783203125:float)
          literal(0.0078125:float)
          literal(0.0041666668839752674102783203125:float)
      literal(2.0:float)
    function(float3:unknown)
      literal(1.0:float)
--------------------------------------------------
第993行: float3 _15661 = _15658 * _15658;
语法树结构:
assignment(=:float3)
  declaration(_15661:float3)
  operator(*:unknown)
    variable(_15658:float3)
    variable(_15658:float3)
--------------------------------------------------
第994行: float3 _15664 = _15661 * _15661;
语法树结构:
assignment(=:float3)
  declaration(_15664:float3)
  operator(*:unknown)
    variable(_15661:float3)
    variable(_15661:float3)
--------------------------------------------------
第995行: half3 _18822;
语法树结构:
declaration(_18822:half3)
--------------------------------------------------
第996行: half3 _18823;
语法树结构:
declaration(_18823:half3)
--------------------------------------------------
第997行: if ((max(int(_14451), 2) == 3) && ((_8397 & 32768u) > 0u))
语法树结构:
function(max:unknown)
  function(int:unknown)
    variable(_14451:uint)
--------------------------------------------------
第999行: half3 _14921 = half3(half(1.0 - fast::clamp(fast::max(_15664.x, fast::max(_15664.y, _15664.z)), 0.0, 1.0)));
语法树结构:
assignment(=:half3)
  declaration(_14921:half3)
  function(half3:unknown)
    function(half:unknown)
      operator(-:unknown)
        literal(1.0:float)
        function(fast::clamp:unknown)
          function(fast::max:unknown)
            member_access(_15664.x:float)
            function(fast::max:unknown)
              member_access(_15664.y:float)
              member_access(_15664.z:float)
          literal(0.0:float)
          literal(1.0:float)
--------------------------------------------------
第1000行: _18823 = _18818 * _14921;
语法树结构:
assignment(=:half3)
  variable(_18823:half3)
  operator(*:unknown)
    variable(_18818:half3)
    variable(_14921:half3)
--------------------------------------------------
第1001行: _18822 = _18806 * _14921;
语法树结构:
assignment(=:half3)
  variable(_18822:half3)
  operator(*:unknown)
    variable(_18806:half3)
    variable(_14921:half3)
--------------------------------------------------
第1005行: _18823 = _18818;
语法树结构:
assignment(=:half3)
  variable(_18823:half3)
  variable(_18818:half3)
--------------------------------------------------
第1006行: _18822 = _18806;
语法树结构:
assignment(=:half3)
  variable(_18822:half3)
  variable(_18806:half3)
--------------------------------------------------
第1008行: _18827 = _18823;
语法树结构:
assignment(=:half3)
  variable(_18827:half3)
  variable(_18823:half3)
--------------------------------------------------
第1009行: _18825 = _18822;
语法树结构:
assignment(=:half3)
  variable(_18825:half3)
  variable(_18822:half3)
--------------------------------------------------
第1013行: _18827 = _8393;
语法树结构:
assignment(=:half3)
  variable(_18827:half3)
  variable(_8393:half3)
--------------------------------------------------
第1014行: _18825 = _8393;
语法树结构:
assignment(=:half3)
  variable(_18825:half3)
  variable(_8393:half3)
--------------------------------------------------
第1016行: _18826 = _18827;
语法树结构:
assignment(=:half3)
  variable(_18826:half3)
  variable(_18827:half3)
--------------------------------------------------
第1017行: _18824 = _18825;
语法树结构:
assignment(=:half3)
  variable(_18824:half3)
  variable(_18825:half3)
--------------------------------------------------
第1021行: _18826 = _8393;
语法树结构:
assignment(=:half3)
  variable(_18826:half3)
  variable(_8393:half3)
--------------------------------------------------
第1022行: _18824 = _8393;
语法树结构:
assignment(=:half3)
  variable(_18824:half3)
  variable(_8393:half3)
--------------------------------------------------
第1024行: half3 _14565 = half3(float3(0.0));
语法树结构:
assignment(=:half3)
  declaration(_14565:half3)
  function(half3:unknown)
    function(float3:unknown)
      literal(0.0:float)
--------------------------------------------------
第1025行: half3 _18830;
语法树结构:
declaration(_18830:half3)
--------------------------------------------------
第1026行: if (_8590)
语法树结构:
variable(_8590:bool)
--------------------------------------------------
第1028行: float3 _14569 = float3(_18824);
语法树结构:
assignment(=:float3)
  declaration(_14569:float3)
  function(float3:unknown)
    variable(_18824:half3)
--------------------------------------------------
第1029行: float _14575 = float(dot(_18826, _18826));
语法树结构:
assignment(=:float)
  declaration(_14575:float)
  function(float:unknown)
    function(dot:unknown)
      variable(_18826:half3)
      variable(_18826:half3)
--------------------------------------------------
第1030行: half3 _18831;
语法树结构:
declaration(_18831:half3)
--------------------------------------------------
第1031行: if ((float(half(((0.21267099678516387939453125 * _14569.x) + (0.71516001224517822265625 * _14569.y)) + (0.072168998420238494873046875 * _14569.z))) > 0.001000000047497451305389404296875) && (_14575 > 9.9999999747524270787835121154785e-07))
语法树结构:
function(float:unknown)
  function(half:unknown)
    operator(*:unknown)
      literal(0.21267099678516387939453125:float)
      member_access(_14569.x:float)
--------------------------------------------------
第1033行: float _14592 = fast::clamp(_9109.y, 0.0, 1.0) * 0.75;
语法树结构:
assignment(=:float)
  declaration(_14592:float)
  operator(*:unknown)
    function(fast::clamp:unknown)
      member_access(_9109.y:float)
      literal(0.0:float)
      literal(1.0:float)
    literal(0.75:float)
--------------------------------------------------
第1034行: float3 _14600 = (float3(_18826) / float3(sqrt(_14575))) * float3(fast::clamp(1.0 - (_14592 * _14592), 0.0, 1.0));
语法树结构:
assignment(=:float3)
  declaration(_14600:float3)
  operator(*:unknown)
    operator(/:unknown)
      function(float3:unknown)
        variable(_18826:half3)
      function(float3:unknown)
        function(sqrt:unknown)
          variable(_14575:float)
    function(float3:unknown)
      function(fast::clamp:unknown)
        operator(-:unknown)
          literal(1.0:float)
          operator(*:unknown)
            variable(_14592:float)
            variable(_14592:float)
        literal(0.0:float)
        literal(1.0:float)
--------------------------------------------------
第1035行: float _14604 = mix(_11560, 1.0, 0.25);
语法树结构:
assignment(=:float)
  declaration(_14604:float)
  function(mix:unknown)
    variable(_11560:float)
    literal(1.0:float)
    literal(0.25:float)
--------------------------------------------------
第1036行: float _14612 = fast::clamp(dot(_9109, fast::normalize(_8373 + _14600)), 0.0, 1.0);
语法树结构:
assignment(=:float)
  declaration(_14612:float)
  function(fast::clamp:unknown)
    function(dot:unknown)
      variable(_9109:float3)
      function(fast::normalize:unknown)
        operator(+:unknown)
          variable(_8373:float3)
          variable(_14600:float3)
    literal(0.0:float)
    literal(1.0:float)
--------------------------------------------------
第1037行: float _15697 = _14604 * _14604;
语法树结构:
assignment(=:float)
  declaration(_15697:float)
  operator(*:unknown)
    variable(_14604:float)
    variable(_14604:float)
--------------------------------------------------
第1038行: float _15710 = _15697 / (((((_14612 * _15697) * _15697) - _14612) * _14612) + 1.0);
语法树结构:
assignment(=:float)
  declaration(_15710:float)
  operator(/:unknown)
    variable(_15697:float)
    operator(+:unknown)
      operator(*:unknown)
        operator(-:unknown)
          operator(*:unknown)
            operator(*:unknown)
              variable(_14612:float)
              variable(_15697:float)
            variable(_15697:float)
          variable(_14612:float)
        variable(_14612:float)
      literal(1.0:float)
--------------------------------------------------
第1039行: _18831 = half3(float3(_10557 * _18824) * float3((fast::min(1000.0, (_15710 * _15710) * 0.3183098733425140380859375) * fast::clamp(dot(_9109, _14600), 0.0, 1.0)) * _Block1.SHGIParam.y));
语法树结构:
assignment(=:half3)
  variable(_18831:half3)
  function(half3:unknown)
    operator(*:unknown)
      function(float3:unknown)
        operator(*:unknown)
          variable(_10557:half3)
          variable(_18824:half3)
      function(float3:unknown)
        operator(*:unknown)
          operator(*:unknown)
            function(fast::min:unknown)
              literal(1000.0:float)
              operator(*:unknown)
                operator(*:unknown)
                  variable(_15710:float)
                  variable(_15710:float)
                literal(0.3183098733425140380859375:float)
            function(fast::clamp:unknown)
              function(dot:unknown)
                variable(_9109:float3)
                variable(_14600:float3)
              literal(0.0:float)
              literal(1.0:float)
          member_access(_Block1.SHGIParam.y:float)
--------------------------------------------------
第1043行: _18831 = _14565;
语法树结构:
assignment(=:half3)
  variable(_18831:half3)
  variable(_14565:half3)
--------------------------------------------------
第1045行: _18830 = _18831;
语法树结构:
assignment(=:half3)
  variable(_18830:half3)
  variable(_18831:half3)
--------------------------------------------------
第1049行: _18830 = _14565;
语法树结构:
assignment(=:half3)
  variable(_18830:half3)
  variable(_14565:half3)
--------------------------------------------------
第1051行: float _14641 = float(half(mix(_Block1.SHGIParam2.y, 1.0, _14482)));
语法树结构:
assignment(=:float)
  declaration(_14641:float)
  function(float:unknown)
    function(half:unknown)
      function(mix:unknown)
        member_access(_Block1.SHGIParam2.y:float)
        literal(1.0:float)
        variable(_14482:float)
--------------------------------------------------
第1052行: _18859 = _18830 * half3(half(_Block1.SHGIParam.y * _14641));
语法树结构:
assignment(=:half3)
  variable(_18859:half3)
  operator(*:unknown)
    variable(_18830:half3)
    function(half3:unknown)
      function(half:unknown)
        operator(*:unknown)
          member_access(_Block1.SHGIParam.y:float)
          variable(_14641:float)
--------------------------------------------------
第1053行: _18832 = _18824 * half3(half(_Block1.SHGIParam.x * _14641));
语法树结构:
assignment(=:half3)
  variable(_18832:half3)
  operator(*:unknown)
    variable(_18824:half3)
    function(half3:unknown)
      function(half:unknown)
        operator(*:unknown)
          member_access(_Block1.SHGIParam.x:float)
          variable(_14641:float)
--------------------------------------------------
第1057行: _18859 = _8393;
语法树结构:
assignment(=:half3)
  variable(_18859:half3)
  variable(_8393:half3)
--------------------------------------------------
第1058行: _18832 = _8393;
语法树结构:
assignment(=:half3)
  variable(_18832:half3)
  variable(_8393:half3)
--------------------------------------------------
第1060行: _19028 = _8560 + (_18832 * _18329);
语法树结构:
assignment(=:half3)
  variable(_19028:half3)
  operator(+:unknown)
    variable(_8560:half3)
    operator(*:unknown)
      variable(_18832:half3)
      variable(_18329:half)
--------------------------------------------------
第1061行: _18989 = _8565 + float3(_18859 * _18329);
语法树结构:
assignment(=:float3)
  variable(_18989:float3)
  operator(+:unknown)
    variable(_8565:float3)
    function(float3:unknown)
      operator(*:unknown)
        variable(_18859:half3)
        variable(_18329:half)
--------------------------------------------------
第1065行: _19028 = _8560;
语法树结构:
assignment(=:half3)
  variable(_19028:half3)
  variable(_8560:half3)
--------------------------------------------------
第1066行: _18989 = _8565;
语法树结构:
assignment(=:float3)
  variable(_18989:float3)
  variable(_8565:float3)
--------------------------------------------------
第1068行: float _15792 = fast::clamp(dot(_9109, fast::normalize(_8373 + _8373)), 0.0, 1.0);
语法树结构:
assignment(=:float)
  declaration(_15792:float)
  function(fast::clamp:unknown)
    function(dot:unknown)
      variable(_9109:float3)
      function(fast::normalize:unknown)
        operator(+:unknown)
          variable(_8373:float3)
          variable(_8373:float3)
    literal(0.0:float)
    literal(1.0:float)
--------------------------------------------------
第1069行: float _15839 = _12049 / (((((_15792 * _12049) * _12049) - _15792) * _15792) + 1.0);
语法树结构:
assignment(=:float)
  declaration(_15839:float)
  operator(/:unknown)
    variable(_12049:float)
    operator(+:unknown)
      operator(*:unknown)
        operator(-:unknown)
          operator(*:unknown)
            operator(*:unknown)
              variable(_15792:float)
              variable(_12049:float)
            variable(_12049:float)
          variable(_15792:float)
        variable(_15792:float)
      literal(1.0:float)
--------------------------------------------------
第1070行: float _15805 = float(half(fast::clamp(_8378, 0.0, 1.0)));
语法树结构:
assignment(=:float)
  declaration(_15805:float)
  function(float:unknown)
    function(half:unknown)
      function(fast::clamp:unknown)
        variable(_8378:float)
        literal(0.0:float)
        literal(1.0:float)
--------------------------------------------------
第1071行: float _15854 = _12049 * 0.5;
语法树结构:
assignment(=:float)
  declaration(_15854:float)
  operator(*:unknown)
    variable(_12049:float)
    literal(0.5:float)
--------------------------------------------------
第1072行: float _15858 = 1.0 - _15854;
语法树结构:
assignment(=:float)
  declaration(_15858:float)
  operator(-:unknown)
    literal(1.0:float)
    variable(_15854:float)
--------------------------------------------------
第1073行: float _15861 = (_15805 * _15858) + _15854;
语法树结构:
assignment(=:float)
  declaration(_15861:float)
  operator(+:unknown)
    operator(*:unknown)
      variable(_15805:float)
      variable(_15858:float)
    variable(_15854:float)
--------------------------------------------------
第1074行: half3 _15813 = half3(_12025);
语法树结构:
assignment(=:half3)
  declaration(_15813:half3)
  function(half3:unknown)
    variable(_12025:float3)
--------------------------------------------------
第1075行: float3 _15762 = float3(_10569);
语法树结构:
assignment(=:float3)
  declaration(_15762:float3)
  function(float3:unknown)
    variable(_10569:half3)
--------------------------------------------------
第1076行: float3 _15920 = _Block1.cLocalVirtualLitPos.xyz + _Block1.cVirtualLitParam.xyz;
语法树结构:
assignment(=:float3)
  declaration(_15920:float3)
  operator(+:unknown)
    member_access(_Block1.cLocalVirtualLitPos.xyz:float3)
    member_access(_Block1.cVirtualLitParam.xyz:float3)
--------------------------------------------------
第1077行: float3 _15960 = ((((fast::normalize(cross(_8373, float3(0.0, 1.0, 0.0))) * _15920.x) + float3(0.0, _15920.y, 0.0)) + (_8373 * _15920.z)) + (float4(0.0, 0.0, 0.0, 1.0) * _Block1.World)) - in.IN_WorldPosition.xyz;
语法树结构:
assignment(=:float3)
  declaration(_15960:float3)
  operator(-:unknown)
    operator(+:unknown)
      operator(+:unknown)
        operator(+:unknown)
          operator(*:unknown)
            function(fast::normalize:unknown)
              function(cross:unknown)
                variable(_8373:float3)
                function(float3:unknown)
                  literal(0.0:float)
                  literal(1.0:float)
                  literal(0.0:float)
            member_access(_15920.x:float)
          function(float3:unknown)
            literal(0.0:float)
            member_access(_15920.y:float)
            literal(0.0:float)
        operator(*:unknown)
          variable(_8373:float3)
          member_access(_15920.z:float)
      operator(*:unknown)
        function(float4:unknown)
          literal(0.0:float)
          literal(0.0:float)
          literal(0.0:float)
          literal(1.0:float)
        member_access(_Block1.World:float3x4)
    member_access(in.IN_WorldPosition.xyz:float3)
--------------------------------------------------
第1078行: float3 _15970 = mix(_15960, fast::normalize(_15960), float3(step(0.0, _Block1.cLocalVirtualLitColor.w)));
语法树结构:
assignment(=:float3)
  declaration(_15970:float3)
  function(mix:unknown)
    variable(_15960:float3)
    function(fast::normalize:unknown)
      variable(_15960:float3)
    function(float3:unknown)
      function(step:unknown)
        literal(0.0:float)
        member_access(_Block1.cLocalVirtualLitColor.w:float)
--------------------------------------------------
第1079行: half _15974 = half(dot(_15970, _15970));
语法树结构:
assignment(=:half)
  declaration(_15974:half)
  function(half:unknown)
    function(dot:unknown)
      variable(_15970:float3)
      variable(_15970:float3)
--------------------------------------------------
第1080行: float3 _15976 = fast::normalize(_15970);
语法树结构:
assignment(=:float3)
  declaration(_15976:float3)
  function(fast::normalize:unknown)
    variable(_15970:float3)
--------------------------------------------------
第1081行: half _15979 = _15974 * half(1.0 / (_Block1.cLocalVirtualLitCustom.x * _Block1.cLocalVirtualLitCustom.x));
语法树结构:
assignment(=:half)
  declaration(_15979:half)
  operator(*:unknown)
    variable(_15974:half)
    function(half:unknown)
      operator(/:unknown)
        literal(1.0:float)
        operator(*:unknown)
          member_access(_Block1.cLocalVirtualLitCustom.x:float)
          member_access(_Block1.cLocalVirtualLitCustom.x:float)
--------------------------------------------------
第1082行: float _15986 = fast::clamp(1.0 - float(_15979 * _15979), 0.0, 1.0);
语法树结构:
assignment(=:float)
  declaration(_15986:float)
  function(fast::clamp:unknown)
    operator(-:unknown)
      literal(1.0:float)
      function(float:unknown)
        operator(*:unknown)
          variable(_15979:half)
          variable(_15979:half)
    literal(0.0:float)
    literal(1.0:float)
--------------------------------------------------
第1083行: half _16031 = half((fast::clamp(dot(_9109, _15976), 0.0, 1.0) * _Block1.cLocalVirtualLitCustom.z) + (1.0 - _Block1.cLocalVirtualLitCustom.z));
语法树结构:
assignment(=:half)
  declaration(_16031:half)
  function(half:unknown)
    operator(+:unknown)
      operator(*:unknown)
        function(fast::clamp:unknown)
          function(dot:unknown)
            variable(_9109:float3)
            variable(_15976:float3)
          literal(0.0:float)
          literal(1.0:float)
        member_access(_Block1.cLocalVirtualLitCustom.z:float)
      operator(-:unknown)
        literal(1.0:float)
        member_access(_Block1.cLocalVirtualLitCustom.z:float)
--------------------------------------------------
第1084行: float _16112 = fast::clamp(dot(_9109, fast::normalize(_8373 + _15976)), 0.0, 1.0);
语法树结构:
assignment(=:float)
  declaration(_16112:float)
  function(fast::clamp:unknown)
    function(dot:unknown)
      variable(_9109:float3)
      function(fast::normalize:unknown)
        operator(+:unknown)
          variable(_8373:float3)
          variable(_15976:float3)
    literal(0.0:float)
    literal(1.0:float)
--------------------------------------------------
第1085行: float _16159 = _12049 / (((((_16112 * _12049) * _12049) - _16112) * _16112) + 1.0);
语法树结构:
assignment(=:float)
  declaration(_16159:float)
  operator(/:unknown)
    variable(_12049:float)
    operator(+:unknown)
      operator(*:unknown)
        operator(-:unknown)
          operator(*:unknown)
            operator(*:unknown)
              variable(_16112:float)
              variable(_12049:float)
            variable(_12049:float)
          variable(_16112:float)
        variable(_16112:float)
      literal(1.0:float)
--------------------------------------------------
第1086行: float _8657 = float(_18329);
语法树结构:
assignment(=:float)
  declaration(_8657:float)
  function(float:unknown)
    variable(_18329:half)
--------------------------------------------------
第1087行: half _8696 = half(fast::min(_8657, mix(abs(_Block1.cVisibilitySH[0].w), 1.0, _Block1.WorldProbeInfo.x)));
语法树结构:
assignment(=:half)
  declaration(_8696:half)
  function(half:unknown)
    function(fast::min:unknown)
      variable(_8657:float)
      function(mix:unknown)
        function(abs:unknown)
          member_access(_Block1.cVisibilitySH[0].w:float)
        literal(1.0:float)
        member_access(_Block1.WorldProbeInfo.x:float)
--------------------------------------------------
第1088行: half _16335 = half(-1.023326873779296875);
语法树结构:
assignment(=:half)
  declaration(_16335:half)
  function(half:unknown)
    operator(-:unknown)
      literal(1.023326873779296875:float)
--------------------------------------------------
第1089行: half _16336 = half(1.023326873779296875);
语法树结构:
assignment(=:half)
  declaration(_16336:half)
  function(half:unknown)
    literal(1.023326873779296875:float)
--------------------------------------------------
第1090行: half _16342 = _9064.y;
语法树结构:
assignment(=:half)
  declaration(_16342:half)
  member_access(_9064.y:half)
--------------------------------------------------
第1091行: half _16351 = half(-0.858085691928863525390625);
语法树结构:
assignment(=:half)
  declaration(_16351:half)
  function(half:unknown)
    operator(-:unknown)
      literal(0.858085691928863525390625:float)
--------------------------------------------------
第1092行: half4 _16356 = half4(_16351, half(0.7431240081787109375), _16351, half(0.4290428459644317626953125));
语法树结构:
assignment(=:half4)
  declaration(_16356:half4)
  function(half4:unknown)
    variable(_16351:half)
    function(half:unknown)
      literal(0.7431240081787109375:float)
    variable(_16351:half)
    function(half:unknown)
      literal(0.4290428459644317626953125:float)
--------------------------------------------------
第1093行: half _16361 = _9064.z;
语法树结构:
assignment(=:half)
  declaration(_16361:half)
  member_access(_9064.z:half)
--------------------------------------------------
第1094行: half _16369 = _9064.x;
语法树结构:
assignment(=:half)
  declaration(_16369:half)
  member_access(_9064.x:half)
--------------------------------------------------
第1095行: half4 _16385 = _16356 * half4(_16342 * _16361, _16361 * _16361, _16369 * _16361, (_16369 * _16369) - (_16342 * _16342));
语法树结构:
assignment(=:half4)
  declaration(_16385:half4)
  operator(*:unknown)
    variable(_16356:half4)
    function(half4:unknown)
      operator(*:unknown)
        variable(_16342:half)
        variable(_16361:half)
      operator(*:unknown)
        variable(_16361:half)
        variable(_16361:half)
      operator(*:unknown)
        variable(_16369:half)
        variable(_16361:half)
      operator(-:unknown)
        operator(*:unknown)
          variable(_16369:half)
          variable(_16369:half)
        operator(*:unknown)
          variable(_16342:half)
          variable(_16342:half)
--------------------------------------------------
第1096行: half _16387 = half(-0.2477079927921295166015625);
语法树结构:
assignment(=:half)
  declaration(_16387:half)
  function(half:unknown)
    operator(-:unknown)
      literal(0.2477079927921295166015625:float)
--------------------------------------------------
第1097行: _16385.y = _16385.y + _16387;
语法树结构:
assignment(=:unknown)
  member_access(_16385.y:unknown)
  operator(+:unknown)
    member_access(_16385.y:half)
    variable(_16387:half)
--------------------------------------------------
第1098行: half3 _16279 = half3(_Block1.cSHCoefficients[0].xyz * float3(half3(half(0.886226952075958251953125))));
语法树结构:
assignment(=:half3)
  declaration(_16279:half3)
  function(half3:unknown)
    operator(*:unknown)
      member_access(_Block1.cSHCoefficients[0].xyz:float3)
      function(float3:unknown)
        function(half3:unknown)
          function(half:unknown)
            literal(0.886226952075958251953125:float)
--------------------------------------------------
第1099行: float4 _16284 = float4(half4(_16335, _16336, _16335, half(0.858085691928863525390625 * float(_16342))) * _9064.yzxx);
语法树结构:
assignment(=:float4)
  declaration(_16284:float4)
  function(float4:unknown)
    operator(*:unknown)
      function(half4:unknown)
        variable(_16335:half)
        variable(_16336:half)
        variable(_16335:half)
        function(half:unknown)
          operator(*:unknown)
            literal(0.858085691928863525390625:float)
            function(float:unknown)
              variable(_16342:half)
      member_access(_9064.yzxx:half4)
--------------------------------------------------
第1100行: float4 _16306 = float4(_16385);
语法树结构:
assignment(=:float4)
  declaration(_16306:float4)
  function(float4:unknown)
    variable(_16385:half4)
--------------------------------------------------
第1101行: half3 _16397 = half3(float3(0.081409998238086700439453125, 0.74361002445220947265625, -0.66364002227783203125));
语法树结构:
assignment(=:half3)
  declaration(_16397:half3)
  function(half3:unknown)
    function(float3:unknown)
      literal(0.081409998238086700439453125:float)
      literal(0.74361002445220947265625:float)
      operator(-:unknown)
        literal(0.66364002227783203125:float)
--------------------------------------------------
第1102行: half _16509 = _16397.y;
语法树结构:
assignment(=:half)
  declaration(_16509:half)
  member_access(_16397.y:half)
--------------------------------------------------
第1103行: half _16528 = _16397.z;
语法树结构:
assignment(=:half)
  declaration(_16528:half)
  member_access(_16397.z:half)
--------------------------------------------------
第1104行: half _16536 = _16397.x;
语法树结构:
assignment(=:half)
  declaration(_16536:half)
  member_access(_16397.x:half)
--------------------------------------------------
第1105行: half4 _16552 = _16356 * half4(_16509 * _16528, _16528 * _16528, _16536 * _16528, (_16536 * _16536) - (_16509 * _16509));
语法树结构:
assignment(=:half4)
  declaration(_16552:half4)
  operator(*:unknown)
    variable(_16356:half4)
    function(half4:unknown)
      operator(*:unknown)
        variable(_16509:half)
        variable(_16528:half)
      operator(*:unknown)
        variable(_16528:half)
        variable(_16528:half)
      operator(*:unknown)
        variable(_16536:half)
        variable(_16528:half)
      operator(-:unknown)
        operator(*:unknown)
          variable(_16536:half)
          variable(_16536:half)
        operator(*:unknown)
          variable(_16509:half)
          variable(_16509:half)
--------------------------------------------------
第1106行: _16552.y = _16552.y + _16387;
语法树结构:
assignment(=:unknown)
  member_access(_16552.y:unknown)
  operator(+:unknown)
    member_access(_16552.y:half)
    variable(_16387:half)
--------------------------------------------------
第1107行: float4 _16451 = float4(half4(_16335, _16336, _16335, half(0.858085691928863525390625 * float(_16509))) * _16397.yzxx);
语法树结构:
assignment(=:float4)
  declaration(_16451:float4)
  function(float4:unknown)
    operator(*:unknown)
      function(half4:unknown)
        variable(_16335:half)
        variable(_16336:half)
        variable(_16335:half)
        function(half:unknown)
          operator(*:unknown)
            literal(0.858085691928863525390625:float)
            function(float:unknown)
              variable(_16509:half)
      member_access(_16397.yzxx:half4)
--------------------------------------------------
第1108行: float4 _16473 = float4(_16552);
语法树结构:
assignment(=:float4)
  declaration(_16473:float4)
  function(float4:unknown)
    variable(_16552:half4)
--------------------------------------------------
第1109行: half3 _16258 = ((((max(_16279 + (half3(half(dot(_Block1.cSHCoefficients[1], _16284)), half(dot(_Block1.cSHCoefficients[3], _16284)), half(dot(_Block1.cSHCoefficients[5], _16284))) + half3(half(dot(_Block1.cSHCoefficients[2], _16306)), half(dot(_Block1.cSHCoefficients[4], _16306)), half(dot(_Block1.cSHCoefficients[6], _16306)))), _9179) * half3(half(0.699999988079071044921875 + (0.300000011920928955078125 * _Block1.EnvInfo.z)))) * half3(half(float(_8295 * _18329) * mix(1.0, _Block1.WorldProbeInfo.y, step(0.0, _Block1.cVisibilitySH[0].w))))) * half3(half(_Block1.GIInfo.z))) + half3(float3(max(_16279 + (half3(half(dot(_Block1.cSHCoefficients[1], _16451)), half(dot(_Block1.cSHCoefficients[3], _16451)), half(dot(_Block1.cSHCoefficients[5], _16451))) + half3(half(dot(_Block1.cSHCoefficients[2], _16473)), half(dot(_Block1.cSHCoefficients[4], _16473)), half(dot(_Block1.cSHCoefficients[6], _16473)))), _9179)) * float3(((3.1415927410125732421875 * float(clamp(dot(_9064, _16397), half(0.0), half(1.0)))) * _Block1.WorldProbeInfo.w) * float(half(float(_8295 * _18236)) * half(0.5))))) * half3(half(_Block1.cSHCoefficients[0].w));
语法树结构:
assignment(=:half3)
  declaration(_16258:half3)
  operator(*:unknown)
    operator(+:unknown)
      operator(*:unknown)
        operator(*:unknown)
          operator(*:unknown)
            function(max:unknown)
              operator(+:unknown)
                variable(_16279:half3)
                operator(+:unknown)
                  function(half3:unknown)
                    function(half:unknown)
                      function(dot:unknown)
                        member_access(_Block1.cSHCoefficients[1]:float4)
                        variable(_16284:float4)
                    function(half:unknown)
                      function(dot:unknown)
                        member_access(_Block1.cSHCoefficients[3]:float4)
                        variable(_16284:float4)
                    function(half:unknown)
                      function(dot:unknown)
                        member_access(_Block1.cSHCoefficients[5]:float4)
                        variable(_16284:float4)
                  function(half3:unknown)
                    function(half:unknown)
                      function(dot:unknown)
                        member_access(_Block1.cSHCoefficients[2]:float4)
                        variable(_16306:float4)
                    function(half:unknown)
                      function(dot:unknown)
                        member_access(_Block1.cSHCoefficients[4]:float4)
                        variable(_16306:float4)
                    function(half:unknown)
                      function(dot:unknown)
                        member_access(_Block1.cSHCoefficients[6]:float4)
                        variable(_16306:float4)
              variable(_9179:half3)
            function(half3:unknown)
              function(half:unknown)
                operator(+:unknown)
                  literal(0.699999988079071044921875:float)
                  operator(*:unknown)
                    literal(0.300000011920928955078125:float)
                    member_access(_Block1.EnvInfo.z:float)
          function(half3:unknown)
            function(half:unknown)
              operator(*:unknown)
                function(float:unknown)
                  operator(*:unknown)
                    variable(_8295:half)
                    variable(_18329:half)
                function(mix:unknown)
                  literal(1.0:float)
                  member_access(_Block1.WorldProbeInfo.y:float)
                  function(step:unknown)
                    literal(0.0:float)
                    member_access(_Block1.cVisibilitySH[0].w:float)
        function(half3:unknown)
          function(half:unknown)
            member_access(_Block1.GIInfo.z:float)
      function(half3:unknown)
        operator(*:unknown)
          function(float3:unknown)
            function(max:unknown)
              operator(+:unknown)
                variable(_16279:half3)
                operator(+:unknown)
                  function(half3:unknown)
                    function(half:unknown)
                      function(dot:unknown)
                        member_access(_Block1.cSHCoefficients[1]:float4)
                        variable(_16451:float4)
                    function(half:unknown)
                      function(dot:unknown)
                        member_access(_Block1.cSHCoefficients[3]:float4)
                        variable(_16451:float4)
                    function(half:unknown)
                      function(dot:unknown)
                        member_access(_Block1.cSHCoefficients[5]:float4)
                        variable(_16451:float4)
                  function(half3:unknown)
                    function(half:unknown)
                      function(dot:unknown)
                        member_access(_Block1.cSHCoefficients[2]:float4)
                        variable(_16473:float4)
                    function(half:unknown)
                      function(dot:unknown)
                        member_access(_Block1.cSHCoefficients[4]:float4)
                        variable(_16473:float4)
                    function(half:unknown)
                      function(dot:unknown)
                        member_access(_Block1.cSHCoefficients[6]:float4)
                        variable(_16473:float4)
              variable(_9179:half3)
          function(float3:unknown)
            operator(*:unknown)
              operator(*:unknown)
                operator(*:unknown)
                  literal(3.1415927410125732421875:float)
                  function(float:unknown)
                    function(clamp:unknown)
                      function(dot:unknown)
                        variable(_9064:half3)
                        variable(_16397:half3)
                      function(half:unknown)
                        literal(0.0:float)
                      function(half:unknown)
                        literal(1.0:float)
                member_access(_Block1.WorldProbeInfo.w:float)
              function(float:unknown)
                operator(*:unknown)
                  function(half:unknown)
                    function(float:unknown)
                      operator(*:unknown)
                        variable(_8295:half)
                        variable(_18236:half)
                  function(half:unknown)
                    literal(0.5:float)
    function(half3:unknown)
      function(half:unknown)
        member_access(_Block1.cSHCoefficients[0].w:float)
--------------------------------------------------
第1110行: half3 _8776 = ((_18255 + (half3((float3(_15813 * (half(fast::min(1000.0, (_15839 * _15839) * 0.3183098733425140380859375)) * half(0.25 / fast::max(_15861 * _15861, _12108)))) * _Block1.cVirtualLitColor.xyz) * _15805) + half3((_Block1.cVirtualLitColor.xyz * abs(_8378)) * _15762))) + half3(fast::min(float3(8192.0), ((((float3((_15813 * (half(fast::min(1000.0, (_16159 * _16159) * 0.3183098733425140380859375)) * half(0.25 / fast::max(_15861 * ((float(_16031) * _15858) + _15854), _12108)))) * _16031) * float3(_Block1.cLocalVirtualLitPos.w)) + float3(_10569 * _16031)) * float(half(fast::min(float(half((_15986 * _15986) / ((float(_15974) * abs(_Block1.cLocalVirtualLitCustom.y)) + 9.9999997473787516355514526367188e-05))), _Block1.cLocalVirtualLitCustom.w)))) * (_Block1.cLocalVirtualLitColor.xyz * abs(_Block1.cLocalVirtualLitColor.w))) * _Block1.DiyLightingInfo.z))) * _8696;
语法树结构:
assignment(=:half3)
  declaration(_8776:half3)
  operator(+:unknown)
    operator(+:unknown)
      variable(_18255:half3)
      operator(+:unknown)
        function(half3:unknown)
          operator(*:unknown)
            operator(*:unknown)
              function(float3:unknown)
                operator(*:unknown)
                  variable(_15813:half3)
                  operator(*:unknown)
                    function(half:unknown)
                      function(fast::min:unknown)
                        literal(1000.0:float)
                        operator(*:unknown)
                          operator(*:unknown)
                            variable(_15839:float)
                            variable(_15839:float)
                          literal(0.3183098733425140380859375:float)
                    function(half:unknown)
                      operator(/:unknown)
                        literal(0.25:float)
                        function(fast::max:unknown)
                          operator(*:unknown)
                            variable(_15861:float)
                            variable(_15861:float)
                          variable(_12108:float)
              member_access(_Block1.cVirtualLitColor.xyz:float3)
            variable(_15805:float)
        function(half3:unknown)
          operator(*:unknown)
            operator(*:unknown)
              member_access(_Block1.cVirtualLitColor.xyz:float3)
              function(abs:unknown)
                variable(_8378:float)
            variable(_15762:float3)
    function(half3:unknown)
      function(fast::min:unknown)
        function(float3:unknown)
          literal(8192.0:float)
        operator(*:unknown)
          operator(+:unknown)
            operator(*:unknown)
              function(float3:unknown)
                operator(*:unknown)
                  operator(*:unknown)
                    variable(_15813:half3)
                    operator(*:unknown)
                      function(half:unknown)
                        function(fast::min:unknown)
                          literal(1000.0:float)
                          operator(*:unknown)
                            operator(*:unknown)
                              variable(_16159:float)
                              variable(_16159:float)
                            literal(0.3183098733425140380859375:float)
                      function(half:unknown)
                        operator(/:unknown)
                          literal(0.25:float)
                          function(fast::max:unknown)
                            operator(*:unknown)
                              variable(_15861:float)
                              operator(+:unknown)
                                operator(*:unknown)
                                  function(float:unknown)
                                    variable(_16031:half)
                                  variable(_15858:float)
                                variable(_15854:float)
                            variable(_12108:float)
                  variable(_16031:half)
              function(float3:unknown)
                member_access(_Block1.cLocalVirtualLitPos.w:float)
            function(float3:unknown)
              operator(*:unknown)
                variable(_10569:half3)
                variable(_16031:half)
          function(float:unknown)
            function(half:unknown)
              function(fast::min:unknown)
                function(float:unknown)
                  function(half:unknown)
                    operator(/:unknown)
                      operator(*:unknown)
                        variable(_15986:float)
                        variable(_15986:float)
                      operator(+:unknown)
                        operator(*:unknown)
                          function(float:unknown)
                            variable(_15974:half)
                          function(abs:unknown)
                            member_access(_Block1.cLocalVirtualLitCustom.y:float)
                        literal(9.9999997473787516355514526367188:float)
--------------------------------------------------
第1111行: float _16622 = length(_8370);
语法树结构:
assignment(=:float)
  declaration(_16622:float)
  function(length:unknown)
    variable(_8370:float3)
--------------------------------------------------
第1112行: float _16645 = _8370.y;
语法树结构:
assignment(=:float)
  declaration(_16645:float)
  member_access(_8370.y:float)
--------------------------------------------------
第1113行: float _16657 = (_16645 + _12108) + float((_9251 * half(9.9956989288330078125e-05)) * half(int(sign(_16645))));
语法树结构:
assignment(=:float)
  declaration(_16657:float)
  operator(+:unknown)
    operator(+:unknown)
      variable(_16645:float)
      variable(_12108:float)
    function(float:unknown)
      operator(*:unknown)
        variable(_9251:half)
        function(half:unknown)
          literal(9.9956989288330078125:float)
--------------------------------------------------
第1114行: float2 _16682 = fast::max(float2(0.0), float2(_Block1.FogInfo.w, exp((-_Block1.AerialPerspectiveMie.y) * _Block1.CameraPos.y) * _Block1.AerialPerspectiveMie.z) * ((float2(1.0) - exp(-fast::min(float2(_Block1.FogInfo.z, _Block1.AerialPerspectiveMie.y) * _16657, float2(10.0)))) / float2(_16657)));
语法树结构:
assignment(=:float2)
  declaration(_16682:float2)
  function(fast::max:unknown)
    function(float2:unknown)
      literal(0.0:float)
    operator(*:unknown)
      function(float2:unknown)
        member_access(_Block1.FogInfo.w:float)
        operator(*:unknown)
          function(exp:unknown)
            operator(*:unknown)
              operator(-:unknown)
                member_access(_Block1.AerialPerspectiveMie.y:float)
              member_access(_Block1.CameraPos.y:float)
          member_access(_Block1.AerialPerspectiveMie.z:float)
      operator(/:unknown)
        operator(-:unknown)
          function(float2:unknown)
            literal(1.0:float)
          function(exp:unknown)
            operator(-:unknown)
              function(fast::min:unknown)
                operator(*:unknown)
                  function(float2:unknown)
                    member_access(_Block1.FogInfo.z:float)
                    member_access(_Block1.AerialPerspectiveMie.y:float)
                  variable(_16657:float)
                function(float2:unknown)
                  literal(10.0:float)
        function(float2:unknown)
          variable(_16657:float)
--------------------------------------------------
第1115行: float3 _16688 = fast::max(float3(_12108), _Block1.AerialPerspectiveExt.xyz);
语法树结构:
assignment(=:float3)
  declaration(_16688:float3)
  function(fast::max:unknown)
    function(float3:unknown)
      variable(_12108:float)
    member_access(_Block1.AerialPerspectiveExt.xyz:float3)
--------------------------------------------------
第1116行: float3 _16698 = float3(_8303);
语法树结构:
assignment(=:float3)
  declaration(_16698:float3)
  function(float3:unknown)
    variable(_8303:half3)
--------------------------------------------------
第1117行: float3 _16715 = exp(-(_16688 * ((_16622 * (_Block1.FogColor.w + ((1.0 - _Block1.FogColor.w) * fast::clamp(_16622 / _Block1.FogInfo.x, 0.0, 1.0)))) * ((_16682.x / dot(_16688, _16698)) + ((_16682.y * fast::max(9.9999999747524270787835121154785e-07, _Block1.AerialPerspectiveRay.w * 0.0005000000237487256526947021484375)) * 5.0)))));
语法树结构:
assignment(=:float3)
  declaration(_16715:float3)
  function(exp:unknown)
    operator(-:unknown)
      operator(*:unknown)
        variable(_16688:float3)
        operator(*:unknown)
          operator(*:unknown)
            variable(_16622:float)
            operator(+:unknown)
              member_access(_Block1.FogColor.w:float)
              operator(*:unknown)
                operator(-:unknown)
                  literal(1.0:float)
                  member_access(_Block1.FogColor.w:float)
                function(fast::clamp:unknown)
                  operator(/:unknown)
                    variable(_16622:float)
                    member_access(_Block1.FogInfo.x:float)
                  literal(0.0:float)
                  literal(1.0:float)
          operator(+:unknown)
            operator(/:unknown)
              member_access(_16682.x:float)
              function(dot:unknown)
                variable(_16688:float3)
                variable(_16698:float3)
            operator(*:unknown)
              member_access(_16682.y:float)
              function(fast::max:unknown)
                literal(9.9999999747524270787835121154785:float)
--------------------------------------------------
第1118行: float3 _16602 = fast::normalize(_8370);
语法树结构:
assignment(=:float3)
  declaration(_16602:float3)
  function(fast::normalize:unknown)
    variable(_8370:float3)
--------------------------------------------------
第1119行: float _16756 = fast::clamp(dot(_16602, _Block1.OriginSunDir.xyz), 0.0, 1.0);
语法树结构:
assignment(=:float)
  declaration(_16756:float)
  function(fast::clamp:unknown)
    function(dot:unknown)
      variable(_16602:float3)
      member_access(_Block1.OriginSunDir.xyz:float3)
    literal(0.0:float)
    literal(1.0:float)
--------------------------------------------------
第1120行: float _16759 = fast::max(0.0, _16602.y);
语法树结构:
assignment(=:float)
  declaration(_16759:float)
  function(fast::max:unknown)
    literal(0.0:float)
    member_access(_16602.y:float)
--------------------------------------------------
第1121行: float _16820 = fast::clamp((_16622 - 80.0) / fast::max(_12108, 520.0), 0.0, 1.0);
语法树结构:
assignment(=:float)
  declaration(_16820:float)
  function(fast::clamp:unknown)
    operator(/:unknown)
      operator(-:unknown)
        variable(_16622:float)
        literal(80.0:float)
      function(fast::max:unknown)
        variable(_12108:float)
        literal(520.0:float)
    literal(0.0:float)
    literal(1.0:float)
--------------------------------------------------
第1122行: float _16778 = 1.0 - (_16759 * _16759);
语法树结构:
assignment(=:float)
  declaration(_16778:float)
  operator(-:unknown)
    literal(1.0:float)
    operator(*:unknown)
      variable(_16759:float)
      variable(_16759:float)
--------------------------------------------------
第1123行: float3 _16785 = float3(((1.0 - (_Block1.AerialPerspectiveExt.w * _Block1.AerialPerspectiveExt.w)) / (12.56637096405029296875 * powr(fast::max(1.0 + (_Block1.AerialPerspectiveExt.w * (_Block1.AerialPerspectiveExt.w - (2.0 * _16756))), _12108), 1.5))) * (_16820 * _16820)) * _Block1.SunFogColor.xyz;
语法树结构:
assignment(=:float3)
  declaration(_16785:float3)
  operator(*:unknown)
    function(float3:unknown)
      operator(*:unknown)
        operator(/:unknown)
          operator(-:unknown)
            literal(1.0:float)
            operator(*:unknown)
              member_access(_Block1.AerialPerspectiveExt.w:float)
              member_access(_Block1.AerialPerspectiveExt.w:float)
          operator(*:unknown)
            literal(12.56637096405029296875:float)
            function(powr:unknown)
              function(fast::max:unknown)
                operator(+:unknown)
                  literal(1.0:float)
                  operator(*:unknown)
                    member_access(_Block1.AerialPerspectiveExt.w:float)
                    operator(-:unknown)
                      member_access(_Block1.AerialPerspectiveExt.w:float)
                      operator(*:unknown)
                        literal(2.0:float)
                        variable(_16756:float)
                variable(_12108:float)
              literal(1.5:float)
        operator(*:unknown)
          variable(_16820:float)
          variable(_16820:float)
    member_access(_Block1.SunFogColor.xyz:float3)
--------------------------------------------------
第1124行: half _16793 = half(_16756);
语法树结构:
assignment(=:half)
  declaration(_16793:half)
  function(half:unknown)
    variable(_16756:float)
--------------------------------------------------
第1125行: float3 _16805 = ((_Block1.AerialPerspectiveRay.xyz * float(half(1.0) + (_16793 * _16793))) + (_16785 * _Block1.AerialPerspectiveMie.x)) + ((_Block1.FogColor.xyz * (0.0596831031143665313720703125 * (1.0 + (_16778 * _16778)))) + _16785);
语法树结构:
assignment(=:float3)
  declaration(_16805:float3)
  operator(+:unknown)
    operator(+:unknown)
      operator(*:unknown)
        member_access(_Block1.AerialPerspectiveRay.xyz:float3)
        function(float:unknown)
          operator(+:unknown)
            function(half:unknown)
              literal(1.0:float)
            operator(*:unknown)
              variable(_16793:half)
              variable(_16793:half)
      operator(*:unknown)
        variable(_16785:float3)
        member_access(_Block1.AerialPerspectiveMie.x:float)
    operator(+:unknown)
      operator(*:unknown)
        member_access(_Block1.FogColor.xyz:float3)
        operator(*:unknown)
          literal(0.0596831031143665313720703125:float)
          operator(+:unknown)
            literal(1.0:float)
            operator(*:unknown)
              variable(_16778:float)
              variable(_16778:float)
      variable(_16785:float3)
--------------------------------------------------
第1126行: float3 _16862 = (((((((((((_Block1.cVisibilitySH[0].xyz * _8657) * _15762) * _Block1.AmbientColor.w) * _Block1.ReflectionProbeBBMin.w) * float3(_Block1.cSHCoefficients[0].w)).xyz + float3(_18268)).xyz + (_18989 + float3(((_9179 * _9254) + _8393) * _8696))).xyz + float3((_19028 + half3(float3(_16258) * float3((_15805 * 0.5) + 0.5))) * _10569)).xyz + float3((half3(_9698 * _10588) * _11772) + _8776)).xyz * dot(_16715, _16698)) + (_16805 - (_16805 * _16715))).xyz;
语法树结构:
assignment(=:float3)
  declaration(_16862:float3)
  member_access(+.xyz:unknown)
    operator(+:unknown)
      operator(*:unknown)
        member_access(+.xyz:unknown)
          operator(+:unknown)
            member_access(+.xyz:unknown)
              operator(+:unknown)
                member_access(+.xyz:unknown)
                  operator(+:unknown)
                    member_access(+.xyz:unknown)
                      operator(+:unknown)
                        member_access(*.xyz:unknown)
                          operator(*:unknown)
                            operator(*:unknown)
                              operator(*:unknown)
                                operator(*:unknown)
                                  operator(*:unknown)
                                    member_access(_Block1.cVisibilitySH[0].xyz:float3)
                                    variable(_8657:float)
                                  variable(_15762:float3)
                                member_access(_Block1.AmbientColor.w:float)
                              member_access(_Block1.ReflectionProbeBBMin.w:float)
                            function(float3:unknown)
                              member_access(_Block1.cSHCoefficients[0].w:float)
                        function(float3:unknown)
                          variable(_18268:half3)
                    operator(+:unknown)
                      variable(_18989:float3)
                      function(float3:unknown)
                        operator(*:unknown)
                          operator(+:unknown)
                            operator(*:unknown)
                              variable(_9179:half3)
                              variable(_9254:half3)
                            variable(_8393:half3)
                          variable(_8696:half)
                function(float3:unknown)
                  operator(*:unknown)
                    operator(+:unknown)
                      variable(_19028:half3)
                      function(half3:unknown)
                        operator(*:unknown)
                          function(float3:unknown)
                            variable(_16258:half3)
                          function(float3:unknown)
                            operator(+:unknown)
                              operator(*:unknown)
                                variable(_15805:float)
                                literal(0.5:float)
                              literal(0.5:float)
                    variable(_10569:half3)
            function(float3:unknown)
              operator(+:unknown)
                operator(*:unknown)
                  function(half3:unknown)
                    operator(*:unknown)
                      variable(_9698:float3)
                      variable(_10588:float3)
                  variable(_11772:half)
                variable(_8776:half3)
        function(dot:unknown)
          variable(_16715:float3)
          variable(_16698:float3)
      operator(-:unknown)
        variable(_16805:float3)
        operator(*:unknown)
          variable(_16805:float3)
          variable(_16715:float3)
--------------------------------------------------
第1127行: float3 _8804 = (_16862 * _9868).xyz;
语法树结构:
assignment(=:float3)
  declaration(_8804:float3)
  member_access(*.xyz:unknown)
    operator(*:unknown)
      variable(_16862:float3)
      variable(_9868:float)
--------------------------------------------------
第1128行: float3 _19031;
语法树结构:
declaration(_19031:float3)
--------------------------------------------------
第1129行: if (_Block1.eIsPlayerOverride < 0.5)
语法树结构:
operator(<:unknown)
  member_access(_Block1.eIsPlayerOverride:float)
  literal(0.5:float)
--------------------------------------------------
第1131行: float3 _19032;
语法树结构:
declaration(_19032:float3)
--------------------------------------------------
第1132行: if ((_Block1.ScreenMotionGray.x * _Block1.ScreenMotionGray.x) > _12108)
语法树结构:
operator(*:unknown)
  member_access(_Block1.ScreenMotionGray.x:float)
  member_access(_Block1.ScreenMotionGray.x:float)
--------------------------------------------------
第1134行: float _16911 = fast::clamp((_Block1.CameraPos.w - _Block1.ScreenMotionGray.w) / fast::max(_12108, (_Block1.ScreenMotionGray.w + abs(_Block1.ScreenMotionGray.x)) - _Block1.ScreenMotionGray.w), 0.0, 1.0);
语法树结构:
assignment(=:float)
  declaration(_16911:float)
  function(fast::clamp:unknown)
    operator(/:unknown)
      operator(-:unknown)
        member_access(_Block1.CameraPos.w:float)
        member_access(_Block1.ScreenMotionGray.w:float)
      function(fast::max:unknown)
        variable(_12108:float)
        operator(-:unknown)
          operator(+:unknown)
            member_access(_Block1.ScreenMotionGray.w:float)
            function(abs:unknown)
              member_access(_Block1.ScreenMotionGray.x:float)
          member_access(_Block1.ScreenMotionGray.w:float)
    literal(0.0:float)
    literal(1.0:float)
--------------------------------------------------
第1135行: float _19029;
语法树结构:
declaration(_19029:float)
--------------------------------------------------
第1136行: if (_Block1.ScreenMotionGray.x > 0.001000000047497451305389404296875)
语法树结构:
operator(>:unknown)
  member_access(_Block1.ScreenMotionGray.x:float)
  literal(0.001000000047497451305389404296875:float)
--------------------------------------------------
第1138行: _19029 = 1.0 - _16911;
语法树结构:
assignment(=:float)
  variable(_19029:float)
  operator(-:unknown)
    literal(1.0:float)
    variable(_16911:float)
--------------------------------------------------
第1142行: _19029 = _16911;
语法树结构:
assignment(=:float)
  variable(_19029:float)
  variable(_16911:float)
--------------------------------------------------
第1144行: _19032 = mix(_8804, float3(dot(_8804, _16698) * (0.00999999977648258209228515625 * floor(_Block1.ScreenMotionGray.z))), float3(_19029 * fract(_Block1.ScreenMotionGray.z)));
语法树结构:
assignment(=:float3)
  variable(_19032:float3)
  function(mix:unknown)
    variable(_8804:float3)
    function(float3:unknown)
      operator(*:unknown)
        function(dot:unknown)
          variable(_8804:float3)
          variable(_16698:float3)
        operator(*:unknown)
          literal(0.00999999977648258209228515625:float)
          function(floor:unknown)
            member_access(_Block1.ScreenMotionGray.z:float)
    function(float3:unknown)
      operator(*:unknown)
        variable(_19029:float)
        function(fract:unknown)
          member_access(_Block1.ScreenMotionGray.z:float)
--------------------------------------------------
第1148行: _19032 = _8804;
语法树结构:
assignment(=:float3)
  variable(_19032:float3)
  variable(_8804:float3)
--------------------------------------------------
第1150行: _19031 = _19032;
语法树结构:
assignment(=:float3)
  variable(_19031:float3)
  variable(_19032:float3)
--------------------------------------------------
第1154行: _19031 = _8804;
语法树结构:
assignment(=:float3)
  variable(_19031:float3)
  variable(_8804:float3)
--------------------------------------------------
第1156行: float4 _8808 = float4(_19031.x, _19031.y, _19031.z, float4(0.0).w);
语法树结构:
assignment(=:float4)
  declaration(_8808:float4)
  function(float4:unknown)
    member_access(_19031.x:float)
    member_access(_19031.y:float)
    member_access(_19031.z:float)
    member_access(result_float4.w:unknown)
      function(float4:unknown)
        literal(0.0:float)
--------------------------------------------------
第1157行: _8808.w = _9868;
语法树结构:
assignment(=:unknown)
  member_access(_8808.w:unknown)
  variable(_9868:float)
--------------------------------------------------
第1158行: float3 _8816 = fast::min(_8808.xyz, float3(10000.0));
语法树结构:
assignment(=:float3)
  declaration(_8816:float3)
  function(fast::min:unknown)
    member_access(_8808.xyz:float3)
    function(float3:unknown)
      literal(10000.0:float)
--------------------------------------------------
第1159行: out._Ret = float4(_8816.x, _8816.y, _8816.z, _8808.w);
语法树结构:
assignment(=:unknown)
  member_access(out._Ret:unknown)
  function(float4:unknown)
    member_access(_8816.x:float)
    member_access(_8816.y:float)
    member_access(_8816.z:float)
    member_access(_8808.w:float)
--------------------------------------------------
第1160行: return out;
语法树结构:
declaration(out:custom_struct)
--------------------------------------------------
