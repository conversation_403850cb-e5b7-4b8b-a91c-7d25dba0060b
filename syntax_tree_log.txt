=== 语法树分析日志 ===

第2行: vertex VertexOut vertex_main(
语法树结构:
declaration(VertexOut:unknown)
--------------------------------------------------
第3行: VertexIn in [[stage_in]],
语法树结构:
declaration(in:unknown)
--------------------------------------------------
第4行: constant Uniforms& uniforms [[buffer(0)]]
语法树结构:
variable(constant:unknown)
--------------------------------------------------
第5行: ) {
  语法树为空
--------------------------------------------------
第6行: VertexOut out;
语法树结构:
declaration(out:unknown)
--------------------------------------------------
第9行: half _9176 = 0.5h;
语法树结构:
assignment(=:half)
  declaration(_9176:half)
  literal(0.5:float)
--------------------------------------------------
第10行: half3 position = half3(_9176);  // _9176: half → half3 (scalar_broadcast)
语法树结构:
assignment(=:half3)
  declaration(position:half3)
  function(half3:unknown)
    variable(_9176:half)
--------------------------------------------------
第13行: float4 worldPos = float4(position, 1.0f);  // position: half3 → float4 (precision_promotion)
语法树结构:
assignment(=:float4)
  declaration(worldPos:float4)
  function(float4:unknown)
    variable(position:half3)
    literal(1.0f:float)
--------------------------------------------------
第16行: half4x4 mvpMatrix = uniforms.mvpMatrix;
语法树结构:
assignment(=:half4x4)
  declaration(mvpMatrix:half4x4)
  member_access(uniforms.mvpMatrix:unknown)
--------------------------------------------------
第17行: float4 clipPos = mvpMatrix * worldPos;  // mvpMatrix: half4x4 → float4x4 (precision_promotion)
语法树结构:
assignment(=:float4)
  declaration(clipPos:float4)
  operator(*:unknown)
    variable(mvpMatrix:half4x4)
    variable(worldPos:float4)
--------------------------------------------------
第20行: int index = 42;
语法树结构:
assignment(=:int)
  declaration(index:int)
  literal(42:int)
--------------------------------------------------
第21行: float weight = index * 0.1f;  // index: int → float (precision_promotion)
语法树结构:
assignment(=:float)
  declaration(weight:float)
  operator(*:unknown)
    variable(index:int)
    literal(0.1f:float)
--------------------------------------------------
第23行: out.position = clipPos;
语法树结构:
assignment(=:unknown)
  member_access(out.position:unknown)
  variable(clipPos:float4)
--------------------------------------------------
第24行: out.color = float4(weight, weight, weight, 1.0f);
语法树结构:
assignment(=:unknown)
  member_access(out.color:unknown)
  function(float4:unknown)
    variable(weight:float)
    variable(weight:float)
    variable(weight:float)
    literal(1.0f:float)
--------------------------------------------------
第26行: return out;
语法树结构:
declaration(out:unknown)
--------------------------------------------------
