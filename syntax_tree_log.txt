=== 语法树分析日志 ===

第2行: float4 main() {
语法树结构:
variable(float4:unknown)
--------------------------------------------------
第3行: half a = 1.0h;
语法树结构:
assignment(=:half)
  declaration(a:half)
  literal(1.0:float)
--------------------------------------------------
第4行: float b = 2.0f;
语法树结构:
assignment(=:float)
  declaration(b:float)
  literal(2.0f:float)
--------------------------------------------------
第7行: float result1 = a + b;          // a: half → float
语法树结构:
assignment(=:float)
  declaration(result1:float)
  operator(+:unknown)
    variable(a:half)
    variable(b:float)
--------------------------------------------------
第10行: float result2 = float(a);       // a: half → float (强转)
语法树结构:
assignment(=:float)
  declaration(result2:float)
  function(float:unknown)
    variable(a:half)
--------------------------------------------------
第11行: half result3 = half(b);         // b: float → half (强转)
语法树结构:
assignment(=:half)
  declaration(result3:half)
  function(half:unknown)
    variable(b:float)
--------------------------------------------------
第14行: float result4 = dot(a, b);      // a: half → float
语法树结构:
assignment(=:float)
  declaration(result4:float)
  function(dot:unknown)
    variable(a:half)
    variable(b:float)
--------------------------------------------------
第17行: float result5 = a * _Block1.cEmissionScale;
语法树结构:
assignment(=:float)
  declaration(result5:float)
  operator(*:unknown)
    variable(a:half)
    member_access(_Block1.cEmissionScale:unknown)
--------------------------------------------------
第19行: return float4(result1, result2, result3, result4);
语法树结构:
variable(return:unknown)
--------------------------------------------------
