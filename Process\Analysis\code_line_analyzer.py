#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代码行分析器 - 识别和分析着色器代码中的有效代码行
"""

import re
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass

@dataclass
class CodeLineInfo:
    """简化的代码行信息"""
    line_number: int          # 原始行号
    content: str             # 代码内容

class CodeLineAnalyzer:
    """代码行分析器"""
    
    def __init__(self):
        # 需要跳过的行模式
        self.skip_patterns = [
            r'^\s*$',                    # 空行
            r'^\s*//.*$',                # 注释行
            r'^\s*#.*$',                 # 预处理指令
            r'^\s*\{?\s*$',              # 只有大括号的行
            r'^\s*\}?\s*$',              # 只有大括号的行
            r'^\s*\};\s*$',              # 只有};的行
            r'^\s*(else)\s*$',           # else关键字
            r'^\s*(struct|class|enum)\s+\w+\s*\{?\s*$',  # 结构体声明
            r'^\s*(vertex|fragment|kernel)\s+\w+.*\[\[.*\]\].*\{?\s*$',  # 函数声明
        ]
        
        # 编译正则表达式以提高性能
        self.compiled_skip_patterns = [re.compile(pattern) for pattern in self.skip_patterns]
    
    def is_code_line(self, line: str) -> bool:
        """判断是否是有效的代码行"""
        line = line.strip()
        
        # 空行直接跳过
        if not line:
            return False
        
        # 检查跳过模式
        for pattern in self.compiled_skip_patterns:
            if pattern.match(line):
                return False
        
        return True
    
    def analyze_shader_code(self, shader_content: str) -> List[CodeLineInfo]:
        """分析着色器代码，返回所有有效代码行的基本信息"""
        lines = shader_content.split('\n')
        code_lines = []

        for i, line in enumerate(lines, 1):
            if self.is_code_line(line):
                # 创建简化的代码行信息
                code_line = CodeLineInfo(
                    line_number=i,
                    content=line.strip()
                )
                code_lines.append(code_line)

        return code_lines
    
    # 用于打印查看结果
    def print_analysis_result(self, code_lines: List[CodeLineInfo]) -> str:
        """格式化分析结果为可读文本"""
        print(f"识别到 {len(code_lines)} 行有效代码:")
        print("=" * 60)
        
        for code_line in code_lines:
           print(f"第 {code_line.line_number} 行: {code_line.content}")

    