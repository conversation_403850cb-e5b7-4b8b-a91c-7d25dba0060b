#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语法树构建器 - 构建着色器代码的语法树结构并进行类型分析
"""

import re
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
from .code_line_analyzer import Code<PERSON>ineAnaly<PERSON>
from .type_inference_engine import DataType, type_inference_engine

class NodeType(Enum):
    """节点类型枚举"""
    VARIABLE = "variable"           # 变量
    LITERAL = "literal"            # 字面量
    OPERATOR = "operator"          # 运算符
    FUNCTION = "function"          # 函数调用
    ASSIGNMENT = "assignment"      # 赋值
    DECLARATION = "declaration"    # 声明
    MEMBER_ACCESS = "member_access" # 成员访问
    TYPE_CAST = "type_cast"        # 类型转换



@dataclass
class operationProcess:
    """运算过程数据结构"""
    string: str = ""  # 完整的运算表达式，比如"float a = b + c"
    left_dataType: List[DataType] = None  # 左操作数的数据类型
    right_dataType: List[DataType] = None # 右操作数的数据类型

    def __post_init__(self):
        if self.left_dataType is None:
            self.left_dataType = []
        if self.right_dataType is None:
            self.right_dataType = []

@dataclass
class ASTNode:
    """抽象语法树节点"""
    node_type: NodeType
    value: str                   # 节点值（变量名、运算符、函数名等）
    data_type: DataType          # 数据类型
    children: List['ASTNode']    # 子节点
    line_number: int             # 源代码行号
    position: int = 0            # 在行中的位置

    def add_child(self, child: 'ASTNode'):
        """添加子节点"""
        self.children.append(child)

    def __str__(self):
        return f"{self.node_type.value}({self.value}:{self.data_type.value})"

    def print_tree(self, indent: int = 0) -> str:
        """打印语法树结构"""
        prefix = "  " * indent
        result = f"{prefix}{self.node_type.value}({self.value}:{self.data_type.value})\n"

        for child in self.children:
            result += child.print_tree(indent + 1)

        return result


class LineAnalysisResult:
    """单行代码分析结果"""
    def __init__(self, line_number: int, line_content: str):
        self.line_number = line_number
        self.line_content = line_content
        self.syntax_tree: Optional[ASTNode] = None
        self.variable_types: Dict[str, DataType] = {}
        self.type_conversions: List[Dict] = []
        self.precision_issues: List[Dict] = []
        self.operation_process: List[operationProcess] = []
        self.message: str = ""



class SyntaxTreeBuilder:
    """语法树构建器 - 构建单行代码的语法树并进行分析"""

    def __init__(self):
        # 使用全局类型推断引擎
        self.type_engine = type_inference_engine

        # 初始化时清空日志文件
        self._init_log_files()

        # 数据类型映射
        self.type_mapping = {
            'float': DataType.FLOAT,
            'half': DataType.HALF,
            'int': DataType.INT,
            'uint': DataType.UINT,
            'bool': DataType.BOOL,
            'float2': DataType.FLOAT2,
            'float3': DataType.FLOAT3,
            'float4': DataType.FLOAT4,
            'half2': DataType.HALF2,
            'half3': DataType.HALF3,
            'half4': DataType.HALF4,
            'float3x3': DataType.FLOAT3X3,
            'float4x4': DataType.FLOAT4X4,
        }

        # 运算符优先级
        self.operator_precedence = {
            '=': 1,
            '||': 2,
            '&&': 3,
            '|': 4,
            '^': 5,
            '&': 6,
            '==': 7, '!=': 7,
            '<': 8, '<=': 8, '>': 8, '>=': 8,
            '<<': 9, '>>': 9,  # 位移运算符
            '+': 10, '-': 10,
            '*': 11, '/': 11, '%': 11,
            '!': 12, '~': 12, 'unary+': 12, 'unary-': 12,
            '.': 13, '[]': 13, '()': 13
        }

        # 临时变量计数器
        self.temp_var_counter = 0
        self.temp_var_types: Dict[str, DataType] = {}

    def _init_log_files(self):
        """初始化日志文件，清空之前的内容"""
        # 清空语法树日志文件
        with open("syntax_tree_log.txt", "w", encoding="utf-8") as f:
            f.write("=== 语法树分析日志 ===\n\n")

        # 清空运算过程日志文件
        with open("operation_process_log.txt", "w", encoding="utf-8") as f:
            f.write("=== 运算过程分析日志 ===\n\n")
        

    def analyze_line(self, line_content: str, line_number: int, global_var_types: Dict[str, DataType]) -> LineAnalysisResult:
        """分析单行代码，生成语法树和运算过程"""
        result = LineAnalysisResult(line_number, line_content)

        try:
            # 1. 构建语法树
            result.syntax_tree = self._build_syntax_tree(line_content, line_number, global_var_types)

            # 打印语法树
            self.print_syntax_tree(result.syntax_tree, line_number, line_content)

            # 2. 更新变量类型信息
            self._extract_variable_types(result.syntax_tree, result.variable_types, global_var_types)

            # 3. 生成运算过程并记录类型转换
            result.operation_process, result.type_conversions = self._generate_operation_process(result.syntax_tree, global_var_types)

            # 打印运算过程
            self.print_operation_process(result.operation_process, line_number, line_content)

            # 5. 检查精度问题（暂时为空，后续添加）
            result.precision_issues = []

            result.message = "分析成功"

        except Exception as e:
  
            log_file_path = "error_log.txt"
            with open(log_file_path, "a", encoding="utf-8") as f:
                f.write(f"分析失败: {str(e)} in {line_number} : {line_content}\n\n")
            result.message = f"分析失败: {str(e)}"

        return result


################################构建语法树############################################
    def _build_syntax_tree(self, line_content: str, line_number: int, global_var_types: Dict[str, DataType] = None) -> ASTNode:
        """构建单行代码的语法树"""
        if global_var_types is None:
            global_var_types = {}

        # 清理代码行，移除注释和多余空格
        clean_line = self._clean_line(line_content)

        code_line =self.extract_code_line(clean_line)
        # 解析代码行，构建语法树
        if self._is_assignment_statement(code_line):
            # 赋值语句
            return self._parse_assignment(code_line, line_number, global_var_types)
        elif self._is_type_declaration(code_line):
            # 纯类型声明语句（如 half a;）
            return self._parse_type_declaration(code_line, line_number, global_var_types)
        else:
            # 表达式语句
            return self._parse_expression(code_line, line_number, global_var_types)

    def _clean_line(self, line: str) -> str:
        """清理代码行"""
        # 移除注释
        line = re.sub(r'//.*$', '', line)
        # 移除分号
        line = line.rstrip(';')
        # 移除多余空格
        line = ' '.join(line.split())
        return line.strip()

    def extract_code_line(self, line: str) -> ASTNode:
        # 提取运算部分并调用原始分析
        extracted_part = None

        # for循环：提取初始化部分
        if line.startswith('for'):
            match = re.search(r'for\s*\(\s*([^;]*)', line)
            if match:
                extracted_part = match.group(1).strip()

        # while循环：提取条件部分
        elif line.startswith('while'):
            match = re.search(r'while\s*\(\s*([^)]*)\)', line)
            if match:
                extracted_part = match.group(1).strip()

        # if语句：提取条件部分
        elif line.startswith('if'):
            match = re.search(r'if\s*\(\s*([^)]*)\)', line)
            if match:
                extracted_part = match.group(1).strip()

        # do-while循环：提取条件部分
        elif '} while' in line:
            match = re.search(r'}\s*while\s*\(\s*([^)]*)\)', line)
            if match:
                extracted_part = match.group(1).strip()

        # 如果提取到了运算部分，调用原始分析
        if extracted_part:
            return extracted_part

        return line

    def _is_assignment_statement(self, line: str) -> bool:
        """检查是否是赋值语句"""
        # 查找所有的 '=' 符号
        i = 0
        while i < len(line):
            if line[i] == '=':
                # 检查前后字符，确保不是比较运算符
                prev_char = line[i-1] if i > 0 else ''
                next_char = line[i+1] if i < len(line) - 1 else ''

                # 如果不是 ==, !=, <=, >= 中的一部分，则是赋值
                if prev_char not in ['=', '!', '<', '>'] and next_char != '=':
                    return True
            i += 1
        return False



    def _is_type_declaration(self, line: str) -> bool:
        """检查是否是纯类型声明语句（如 half a;）"""
        # 移除前导空格和分号
        line = line.strip().rstrip(';')

        # 如果包含赋值符号，则不是纯类型声明
        if '=' in line:
            return False

        # 如果是空行，不是类型声明
        if not line:
            return False

        # 移除属性标记 [[...]]
        clean_line = re.sub(r'\[\[.*?\]\]', '', line).strip()

        # 分割为单词
        parts = clean_line.split()

        # 至少需要两个部分：类型名 + 变量名
        if len(parts) >= 2:
            # 从后往前找第一个有效的标识符作为变量名
            var_name = None
            type_parts = []

            for i in range(len(parts) - 1, -1, -1):
                part = parts[i]
                if re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', part) and var_name is None:
                    var_name = part
                    type_parts = parts[:i]
                    break

            # 检查是否找到了变量名，并且有类型部分
            if var_name and type_parts:
                # 检查类型部分都是有效的标识符或关键字
                for part in type_parts:
                    if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_<>]*$', part):
                        return False
                return True

        return False

    def _parse_type_declaration(self, line: str, line_number: int, global_var_types: Dict[str, DataType] = None) -> ASTNode:
        """解析纯类型声明语句（如 half a;）"""
        if global_var_types is None:
            global_var_types = {}

        # 移除分号和前导空格
        line = line.strip().rstrip(';')

        # 移除属性标记 [[...]]
        clean_line = re.sub(r'\[\[.*?\]\]', '', line).strip()

        # 分割为单词
        parts = clean_line.split()

        if len(parts) >= 2:
            # 从后往前找第一个有效的标识符作为变量名
            var_name = None
            type_parts = []

            for i in range(len(parts) - 1, -1, -1):
                part = parts[i]
                if re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', part) and var_name is None:
                    var_name = part
                    type_parts = parts[:i]
                    break

            if var_name and type_parts:
                # 组合类型名
                type_name = ' '.join(type_parts)

                # 尝试从类型映射中获取数据类型
                # 对于复合类型名，尝试提取核心类型
                core_type = type_name
                if 'constant' in type_name:
                    core_type = re.sub(r'\bconstant\s+', '', type_name).strip()
                if 'struct' in type_name:
                    core_type = re.sub(r'\bstruct\s+', '', type_name).strip()
                if 'unsigned' in type_name:
                    core_type = re.sub(r'\bunsigned\s+', '', type_name).strip()

                data_type = self.type_mapping.get(core_type, DataType.UNKNOWN)

                # 将变量类型添加到全局变量类型表中
                # 只有当变量不存在或者现有类型是UNKNOWN时才覆盖
                if global_var_types is not None:
                    existing_type = global_var_types.get(var_name)
                    if existing_type is None or existing_type == DataType.UNKNOWN:
                        global_var_types[var_name] = data_type
                    # 如果已经有正确的类型，使用现有类型而不是覆盖
                    elif existing_type != DataType.UNKNOWN:
                        data_type = existing_type

                return ASTNode(
                    node_type=NodeType.DECLARATION,
                    value=var_name,
                    data_type=data_type,
                    children=[],
                    line_number=line_number
                )

        # 如果不符合格式，返回未知节点
        return ASTNode(
            node_type=NodeType.LITERAL,
            value=line,
            data_type=DataType.UNKNOWN,
            children=[],
            line_number=line_number
        )

    def _parse_assignment(self, line: str, line_number: int, global_var_types: Dict[str, DataType] = None) -> ASTNode:
        """解析赋值语句"""
        if global_var_types is None:
            global_var_types = {}

        # 分割赋值语句
        parts = line.split('=', 1)
        if len(parts) != 2:
            raise ValueError(f"无效的赋值语句: {line}")

        left_part = parts[0].strip()
        right_part = parts[1].strip()

        # 创建赋值节点
        assignment_node = ASTNode(
            node_type=NodeType.ASSIGNMENT,
            value='=',
            data_type=DataType.UNKNOWN,
            children=[],
            line_number=line_number
        )

        # 解析左侧（变量声明或变量）
        left_node = self._parse_left_side(left_part, line_number, global_var_types)
        assignment_node.add_child(left_node)

        # 解析右侧表达式
        if right_part.strip() == '{}':
            # 空的初始化列表
            right_node = ASTNode(
                node_type=NodeType.LITERAL,
                value='{}',
                data_type=DataType.UNKNOWN,
                children=[],
                line_number=line_number
            )
        else:
            right_node = self._parse_expression(right_part, line_number, global_var_types)
        assignment_node.add_child(right_node)

        # 设置赋值节点的数据类型为左侧变量的类型
        assignment_node.data_type = left_node.data_type

        return assignment_node

    def _parse_left_side(self, left_part: str, line_number: int, global_var_types: Dict[str, DataType] = None) -> ASTNode:
        """解析赋值语句的左侧"""
        # 检查是否是变量声明（支持 constant 关键字和结构体类型）
        type_pattern = r'(?:constant\s+)?(?:\b(float|half|int|uint|bool|float2|float3|float4|half2|half3|half4|float3x3|float4x4)\s+([a-zA-Z_][a-zA-Z0-9_]*)|([a-zA-Z_][a-zA-Z0-9_]*)\s+([a-zA-Z_][a-zA-Z0-9_]*))'
        match = re.match(type_pattern, left_part)

        if match:
            # 变量声明
            if match.group(1) and match.group(2):
                # 标准类型声明
                type_name = match.group(1)
                var_name = match.group(2)
                # 首先尝试从内置类型映射中查找
                data_type = self.type_mapping.get(type_name, DataType.UNKNOWN)
                # 如果不是内置类型，检查是否是自定义结构体类型
                if data_type == DataType.UNKNOWN:
                    data_type = self.type_engine._string_to_datatype(type_name)
                    if data_type == DataType.CUSTOM_STRUCT:
                        # 注册自定义结构体变量
                        self.type_engine.register_custom_struct_variable(var_name, type_name)
            elif match.group(3) and match.group(4):
                # 结构体类型声明 
                type_name = match.group(3)
                var_name = match.group(4)
                # 检查是否是自定义结构体类型
                data_type = self.type_engine._string_to_datatype(type_name)
                if data_type == DataType.CUSTOM_STRUCT:
                    # 注册自定义结构体变量
                    self.type_engine.register_custom_struct_variable(var_name, type_name)
            else:
                # 解析失败，当作普通变量处理
                return ASTNode(
                    node_type=NodeType.VARIABLE,
                    value=left_part,
                    data_type=global_var_types.get(left_part, DataType.UNKNOWN),
                    children=[],
                    line_number=line_number
                )

            if global_var_types is not None:
                global_var_types[var_name] = data_type
            return ASTNode(
                node_type=NodeType.DECLARATION,
                value=var_name,
                data_type=data_type,
                children=[],
                line_number=line_number
            )
        else:
            # 普通变量或成员访问
            if '.' in left_part:
                # 成员访问赋值 (如 _17899.z)
                return ASTNode(
                    node_type=NodeType.MEMBER_ACCESS,
                    value=left_part,
                    data_type=DataType.UNKNOWN,
                    children=[],
                    line_number=line_number
                )
            else:
                # 普通变量
                return ASTNode(
                    node_type=NodeType.VARIABLE,
                    value=left_part,
                    data_type=global_var_types.get(left_part, DataType.UNKNOWN) if global_var_types else DataType.UNKNOWN,
                    children=[],
                    line_number=line_number
                )

    def _parse_expression(self, expr: str, line_number: int, global_var_types: Dict[str, DataType] = None) -> ASTNode:
        """解析表达式 - 使用递归下降解析器"""
        if global_var_types is None:
            global_var_types = {}

        # 创建词法分析器
        tokens = self._tokenize(expr)
        if not tokens:
            # 如果无法分词，返回一个未知类型的节点
            return ASTNode(
                node_type=NodeType.LITERAL,
                value=expr,
                data_type=DataType.UNKNOWN,
                children=[],
                line_number=line_number
            )

        # 创建解析器状态
        parser_state = {
            'tokens': tokens,
            'position': 0,
            'line_number': line_number,
            'global_var_types': global_var_types
        }

        # 解析表达式
        return self._parse_ternary_expression(parser_state)

    def _tokenize(self, expr: str) -> List[Dict]:
        """词法分析 - 将表达式分解为token"""
        tokens = []
        i = 0
        expr = expr.strip()

        while i < len(expr):
            # 跳过空白字符
            if expr[i].isspace():
                i += 1
                continue

            # 数字字面量（包括无符号整数）
            if expr[i].isdigit() or (expr[i] == '.' and i + 1 < len(expr) and expr[i + 1].isdigit()):
                start = i
                while i < len(expr) and (expr[i].isdigit() or expr[i] == '.'):
                    i += 1
                # 处理后缀：f (float), u (unsigned), l (long) 等
                if i < len(expr) and expr[i] in 'fulUL':
                    i += 1
                tokens.append({'type': 'NUMBER', 'value': expr[start:i], 'pos': start})
                continue

            # 标识符和关键字（包括成员访问、数组索引和模板函数）
            if expr[i].isalpha() or expr[i] == '_':
                start = i
                # 解析完整的标识符，包括成员访问链、数组索引和命名空间
                while i < len(expr):
                    if expr[i].isalnum() or expr[i] == '_':
                        i += 1
                    elif expr[i] == '.' and i + 1 < len(expr) and (expr[i + 1].isalpha() or expr[i + 1] == '_'):
                        # 成员访问：继续解析
                        i += 1
                    elif expr[i] == '[':
                        # 数组索引：解析完整的索引表达式（支持多维数组）
                        while i < len(expr) and expr[i] == '[':
                            bracket_count = 1
                            i += 1
                            while i < len(expr) and bracket_count > 0:
                                if expr[i] == '[':
                                    bracket_count += 1
                                elif expr[i] == ']':
                                    bracket_count -= 1
                                i += 1
                        # 检查索引后是否还有成员访问或更多数组索引
                        if i < len(expr) and ((expr[i] == '.' and i + 1 < len(expr) and (expr[i + 1].isalpha() or expr[i + 1] == '_')) or expr[i] == '['):
                            continue  # 继续解析后续的成员访问或数组索引
                        else:
                            break
                    elif expr[i:i+2] == '::' and i + 2 < len(expr) and (expr[i + 2].isalpha() or expr[i + 2] == '_'):
                        i += 2  # 跳过 '::'
                    else:
                        break

                # 检查是否是模板函数调用（如 as_type<uint>）
                if i < len(expr) and expr[i] == '<':
                    # 查找匹配的 '>'
                    bracket_count = 0
                    j = i
                    while j < len(expr):
                        if expr[j] == '<':
                            bracket_count += 1
                        elif expr[j] == '>':
                            bracket_count -= 1
                            if bracket_count == 0:
                                # 找到匹配的 '>'，包含整个模板部分
                                i = j + 1
                                break
                        j += 1

                    # 如果找到了完整的模板语法，将其作为一个完整的标识符
                    if bracket_count == 0:
                        tokens.append({'type': 'TEMPLATE_FUNCTION', 'value': expr[start:i], 'pos': start})
                        continue

                # 检查是否包含数组索引或成员访问，决定token类型
                token_value = expr[start:i]
                if '[' in token_value or '.' in token_value:
                    tokens.append({'type': 'COMPLEX_IDENTIFIER', 'value': token_value, 'pos': start})
                else:
                    tokens.append({'type': 'IDENTIFIER', 'value': token_value, 'pos': start})
                continue

            # 运算符 - 按长度排序，先匹配长的
            operators = ['<<', '>>', '==', '!=', '<=', '>=', '&&', '||', '++', '--', '+=', '-=', '*=', '/=',
                        '+', '-', '*', '/', '%', '=', '<', '>', '!', '&', '|', '^', '~', '?', ':']
            matched = False
            for op in operators:
                if expr[i:i+len(op)] == op:
                    tokens.append({'type': 'OPERATOR', 'value': op, 'pos': i})
                    i += len(op)
                    matched = True
                    break

            if matched:
                continue

            # 括号和分隔符
            if expr[i] in '()[]{},.;':
                tokens.append({'type': 'DELIMITER', 'value': expr[i], 'pos': i})
                i += 1
                continue

            # 未知字符，跳过
            i += 1

        return tokens

    def _parse_ternary_expression(self, state: Dict) -> ASTNode:
        """解析三目运算符表达式 (condition ? true_expr : false_expr)"""
        condition = self._parse_logical_or_expression(state)

        token = self._peek_token(state)
        if token and token['type'] == 'OPERATOR' and token['value'] == '?':
            self._consume_token(state)  # 消费 '?'

            true_expr = self._parse_ternary_expression(state)  # 递归解析，支持嵌套

            # 期望 ':'
            token = self._peek_token(state)
            if token and token['type'] == 'OPERATOR' and token['value'] == ':':
                self._consume_token(state)  # 消费 ':'

                false_expr = self._parse_ternary_expression(state)  # 递归解析，支持嵌套

                # 创建三目运算符节点
                ternary_node = ASTNode(
                    node_type=NodeType.OPERATOR,
                    value='?:',
                    data_type=DataType.UNKNOWN,
                    children=[condition, true_expr, false_expr],
                    line_number=state['line_number']
                )
                return ternary_node
            else:
                # 缺少 ':'，语法错误，但仍然返回条件表达式
                return condition

        return condition

    def _parse_logical_or_expression(self, state: Dict) -> ASTNode:
        """解析逻辑或表达式 (||)"""
        left = self._parse_logical_and_expression(state)

        while True:
            token = self._peek_token(state)
            if not token or token['type'] != 'OPERATOR' or token['value'] != '||':
                break

            op_token = self._consume_token(state)
            right = self._parse_logical_and_expression(state)

            op_node = ASTNode(
                node_type=NodeType.OPERATOR,
                value=op_token['value'],
                data_type=DataType.UNKNOWN,
                children=[left, right],
                line_number=state['line_number']
            )
            left = op_node

        return left

    def _parse_logical_and_expression(self, state: Dict) -> ASTNode:
        """解析逻辑与表达式 (&&)"""
        left = self._parse_bitwise_or_expression(state)

        while True:
            token = self._peek_token(state)
            if not token or token['type'] != 'OPERATOR' or token['value'] != '&&':
                break

            op_token = self._consume_token(state)
            right = self._parse_bitwise_or_expression(state)

            op_node = ASTNode(
                node_type=NodeType.OPERATOR,
                value=op_token['value'],
                data_type=DataType.UNKNOWN,
                children=[left, right],
                line_number=state['line_number']
            )
            left = op_node

        return left

    def _peek_token(self, state: Dict) -> Dict:
        """查看当前token但不消费"""
        if state['position'] < len(state['tokens']):
            return state['tokens'][state['position']]
        return None

    def _consume_token(self, state: Dict) -> Dict:
        """消费当前token"""
        if state['position'] < len(state['tokens']):
            token = state['tokens'][state['position']]
            state['position'] += 1
            return token
        return None

    def _parse_bitwise_or_expression(self, state: Dict) -> ASTNode:
        """解析位或表达式 (|)"""
        left = self._parse_bitwise_xor_expression(state)

        while True:
            token = self._peek_token(state)
            if not token or token['type'] != 'OPERATOR' or token['value'] != '|':
                break

            op_token = self._consume_token(state)
            right = self._parse_bitwise_xor_expression(state)

            op_node = ASTNode(
                node_type=NodeType.OPERATOR,
                value=op_token['value'],
                data_type=DataType.UNKNOWN,
                children=[left, right],
                line_number=state['line_number']
            )
            left = op_node

        return left

    def _parse_bitwise_xor_expression(self, state: Dict) -> ASTNode:
        """解析位异或表达式 (^)"""
        left = self._parse_bitwise_and_expression(state)

        while True:
            token = self._peek_token(state)
            if not token or token['type'] != 'OPERATOR' or token['value'] != '^':
                break

            op_token = self._consume_token(state)
            right = self._parse_bitwise_and_expression(state)

            op_node = ASTNode(
                node_type=NodeType.OPERATOR,
                value=op_token['value'],
                data_type=DataType.UNKNOWN,
                children=[left, right],
                line_number=state['line_number']
            )
            left = op_node

        return left

    def _parse_bitwise_and_expression(self, state: Dict) -> ASTNode:
        """解析位与表达式 (&)"""
        left = self._parse_equality_expression(state)

        while True:
            token = self._peek_token(state)
            if not token or token['type'] != 'OPERATOR' or token['value'] != '&':
                break

            op_token = self._consume_token(state)
            right = self._parse_equality_expression(state)

            op_node = ASTNode(
                node_type=NodeType.OPERATOR,
                value=op_token['value'],
                data_type=DataType.UNKNOWN,
                children=[left, right],
                line_number=state['line_number']
            )
            left = op_node

        return left

    def _parse_equality_expression(self, state: Dict) -> ASTNode:
        """解析相等性表达式 (== !=)"""
        left = self._parse_relational_expression(state)

        while True:
            token = self._peek_token(state)
            if not token or token['type'] != 'OPERATOR' or token['value'] not in ['==', '!=']:
                break

            op_token = self._consume_token(state)
            right = self._parse_relational_expression(state)

            op_node = ASTNode(
                node_type=NodeType.OPERATOR,
                value=op_token['value'],
                data_type=DataType.UNKNOWN,
                children=[left, right],
                line_number=state['line_number']
            )
            left = op_node

        return left

    def _parse_relational_expression(self, state: Dict) -> ASTNode:
        """解析关系表达式 (< > <= >=)"""
        left = self._parse_additive_expression(state)

        while True:
            token = self._peek_token(state)
            if not token or token['type'] != 'OPERATOR' or token['value'] not in ['<', '>', '<=', '>=']:
                break

            op_token = self._consume_token(state)
            right = self._parse_additive_expression(state)

            op_node = ASTNode(
                node_type=NodeType.OPERATOR,
                value=op_token['value'],
                data_type=DataType.UNKNOWN,
                children=[left, right],
                line_number=state['line_number']
            )
            left = op_node

        return left

    def _parse_additive_expression(self, state: Dict) -> ASTNode:
        """解析加法表达式 (+ -)"""
        left = self._parse_shift_expression(state)

        while True:
            token = self._peek_token(state)
            if not token or token['type'] != 'OPERATOR' or token['value'] not in ['+', '-']:
                break

            op_token = self._consume_token(state)
            right = self._parse_shift_expression(state)

            # 创建运算符节点
            op_node = ASTNode(
                node_type=NodeType.OPERATOR,
                value=op_token['value'],
                data_type=DataType.UNKNOWN,
                children=[left, right],
                line_number=state['line_number']
            )
            left = op_node

        return left

    def _parse_shift_expression(self, state: Dict) -> ASTNode:
        """解析位移表达式 (<< >>)"""
        left = self._parse_multiplicative_expression(state)

        while True:
            token = self._peek_token(state)
            if not token or token['type'] != 'OPERATOR' or token['value'] not in ['<<', '>>']:
                break

            op_token = self._consume_token(state)
            right = self._parse_multiplicative_expression(state)

            op_node = ASTNode(
                node_type=NodeType.OPERATOR,
                value=op_token['value'],
                data_type=DataType.UNKNOWN,
                children=[left, right],
                line_number=state['line_number']
            )
            left = op_node

        return left

    def _parse_multiplicative_expression(self, state: Dict) -> ASTNode:
        """解析乘法表达式 (* / %)"""
        left = self._parse_unary_expression(state)

        while True:
            token = self._peek_token(state)
            if not token or token['type'] != 'OPERATOR' or token['value'] not in ['*', '/', '%']:
                break

            op_token = self._consume_token(state)
            right = self._parse_unary_expression(state)

            # 创建运算符节点
            op_node = ASTNode(
                node_type=NodeType.OPERATOR,
                value=op_token['value'],
                data_type=DataType.UNKNOWN,
                children=[left, right],
                line_number=state['line_number']
            )
            left = op_node

        return left

    def _parse_unary_expression(self, state: Dict) -> ASTNode:
        """解析一元表达式 (- + ! ~)"""
        token = self._peek_token(state)

        if token and token['type'] == 'OPERATOR' and token['value'] in ['-', '+', '!', '~']:
            op_token = self._consume_token(state)
            operand = self._parse_unary_expression(state)

            # 创建一元运算符节点
            op_node = ASTNode(
                node_type=NodeType.OPERATOR,
                value=op_token['value'],
                data_type=DataType.UNKNOWN,
                children=[operand],
                line_number=state['line_number']
            )
            return op_node

        return self._parse_postfix_expression(state)

    def _parse_postfix_expression(self, state: Dict) -> ASTNode:
        """解析后缀表达式 (函数调用, 成员访问)"""
        left = self._parse_primary_expression(state)

        while True:
            token = self._peek_token(state)
            if not token:
                break

            if token['type'] == 'DELIMITER' and token['value'] == '(':
                # 函数调用
                self._consume_token(state)  # 消费 '('

                # 解析参数
                args = []
                while True:
                    token = self._peek_token(state)
                    if token and token['type'] == 'DELIMITER' and token['value'] == ')':
                        break

                    arg = self._parse_ternary_expression(state)
                    if arg:
                        args.append(arg)

                    # 检查是否有逗号
                    token = self._peek_token(state)
                    if token and token['type'] == 'DELIMITER' and token['value'] == ',':
                        self._consume_token(state)  # 消费 ','
                    else:
                        break

                # 消费 ')'
                token = self._peek_token(state)
                if token and token['type'] == 'DELIMITER' and token['value'] == ')':
                    self._consume_token(state)

                # 创建函数调用节点
                func_node = ASTNode(
                    node_type=NodeType.FUNCTION,
                    value=left.value if left else 'unknown',
                    data_type=DataType.UNKNOWN,
                    children=args,
                    line_number=state['line_number']
                )
                left = func_node
            elif token['type'] == 'DELIMITER' and token['value'] == '[':
                # 只有当左侧不是复杂标识符时才处理数组索引
                # 如果左侧已经是复杂标识符，说明数组索引已经在词法分析阶段处理了
                if left and left.node_type != NodeType.MEMBER_ACCESS:
                    self._consume_token(state)  # 消费 '['

                    # 解析索引表达式
                    index_expr = self._parse_ternary_expression(state)

                    # 消费 ']'
                    token = self._peek_token(state)
                    if token and token['type'] == 'DELIMITER' and token['value'] == ']':
                        self._consume_token(state)

                    # 创建数组索引节点
                    array_node = ASTNode(
                        node_type=NodeType.MEMBER_ACCESS,  # 使用MEMBER_ACCESS表示数组索引
                        value=f"{left.value if left else 'unknown'}[{index_expr.value if index_expr else 'unknown'}]",
                        data_type=DataType.UNKNOWN,
                        children=[left, index_expr] if index_expr else [left],
                        line_number=state['line_number']
                    )
                    left = array_node
                else:
                    break
            elif token['type'] == 'DELIMITER' and token['value'] == '.':
                # 只有当左侧不是复杂标识符时才处理成员访问
                # 如果左侧已经是复杂标识符，说明成员访问已经在词法分析阶段处理了
                if left and left.node_type != NodeType.MEMBER_ACCESS:
                    self._consume_token(state)  # 消费 '.'

                    # 获取成员名
                    member_token = self._peek_token(state)
                    if member_token and member_token['type'] == 'IDENTIFIER':
                        member_token = self._consume_token(state)

                        # 创建成员访问节点
                        # 如果左侧是函数调用，使用更合适的命名
                        if left and left.node_type == NodeType.FUNCTION:
                            base_name = f"result_{left.value}"
                        else:
                            base_name = left.value if left else 'unknown'

                        member_node = ASTNode(
                            node_type=NodeType.MEMBER_ACCESS,
                            value=f"{base_name}.{member_token['value']}",
                            data_type=DataType.UNKNOWN,
                            children=[left],
                            line_number=state['line_number']
                        )
                        left = member_node
                    else:
                        break
                else:
                    break
            else:
                break

        return left

    def _parse_primary_expression(self, state: Dict) -> ASTNode:
        """解析基本表达式 (数字, 标识符, 括号表达式)"""
        token = self._peek_token(state)
        if not token:
            # 如果没有token，返回一个未知类型的节点
            return ASTNode(
                node_type=NodeType.LITERAL,
                value="",
                data_type=DataType.UNKNOWN,
                children=[],
                line_number=state['line_number']
            )

        # 括号表达式
        if token['type'] == 'DELIMITER' and token['value'] == '(':
            self._consume_token(state)  # 消费 '('
            expr = self._parse_ternary_expression(state)

            # 消费 ')'
            token = self._peek_token(state)
            if token and token['type'] == 'DELIMITER' and token['value'] == ')':
                self._consume_token(state)

            return expr

        # 数字字面量
        if token['type'] == 'NUMBER':
            token = self._consume_token(state)
            return ASTNode(
                node_type=NodeType.LITERAL,
                value=token['value'],
                data_type=DataType.FLOAT if 'f' in token['value'] or '.' in token['value'] else DataType.INT,
                children=[],
                line_number=state['line_number']
            )

        # 模板函数 (如 as_type<uint>)
        if token['type'] == 'TEMPLATE_FUNCTION':
            token = self._consume_token(state)
            return ASTNode(
                node_type=NodeType.FUNCTION,
                value=token['value'],
                data_type=DataType.UNKNOWN,
                children=[],
                line_number=state['line_number']
            )

        # 复杂标识符 (包含成员访问或数组索引的完整表达式)
        if token['type'] == 'COMPLEX_IDENTIFIER':
            token = self._consume_token(state)

            # 复杂标识符总是作为成员访问处理
            data_type = self.type_engine.infer_member_access_type(token['value'], state['global_var_types'])

            return ASTNode(
                node_type=NodeType.MEMBER_ACCESS,
                value=token['value'],
                data_type=data_type,
                children=[],
                line_number=state['line_number']
            )

        # 标识符 (普通变量)
        if token['type'] == 'IDENTIFIER':
            token = self._consume_token(state)

            # 推断类型
            if '.' in token['value']:
                # 成员访问 - 推断结果类型
                data_type = self.type_engine.infer_member_access_type(token['value'], state['global_var_types'])
                node_type = NodeType.MEMBER_ACCESS
            else:
                # 普通变量
                data_type = state['global_var_types'].get(token['value'], DataType.UNKNOWN)
                node_type = NodeType.VARIABLE

            return ASTNode(
                node_type=node_type,
                value=token['value'],
                data_type=data_type,
                children=[],
                line_number=state['line_number']
            )

        # 无法识别的token
        return None



    def _split_arguments(self, args_str: str) -> List[str]:
        """分割函数参数"""
        args = []
        current_arg = ""
        paren_level = 0

        for char in args_str:
            if char == ',' and paren_level == 0:
                args.append(current_arg.strip())
                current_arg = ""
            else:
                if char == '(':
                    paren_level += 1
                elif char == ')':
                    paren_level -= 1
                current_arg += char

        if current_arg.strip():
            args.append(current_arg.strip())

        return args

    def print_syntax_tree(self, syntax_tree: ASTNode, line_number: int, line_content: str):
        """将语法树结构输出到日志文件"""
        log_file_path = "syntax_tree_log.txt"

        with open(log_file_path, "a", encoding="utf-8") as f:
            if syntax_tree is None:
                f.write(f"第{line_number}行: {line_content}\n")
                f.write("  语法树为空\n")
                f.write("-" * 50 + "\n")
                return

            f.write(f"第{line_number}行: {line_content}\n")
            f.write("语法树结构:\n")
            f.write(syntax_tree.print_tree())
            f.write("-" * 50 + "\n")

    def print_operation_process(self, operation_process: List[operationProcess], line_number: int, line_content: str):
        """将运算过程输出到日志文件"""
        log_file_path = "operation_process_log.txt"

        with open(log_file_path, "a", encoding="utf-8") as f:
            f.write(f"第{line_number}行运算过程: {line_content}\n")

            if not operation_process:
                f.write("  无运算过程\n")
                f.write("=" * 50 + "\n")
                return

            f.write("运算过程详情:\n")
            for i, op in enumerate(operation_process, 1):
                f.write(f"  步骤{i}: {op.string}\n")

                # 写入左侧数据类型（结果类型）
                if op.left_dataType:
                    left_types = [dt.value for dt in op.left_dataType]
                    f.write(f"    结果类型: {', '.join(left_types)}\n")

                # 写入右侧数据类型（操作数类型）
                if op.right_dataType:
                    right_types = [dt.value for dt in op.right_dataType]
                    f.write(f"    操作数类型: {', '.join(right_types)}\n")

                f.write("\n")  # 空行分隔

            f.write("=" * 50 + "\n")







###########################提取变量信息############################################
    def _extract_variable_types(self, node: ASTNode, variable_types: Dict[str, DataType], global_var_types: Dict[str, DataType]):
        """从语法树中提取变量类型信息"""
        # 也可以不加到global_var_types，因为我在生成的时候就添加了
  
        if node.node_type == NodeType.VARIABLE or node.node_type == NodeType.DECLARATION or node.node_type == NodeType.MEMBER_ACCESS:
            variable_types[node.value] =self._get_node_type(node.value, node, global_var_types)
        
        # 递归处理子节点
        for child in node.children:
            self._extract_variable_types(child, variable_types, global_var_types)










###########################生成运算过程############################################
    def _generate_operation_process(self, node: ASTNode, global_var_types: Dict[str, DataType]) -> Tuple[List[operationProcess], List[Dict]]:
        """生成运算过程并记录类型转换"""
        operations = []
        type_conversions = []
        self._reset_temp_var_info()

        if node is None:
            # 如果节点为空，返回空的操作列表
            return operations, type_conversions

        if node.node_type == NodeType.ASSIGNMENT:
            # 处理赋值语句
            left_node = node.children[0]
            right_node = node.children[1]

            # 生成右侧表达式的运算过程
            right_operations, right_conversions, right_result_var = self._generate_expression_operations(right_node, global_var_types)
            operations.extend(right_operations)
            type_conversions.extend(right_conversions)

            # 生成最终赋值操作
            final_op = operationProcess()
            final_op.string = f"{left_node.value} = {right_result_var}"
            final_op.left_dataType = [left_node.data_type] if left_node.data_type != DataType.UNKNOWN else []


            right_type = self._get_node_type(right_result_var,right_node, global_var_types)
            final_op.right_dataType = [right_type]

            operations.append(final_op)

            # 检查赋值中的类型转换
            if left_node.data_type != DataType.UNKNOWN and right_type != DataType.UNKNOWN:
                if self._needs_type_conversion(right_type, left_node.data_type):
                    type_conversions.append({
                        'variable': right_result_var,
                        'operation': final_op.string,
                        'from_type': right_type.value,
                        'to_type': left_node.data_type.value,
                        'conversion_type': 'implicit_promotion',  # 赋值中的转换通常是隐式的
                        'reason': '赋值类型转换'
                    })

        elif node.node_type == NodeType.OPERATOR:
            # 处理运算符表达式
            expr_operations, expr_conversions = self._generate_expression_operations(node, global_var_types)
            operations.extend(expr_operations)
            type_conversions.extend(expr_conversions)
        else:
            # 其他类型的节点，暂时不处理
            pass

        return operations, type_conversions

    def _reset_temp_var_info(self):
        self.temp_var_counter = 0
        self.temp_var_types = {}

    
    def _generate_expression_operations(self, node: ASTNode, global_var_types: Dict[str, DataType]) -> Tuple[List[operationProcess], List[Dict], str]:
        """生成表达式的运算过程，返回操作列表、类型转换列表和结果变量名"""
        operations = []
        type_conversions = []

        if node.node_type == NodeType.OPERATOR:
            # 处理运算符
            if len(node.children) == 2:
                # 二元运算符
                left_child = node.children[0]
                right_child = node.children[1]

                # 生成左操作数的运算过程
                left_ops, left_conversions, left_var = self._generate_expression_operations(left_child, global_var_types)
                operations.extend(left_ops)
                type_conversions.extend(left_conversions)

                # 生成右操作数的运算过程
                right_ops, right_conversions, right_var = self._generate_expression_operations(right_child, global_var_types)
                operations.extend(right_ops)
                type_conversions.extend(right_conversions)

                # 生成当前运算
                temp_var = f"tmp_{self.temp_var_counter}"
                self.temp_var_counter += 1

                # 获取操作数类型
                left_type = self._get_node_type(left_var, left_child, global_var_types)
                right_type = self._get_node_type(right_var, right_child, global_var_types)

                # 推断运算结果类型
                result_type = self._infer_operation_result_type(left_type, right_type, node.value)
                self.temp_var_types[temp_var] = result_type

                # 检查运算中的类型转换
                op_string = f"{temp_var} = {left_var} {node.value} {right_var}"

                # 检查左操作数是否需要精度提升
                if self._is_precision_promotion(left_type, result_type):
                    type_conversions.append({
                        'variable': left_var,
                        'operation': op_string,
                        'from_type': left_type.value,
                        'to_type': result_type.value,
                        'conversion_type': 'implicit_promotion',
                        'reason': f'{node.value} 运算中的隐式提升'
                    })

                # 检查右操作数是否需要精度提升
                if self._is_precision_promotion(right_type, result_type):
                    type_conversions.append({
                        'variable': right_var,
                        'operation': op_string,
                        'from_type': right_type.value,
                        'to_type': result_type.value,
                        'conversion_type': 'implicit_promotion',
                        'reason': f'{node.value} 运算中的隐式提升'
                    })

                op = operationProcess()
                op.string = op_string
                op.left_dataType = [result_type]  # 左侧是运算结果类型
                op.right_dataType = [left_type, right_type]  # 右侧是两个操作数的类型
                operations.append(op)

                return operations, type_conversions, temp_var
            elif len(node.children) == 1:
                # 一元运算符
                child = node.children[0]
                child_ops, child_conversions, child_var = self._generate_expression_operations(child, global_var_types)
                operations.extend(child_ops)
                type_conversions.extend(child_conversions)

                temp_var = f"tmp_{self.temp_var_counter}"
                self.temp_var_counter += 1
                # 获取操作数类型
                child_type = self._get_node_type(child_var, child, global_var_types)

                # 一元运算符的结果类型推断
                result_type = self._infer_unary_operation_result_type(child_type, node.value)
                self.temp_var_types[temp_var] = result_type

                op = operationProcess()
                op.string = f"{temp_var} = {node.value}{child_var}"
                op.left_dataType = [result_type]  # 左侧是一元运算的结果类型
                op.right_dataType = [child_type]
                operations.append(op)

                return operations, type_conversions, temp_var
            elif len(node.children) == 3 and node.value == '?:':
                # 三目运算符 (condition ? true_expr : false_expr)
                condition_child = node.children[0]
                true_child = node.children[1]
                false_child = node.children[2]

                # 生成条件表达式的运算过程
                condition_ops, condition_conversions, condition_var = self._generate_expression_operations(condition_child, global_var_types)
                operations.extend(condition_ops)
                type_conversions.extend(condition_conversions)

                # 生成true分支的运算过程
                true_ops, true_conversions, true_var = self._generate_expression_operations(true_child, global_var_types)
                operations.extend(true_ops)
                type_conversions.extend(true_conversions)

                # 生成false分支的运算过程
                false_ops, false_conversions, false_var = self._generate_expression_operations(false_child, global_var_types)
                operations.extend(false_ops)
                type_conversions.extend(false_conversions)

                # 生成三目运算
                temp_var = f"tmp_{self.temp_var_counter}"
                self.temp_var_counter += 1

                # 获取操作数类型
                condition_type = self._get_node_type(condition_var, condition_child, global_var_types)
                true_type = self._get_node_type(true_var, true_child, global_var_types)
                false_type = self._get_node_type(false_var, false_child, global_var_types)

                # 三目运算符的结果类型通常是true和false分支的公共类型
                result_type = self._infer_ternary_result_type(true_type, false_type)
                self.temp_var_types[temp_var] = result_type

                op = operationProcess()
                op.string = f"{temp_var} = {condition_var} ? {true_var} : {false_var}"
                op.left_dataType = [result_type]  # 左侧是三目运算的结果类型
                op.right_dataType = [condition_type, true_type, false_type]  # 右侧是三个操作数的类型
                operations.append(op)

                return operations, type_conversions, temp_var

        elif node.node_type == NodeType.FUNCTION:
            # 处理函数调用
            arg_vars = []
            arg_types = []
            for child in node.children:
                child_ops, child_conversions, child_var = self._generate_expression_operations(child, global_var_types)
                operations.extend(child_ops)
                type_conversions.extend(child_conversions)
                arg_vars.append(child_var)

                child_type = self._get_node_type(child_var, child, global_var_types)
                arg_types.append(child_type)

            temp_var = f"tmp_{self.temp_var_counter}"
            self.temp_var_counter += 1

            # 推断函数返回类型
            result_type = self._infer_function_result_type(node.value, arg_types)
            self.temp_var_types[temp_var] = result_type

            # 检查函数调用中的类型转换（强制转换）
            op_string = f"{temp_var} = {node.value}({', '.join(arg_vars)})"

            # 检查是否是类型构造函数（强制转换）
            if self._is_type_constructor(node.value):
                for arg_var, arg_type in zip(arg_vars, arg_types):
                    if self._needs_type_conversion(arg_type, result_type):
                        type_conversions.append({
                            'variable': arg_var,
                            'operation': op_string,
                            'from_type': arg_type.value,
                            'to_type': result_type.value,
                            'conversion_type': 'explicit_cast',
                            'reason': f'强制转换为 {node.value}'
                        })
            else:
                # 内置函数中的隐式提升
                builtin_funcs = ['dot', 'cross', 'max', 'min', 'clamp', 'mix', 'normalize', 'length', 'distance']
                if node.value in builtin_funcs:
                    for arg_var, arg_type in zip(arg_vars, arg_types):
                        if self._is_precision_promotion(arg_type, result_type):
                            type_conversions.append({
                                'variable': arg_var,
                                'operation': op_string,
                                'from_type': arg_type.value,
                                'to_type': result_type.value,
                                'conversion_type': 'implicit_promotion',
                                'reason': f'{node.value} 函数中的隐式提升'
                            })

            op = operationProcess()
            op.string = op_string
            op.left_dataType = [result_type]
            op.right_dataType = arg_types
            operations.append(op)

            return operations, type_conversions, temp_var

        elif node.node_type == NodeType.MEMBER_ACCESS:
            # 处理成员访问（包括复杂标识符如 a[0].b）
            if node.children:
                # 有子节点，先处理子节点（如函数调用或数组索引）
                child_ops, child_conversions, child_var = self._generate_expression_operations(node.children[0], global_var_types)
                operations.extend(child_ops)
                type_conversions.extend(child_conversions)

                # 生成成员访问操作
                temp_var = f"tmp_{self.temp_var_counter}"
                self.temp_var_counter += 1

                # 提取成员名（从 node.value 中提取 .member 部分）
                if '.' in node.value:
                    member_part = node.value.split('.')[-1]  # 获取最后的成员名
                    access_expr = f"{child_var}.{member_part}"
                else:
                    access_expr = node.value

                # 使用类型推断引擎推断成员访问类型
                # 合并全局变量类型和临时变量类型
                combined_var_types = dict(global_var_types) if global_var_types else {}
                combined_var_types.update(self.temp_var_types)
                result_type = self.type_engine.infer_member_access_type(access_expr, combined_var_types)
                self.temp_var_types[temp_var] = result_type

                op = operationProcess()
                op.string = f"{temp_var} = {access_expr}"
                op.left_dataType = [result_type]
                op.right_dataType = [self._get_node_type(child_var, node.children[0], global_var_types)]
                operations.append(op)

                return operations, type_conversions, temp_var
            else:
                # 没有子节点，直接成员访问（如 _Block1.member 或复杂标识符 a[0].b）
                # 对于复杂标识符，不生成中间步骤，直接作为一个完整的操作
                return operations, type_conversions, node.value

        # 基本元素（变量、字面量等）
        # 对于字面量和变量，不需要生成额外的运算操作
        return operations, type_conversions, node.value

    def _get_node_type(self, value: str, node: ASTNode, global_var_types: Dict[str, DataType]) -> DataType:
        """获取节点的数据类型 - 使用type_engine进行推断"""
        # 优先通过名字直接返回,如果名字不行就再用节点
        if value in global_var_types:
            return global_var_types[value]
        elif value in self.temp_var_types:
            return self.temp_var_types[value]
        
        if node.node_type == NodeType.MEMBER_ACCESS:
            # 使用type_engine推断成员访问类型
            return self.type_engine.infer_member_access_type(node.value, global_var_types)
        
        if node.data_type != DataType.UNKNOWN:
            return node.data_type
        return DataType.UNKNOWN


    def _infer_operation_result_type(self, left_type: DataType, right_type: DataType, operator: str) -> DataType:
        """推断运算结果的类型"""
        # 如果有未知类型，返回另一个类型
        if left_type == DataType.UNKNOWN:
            return right_type
        if right_type == DataType.UNKNOWN:
            return left_type

        # 算术运算的类型提升规则
        if operator in ['+', '-', '*', '/', '%']:
            return self._infer_arithmetic_result_type(left_type, right_type)

        # 比较运算返回bool
        if operator in ['==', '!=', '<', '<=', '>', '>=']:
            return DataType.BOOL

        # 逻辑运算返回bool
        if operator in ['&&', '||']:
            return DataType.BOOL

        # 位运算符保持操作数类型（通常返回较高精度的类型）
        # 左移右移默认返回左操作数类型
        if operator in ['&', '|', '^']:
            # 整数类型的位运算
            if left_type in [DataType.INT, DataType.UINT] or right_type in [DataType.INT, DataType.UINT]:
                # 如果有 uint，返回 uint；否则返回 int
                if left_type == DataType.UINT or right_type == DataType.UINT:
                    return DataType.UINT
                return DataType.INT
        # 默认情况：返回左操作数类型
        return left_type

    def _infer_ternary_result_type(self, true_type: DataType, false_type: DataType) -> DataType:
        """推断三目运算符的结果类型"""
        # 如果有未知类型，返回另一个类型
        if true_type == DataType.UNKNOWN:
            return false_type
        if false_type == DataType.UNKNOWN:
            return true_type

        # 如果两个类型相同，直接返回
        if true_type == false_type:
            return true_type

        # 否则使用算术类型提升规则来确定公共类型
        return self._infer_arithmetic_result_type(true_type, false_type)

    def _infer_arithmetic_result_type(self, left_type: DataType, right_type: DataType) -> DataType:
        """推断算术运算的结果类型，正确处理向量-标量运算"""

        # 定义类型分类
        scalar_types = {DataType.FLOAT, DataType.HALF, DataType.INT, DataType.UINT, DataType.BOOL}
        vector_types = {
            DataType.FLOAT2, DataType.FLOAT3, DataType.FLOAT4,
            DataType.HALF2, DataType.HALF3, DataType.HALF4
        }

        # 获取类型信息
        left_is_vector = left_type in vector_types
        right_is_vector = right_type in vector_types
        left_is_scalar = left_type in scalar_types
        right_is_scalar = right_type in scalar_types

        # 向量 op 向量：返回向量类型（优先选择精度更高的）
        if left_is_vector and right_is_vector:
            return self._get_higher_precision_vector_type(left_type, right_type)

        # 向量 op 标量：返回向量类型，但可能需要精度提升
        elif left_is_vector and right_is_scalar:
            return self._promote_vector_with_scalar(left_type, right_type)

        # 标量 op 向量：返回向量类型，但可能需要精度提升
        elif left_is_scalar and right_is_vector:
            return self._promote_vector_with_scalar(right_type, left_type)

        # 标量 op 标量：返回标量类型（精度提升）
        elif left_is_scalar and right_is_scalar:
            return self._get_higher_precision_scalar_type(left_type, right_type)

        # 默认情况：返回左操作数类型
        return left_type

    def _get_higher_precision_vector_type(self, type1: DataType, type2: DataType) -> DataType:
        """获取两个向量类型中精度更高的类型"""
        # 精度优先级：float > half，维度相同时选择精度高的
        precision_map = {
            DataType.HALF2: (DataType.HALF, 2), DataType.FLOAT2: (DataType.FLOAT, 2),
            DataType.HALF3: (DataType.HALF, 3), DataType.FLOAT3: (DataType.FLOAT, 3),
            DataType.HALF4: (DataType.HALF, 4), DataType.FLOAT4: (DataType.FLOAT, 4),
        }

        if type1 not in precision_map or type2 not in precision_map:
            return type1

        base1, dim1 = precision_map[type1]
        base2, dim2 = precision_map[type2]

        # 如果维度不同，返回维度更高的
        if dim1 != dim2:
            return type1 if dim1 > dim2 else type2

        # 维度相同，返回精度更高的（float > half）
        if base1 == DataType.FLOAT or base2 == DataType.FLOAT:
            return DataType.FLOAT2 if dim1 == 2 else (DataType.FLOAT3 if dim1 == 3 else DataType.FLOAT4)
        else:
            return type1  # 都是 half，返回第一个

    def _promote_vector_with_scalar(self, vector_type: DataType, scalar_type: DataType) -> DataType:
        """向量与标量运算时的类型提升"""
        # 获取向量的基础类型和维度
        vector_info = {
            DataType.HALF2: (DataType.HALF, 2), DataType.FLOAT2: (DataType.FLOAT, 2),
            DataType.HALF3: (DataType.HALF, 3), DataType.FLOAT3: (DataType.FLOAT, 3),
            DataType.HALF4: (DataType.HALF, 4), DataType.FLOAT4: (DataType.FLOAT, 4),
        }

        if vector_type not in vector_info:
            return vector_type

        vector_base, dimension = vector_info[vector_type]

        # 如果标量是 float，向量需要提升到 float 向量
        if scalar_type == DataType.FLOAT and vector_base == DataType.HALF:
            if dimension == 2:
                return DataType.FLOAT2
            elif dimension == 3:
                return DataType.FLOAT3
            elif dimension == 4:
                return DataType.FLOAT4

        # 其他情况保持向量类型不变
        return vector_type

    def _get_higher_precision_scalar_type(self, type1: DataType, type2: DataType) -> DataType:
        """获取两个标量类型中精度更高的类型"""
        # 精度优先级：float > half > int > uint > bool
        precision_order = {
            DataType.BOOL: 1,
            DataType.UINT: 2,
            DataType.INT: 3,
            DataType.HALF: 4,
            DataType.FLOAT: 5,
        }

        order1 = precision_order.get(type1, 0)
        order2 = precision_order.get(type2, 0)

        return type1 if order1 >= order2 else type2

    def _infer_unary_operation_result_type(self, operand_type: DataType, operator: str) -> DataType:
        """推断一元运算结果的类型"""
        # 大多数一元运算符保持操作数的类型
        if operator in ['+', '-', '!', '~']:
            return operand_type

        # 默认返回操作数类型
        return operand_type

    def _infer_function_result_type(self, func_name: str, arg_types: List[DataType]) -> DataType:
        """推断函数返回类型"""
        return self.type_engine.infer_function_call_type(func_name, arg_types)








###########################检查类型转换############################################






    def _is_precision_promotion(self, from_type: DataType, to_type: DataType) -> bool:
        """判断是否是精度提升（低精度到高精度，低维度到高维度）"""
        if from_type == to_type or from_type == DataType.UNKNOWN or to_type == DataType.UNKNOWN:
            return False

        # 精度提升规则：half -> float, int -> float, 低维度 -> 高维度
        promotion_rules = [
            # 标量精度提升
            (DataType.HALF, DataType.FLOAT),
            (DataType.INT, DataType.FLOAT),
            (DataType.UINT, DataType.FLOAT),
            (DataType.INT, DataType.HALF),
            (DataType.UINT, DataType.HALF),

            # 向量精度提升
            (DataType.HALF2, DataType.FLOAT2),
            (DataType.HALF3, DataType.FLOAT3),
            (DataType.HALF4, DataType.FLOAT4),

            # 维度提升（标量到向量）
            (DataType.HALF, DataType.HALF2),
            (DataType.HALF, DataType.HALF3),
            (DataType.HALF, DataType.HALF4),
            (DataType.FLOAT, DataType.FLOAT2),
            (DataType.FLOAT, DataType.FLOAT3),
            (DataType.FLOAT, DataType.FLOAT4),
            (DataType.INT, DataType.INT2),
            (DataType.INT, DataType.INT3),
            (DataType.INT, DataType.INT4),

            # 跨类型提升（标量到不同精度向量）
            (DataType.HALF, DataType.FLOAT2),
            (DataType.HALF, DataType.FLOAT3),
            (DataType.HALF, DataType.FLOAT4),
            (DataType.INT, DataType.FLOAT2),
            (DataType.INT, DataType.FLOAT3),
            (DataType.INT, DataType.FLOAT4),
        ]

        return (from_type, to_type) in promotion_rules

    def _is_type_constructor(self, func_name: str) -> bool:
        """判断是否是类型构造函数"""
        type_constructors = [
            'float', 'half', 'int', 'uint', 'bool',
            'float2', 'float3', 'float4',
            'half2', 'half3', 'half4',
            'int2', 'int3', 'int4',
            'uint2', 'uint3', 'uint4',
            'bool2', 'bool3', 'bool4',
            'float4x4', 'float3x3', 'float2x2',
            'half4x4', 'half3x3', 'half2x2'
        ]
        return func_name in type_constructors



    def _needs_type_promotion(self, operand_type: DataType, result_type: DataType) -> bool:
        """检查操作数是否需要类型提升"""
        if operand_type == result_type or operand_type == DataType.UNKNOWN or result_type == DataType.UNKNOWN:
            return False

        # 精度提升规则
        precision_hierarchy = {
            DataType.BOOL: 1,
            DataType.UINT: 2,
            DataType.INT: 3,
            DataType.HALF: 4,
            DataType.FLOAT: 5,
            DataType.HALF2: 6,
            DataType.FLOAT2: 7,
            DataType.HALF3: 8,
            DataType.FLOAT3: 9,
            DataType.HALF4: 10,
            DataType.FLOAT4: 11,
        }

        operand_level = precision_hierarchy.get(operand_type, 0)
        result_level = precision_hierarchy.get(result_type, 0)

        # 只有当结果类型精度更高时才算是类型提升
        return operand_level > 0 and result_level > operand_level

    def _needs_type_conversion(self, source_type: DataType, target_type: DataType) -> bool:
        """检查是否需要类型转换（包括精度损失）"""
        return (source_type != target_type and
                source_type != DataType.UNKNOWN and
                target_type != DataType.UNKNOWN)

    def _get_conversion_type(self, from_type: DataType, to_type: DataType) -> str:
        """获取转换类型"""
        # 精度转换
        if from_type == DataType.FLOAT and to_type == DataType.HALF:
            return "precision_loss"
        elif from_type == DataType.HALF and to_type == DataType.FLOAT:
            return "precision_gain"

        # 向量转换
        vector_types = [DataType.FLOAT2, DataType.FLOAT3, DataType.FLOAT4,
                       DataType.HALF2, DataType.HALF3, DataType.HALF4]
        if from_type in vector_types and to_type in vector_types:
            return "vector_conversion"

        # 标量到向量
        scalar_types = [DataType.FLOAT, DataType.HALF, DataType.INT, DataType.UINT]
        if from_type in scalar_types and to_type in vector_types:
            return "scalar_to_vector"

        # 向量到标量
        if from_type in vector_types and to_type in scalar_types:
            return "vector_to_scalar"

        return "implicit_conversion"








class SyntaxTreeAnalyzer:
    """语法树分析器 - 整合代码行分析和语法树构建"""

    def __init__(self):
        self.tree_builder = SyntaxTreeBuilder()
        self.global_var_types: Dict[str, DataType] = {}
        # 使用全局类型推断引擎
        self.type_engine = type_inference_engine

    def analyze_shader_with_syntax_trees(self, shader_content: str) -> Dict[str, Any]:
        """分析着色器代码并构建语法树"""
        # 使用类型推断引擎解析着色器内容，获取结构体和函数定义
        # 同时将函数参数变量添加到全局变量类型字典中
        self.type_engine.parse_shader_content(shader_content, self.global_var_types)

        # 使用代码行分析器获取有效代码行
        line_analyzer = CodeLineAnalyzer()
        code_lines = line_analyzer.analyze_shader_code(shader_content)

        # 分析每一行代码
        line_analyses = []
        for code_line in code_lines:
            line_analysis = self.tree_builder.analyze_line(
                code_line.content,
                code_line.line_number,
                self.global_var_types
            )
            line_analyses.append(line_analysis)

        # 整合结果
        result = {
            'code_lines': code_lines,
            'line_analyses': line_analyses,
            'global_type_table': self.global_var_types.copy(),
            'struct_definitions': self.type_engine.type_registry.copy(),
            'variable_types': self.type_engine.variable_types.copy()
        }

        return result
