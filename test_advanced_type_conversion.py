#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试高级类型转换场景
"""

from Process.Analysis.syntax_tree_builder import SyntaxTreeAnalyzer

def test_advanced_type_conversions():
    """测试更复杂的类型转换场景"""
    
    analyzer = SyntaxTreeAnalyzer()
    
    # 更复杂的测试代码
    test_code = '''
    float4 complexShader() {
        // 多种精度混合运算
        half h1 = 1.0h;
        half h2 = 2.0h;
        float f1 = 3.0f;
        int i1 = 4;
        uint u1 = 5u;
        
        // 复杂表达式中的类型提升
        float result1 = h1 * f1 + h2;  // h1 和 h2 都会提升为 float
        float result2 = i1 + f1;       // i1 提升为 float
        float result3 = u1 * h1;       // u1 和 h1 都提升为 float
        
        // 向量运算中的类型提升
        half3 hvec = half3(1.0h, 2.0h, 3.0h);
        float fscalar = 2.0f;
        float3 result4 = hvec * fscalar;  // hvec 提升为 float3
        
        // 嵌套运算
        float result5 = (h1 + h2) * f1;  // h1, h2 先提升为 float
        
        // 函数调用中的类型转换
        float3 normalized = normalize(hvec);  // hvec 可能需要提升
        
        return float4(result1, result2, result3, result4.x);
    }
    '''
    
    print("=== 测试高级类型转换场景 ===")
    print("测试代码:")
    print(test_code)
    print("\n" + "="*60)
    
    # 分析代码
    result = analyzer.analyze_shader_with_syntax_trees(test_code)
    
    # 详细分析每一行的转换
    conversion_details = {}
    total_conversions = 0
    
    print("\n=== 详细转换分析 ===")
    for line_analysis in result['line_analyses']:
        if line_analysis.type_conversions:
            line_num = line_analysis.line_number
            line_content = line_analysis.line_content.strip()
            
            print(f"\n第 {line_num} 行: {line_content}")
            
            line_conversions = []
            for conversion in line_analysis.type_conversions:
                total_conversions += 1
                conv_info = {
                    'variable': conversion.get('variable', 'unknown'),
                    'from_type': conversion.get('from_type', 'unknown'),
                    'to_type': conversion.get('to_type', 'unknown'),
                    'conversion_type': conversion.get('conversion_type', 'unknown'),
                    'reason': conversion.get('reason', 'unknown')
                }
                line_conversions.append(conv_info)
                
                print(f"  🔄 {conv_info['variable']}: {conv_info['from_type']} → {conv_info['to_type']}")
                print(f"     类型: {conv_info['conversion_type']}")
                print(f"     原因: {conv_info['reason']}")
            
            conversion_details[line_num] = {
                'content': line_content,
                'conversions': line_conversions
            }
    
    # 统计分析
    print(f"\n=== 转换统计分析 ===")
    print(f"总转换次数: {total_conversions}")
    
    # 按转换类型统计
    type_stats = {}
    variable_stats = {}
    
    for line_num, details in conversion_details.items():
        for conv in details['conversions']:
            conv_type = conv['conversion_type']
            variable = conv['variable']
            
            type_stats[conv_type] = type_stats.get(conv_type, 0) + 1
            variable_stats[variable] = variable_stats.get(variable, 0) + 1
    
    print("\n转换类型分布:")
    for conv_type, count in sorted(type_stats.items()):
        print(f"  {conv_type}: {count} 次")
    
    print("\n变量转换频率:")
    for variable, count in sorted(variable_stats.items(), key=lambda x: x[1], reverse=True):
        print(f"  {variable}: {count} 次")
    
    # 性能影响分析
    print(f"\n=== 性能影响分析 ===")
    high_cost_conversions = ['half_to_float_promotion', 'half2_to_float2_promotion', 
                           'half3_to_float3_promotion', 'half4_to_float4_promotion']
    
    high_cost_count = sum(type_stats.get(conv_type, 0) for conv_type in high_cost_conversions)
    
    print(f"高成本精度提升转换: {high_cost_count} 次")
    if high_cost_count > 0:
        print("💡 优化建议: 考虑统一使用 float 类型以减少精度提升转换")
    
    return total_conversions

if __name__ == "__main__":
    conversion_count = test_advanced_type_conversions()
    print(f"\n✅ 检测到 {conversion_count} 个类型转换，功能正常！")
