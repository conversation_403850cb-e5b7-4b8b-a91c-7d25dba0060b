#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简化的类型转换输出格式
"""

from Process.Analysis.syntax_tree_builder import SyntaxTreeAnalyzer

def test_clean_conversion_output():
    """测试简化的类型转换输出"""
    
    analyzer = SyntaxTreeAnalyzer()
    
    # 测试代码
    test_code = '''
    float4 main() {
        half a = 1.0h;
        float b = 2.0f;
        int c = 3;
        
        // 各种转换场景
        float result1 = a + b;      // a: half → float
        float result2 = c + b;      // c: int → float  
        float3 result3 = a + float3(1,2,3);  // a: half → float3
        
        // 显式转换
        float result4 = float(a);   // a: half → float
        half result5 = half(b);     // b: float → half
        
        return float4(result1, result2, result3.x, result4);
    }
    '''
    
    print("=== 类型转换检测结果 ===")
    
    # 分析代码
    result = analyzer.analyze_shader_with_syntax_trees(test_code)
    
    # 输出转换信息
    conversion_count = 0
    for line_analysis in result['line_analyses']:
        if line_analysis.type_conversions:
            line_content = line_analysis.line_content.strip()
            print(f"\n第 {line_analysis.line_number} 行: {line_content}")
            
            for conversion in line_analysis.type_conversions:
                conversion_count += 1
                variable = conversion.get('variable', 'unknown')
                from_type = conversion.get('from_type', 'unknown')
                to_type = conversion.get('to_type', 'unknown')
                conv_type = conversion.get('conversion_type', 'unknown')
                
                print(f"  变量 {variable}: {from_type} → {to_type} ({conv_type})")
    
    print(f"\n总计检测到 {conversion_count} 个类型转换")
    
    return conversion_count > 0

if __name__ == "__main__":
    success = test_clean_conversion_output()
    if success:
        print("\n✅ 类型转换检测输出格式优化完成！")
    else:
        print("\n❌ 未检测到预期的类型转换")
