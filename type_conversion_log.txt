=== 类型转换统计日志 ===

第 107 行: half _9176 = half(0);
  变量: 0
  转换: int → half
  类型: explicit_cast
  原因: 强制精度转换为 half
  操作: tmp_0 = half(0)
  ---

第 109 行: half _9199 = half(1);
  变量: 1
  转换: int → half
  类型: explicit_cast
  原因: 强制精度转换为 half
  操作: tmp_0 = half(1)
  ---

第 111 行: float3 _8925 = float3(in.IN_WorldNormal.xyz);
  变量: in.IN_WorldNormal.xyz
  转换: half3 → float3
  类型: explicit_cast
  原因: 强制精度转换为 float3
  操作: tmp_0 = float3(in.IN_WorldNormal.xyz)
  ---

第 130 行: half _9251 = half(2);
  变量: 2
  转换: int → half
  类型: explicit_cast
  原因: 强制精度转换为 half
  操作: tmp_0 = half(2)
  ---

第 137 行: float3 _9286 = float3(in.IN_StaticWorldNormal.xyz);
  变量: in.IN_StaticWorldNormal.xyz
  转换: half3 → float3
  类型: explicit_cast
  原因: 强制精度转换为 float3
  操作: tmp_0 = float3(in.IN_StaticWorldNormal.xyz)
  ---

第 138 行: float3 _9331 = (((float4(float3(in.IN_WorldTangent.xyz), 0.0) * _Block1.Local) * float(_9257)) + ((float4(float3(in.IN_WorldBinormal.xyz), 0.0) * _Block1.Local) * float(_9263))) + (_9286 * float(_9270));
  变量: in.IN_WorldTangent.xyz
  转换: half3 → float3
  类型: explicit_cast
  原因: 强制精度转换为 float3
  操作: tmp_0 = float3(in.IN_WorldTangent.xyz)
  ---
  变量: _9257
  转换: half → float
  类型: explicit_cast
  原因: 强制精度转换为 float
  操作: tmp_3 = float(_9257)
  ---
  变量: in.IN_WorldBinormal.xyz
  转换: half3 → float3
  类型: explicit_cast
  原因: 强制精度转换为 float3
  操作: tmp_5 = float3(in.IN_WorldBinormal.xyz)
  ---
  变量: _9263
  转换: half → float
  类型: explicit_cast
  原因: 强制精度转换为 float
  操作: tmp_8 = float(_9263)
  ---
  变量: _9270
  转换: half → float
  类型: explicit_cast
  原因: 强制精度转换为 float
  操作: tmp_11 = float(_9270)
  ---

第 143 行: half _9096 = half(mix(1.0, float(mix(half3(_9019), half3(half(fast::clamp(_9014 + _Block1.cAOoffset, 0.0, 1.0))), in.IN_TintColor.xxx).x * _9074.z), float(_8929)));
  变量: _8929
  转换: half → float
  类型: explicit_cast
  原因: 强制精度转换为 float
  操作: tmp_9 = float(_8929)
  ---

第 144 行: float _9100 = float(_9096);
  变量: _9096
  转换: half → float
  类型: explicit_cast
  原因: 强制精度转换为 float
  操作: tmp_0 = float(_9096)
  ---

第 145 行: float3 _9109 = float3(_9064);
  变量: _9064
  转换: half3 → float3
  类型: explicit_cast
  原因: 强制精度转换为 float3
  操作: tmp_0 = float3(_9064)
  ---

第 147 行: half3 _9145 = _9130.xyz * half3((float3(_Block1.cEmissionColor) * _Block1.cEmissionScale) * float3(_8949));
  变量: _8949
  转换: half3 → float3
  类型: explicit_cast
  原因: 强制精度转换为 float3
  操作: tmp_2 = float3(_8949)
  ---

第 157 行: float3 _9698 = float3(_9179);
  变量: _9179
  转换: half3 → float3
  类型: explicit_cast
  原因: 强制精度转换为 float3
  操作: tmp_0 = float3(_9179)
  ---

第 176 行: float _9510 = float(half(fast::clamp(((float2(0.800000011920928955078125, 0.5) * _9429).x * (1.0 - powr(float(half(powr(float(clamp(_18217, half(0.0), half(1.0))) + fast::clamp(0.20000000298023223876953125 - fast::clamp((-1.0) * _Block1.cCISnowData.z, 0.0, 1.0), 0.0, 1.0), 2.0 - _9462) * float(in.IN_StaticWorldNormal.w))), 0.800000011920928955078125 - (_9462 * 0.4000000059604644775390625)))) + ((_9499 * _9499) * _9499), 0.0, 1.0)));
  变量: in.IN_StaticWorldNormal.w
  转换: half → float
  类型: explicit_cast
  原因: 强制精度转换为 float
  操作: tmp_15 = float(in.IN_StaticWorldNormal.w)
  ---

第 178 行: half _9535 = half(1.0 - fast::clamp((float(in.IN_LocalPosition.y) * (2.0 - _9460)) * _Block1.cCISnowData.y, 0.0, 1.0));
  变量: in.IN_LocalPosition.y
  转换: half → float
  类型: explicit_cast
  原因: 强制精度转换为 float
  操作: tmp_0 = float(in.IN_LocalPosition.y)
  ---

第 179 行: float _9556 = float(half((float(_9535) * fast::clamp(float(_9535 + _18217) + 0.5, 0.0, 1.0)) * _9429));
  变量: _9535
  转换: half → float
  类型: explicit_cast
  原因: 强制精度转换为 float
  操作: tmp_0 = float(_9535)
  ---

第 183 行: float _9603 = float(_9585);
  变量: _9585
  转换: half → float
  类型: explicit_cast
  原因: 强制精度转换为 float
  操作: tmp_0 = float(_9585)
  ---

第 184 行: _18272 = half(mix(float(_9772), 1.0, _9603));
  变量: _9772
  转换: half → float
  类型: explicit_cast
  原因: 强制精度转换为 float
  操作: tmp_0 = float(_9772)
  ---

第 201 行: half3 _8315 = mix(half3(dot(_18225, _8303)), _18225, half3(half(_Block1.cSaturation)));
  变量: _18225
  转换: half3 → float
  类型: implicit_promotion
  原因: dot 函数中的精度提升
  操作: tmp_0 = dot(_18225, _8303)
  ---
  变量: _8303
  转换: half3 → float
  类型: implicit_promotion
  原因: dot 函数中的精度提升
  操作: tmp_0 = dot(_18225, _8303)
  ---

第 219 行: float _9868 = float(_18230);
  变量: _18230
  转换: half → float
  类型: explicit_cast
  原因: 强制精度转换为 float
  操作: tmp_0 = float(_18230)
  ---

第 262 行: float _10004 = fast::min(1.0 - float(half(10) * half(9.9956989288330078125e-05)), _9997.z);
  变量: 10
  转换: int → half
  类型: explicit_cast
  原因: 强制精度转换为 half
  操作: tmp_0 = half(10)
  ---

第 357 行: float _10900 = 3.0 - float(_10704);
  变量: _10704
  转换: uint → float
  类型: explicit_cast
  原因: 强制精度转换为 float
  操作: tmp_0 = float(_10704)
  ---

第 388 行: _18297 = clamp((_11163.w * half(32.0)) + half(float(dot(half3(_8373), half3((float3(_11163.xyz) * float3(2.0)) - float3(1.0)))) * 2.0), half(0.0), half(1.0));
  变量: _11163.xyz
  转换: half3 → float3
  类型: explicit_cast
  原因: 强制精度转换为 float3
  操作: tmp_3 = float3(_11163.xyz)
  ---

第 417 行: _18303 = half(mix(float(_18297), 1.0, fast::clamp(fast::max(_11410.x, fast::max(_11410.y, _11410.z)), 0.0, 1.0)));
  变量: _18297
  转换: half → float
  类型: explicit_cast
  原因: 强制精度转换为 float
  操作: tmp_0 = float(_18297)
  ---

第 438 行: _18306 = half(1.0 - fast::clamp((1.0 - float(half(fast::clamp(1.0 - float(half(powr(float(half(fast::clamp(1.0 - float(_18304), 0.0, 1.0))), fast::max(mix(6.0, 0.0, fast::clamp(_Block1.SHGIParam2.w, 0.0, 1.0)), 0.5)))), 0.0, 1.0)))) * float(half(mix(_Block1.SHAOParam.x, _Block1.SHAOParam.y, 1.0 - (_11479 * _11479)))), 0.0, 1.0));
  变量: _18304
  转换: half → float
  类型: explicit_cast
  原因: 强制精度转换为 float
  操作: tmp_0 = float(_18304)
  ---

第 465 行: float _11560 = float(half(fast::max(0.119999997317790985107421875, float(_18272))));
  变量: _18272
  转换: half → float
  类型: explicit_cast
  原因: 强制精度转换为 float
  操作: tmp_0 = float(_18272)
  ---

第 467 行: float _11919 = float(_11536);
  变量: _11536
  转换: half → float
  类型: explicit_cast
  原因: 强制精度转换为 float
  操作: tmp_0 = float(_11536)
  ---

第 482 行: half _11768 = half(float(_11764) + 0.100000001490116119384765625);
  变量: _11764
  转换: half → float
  类型: explicit_cast
  原因: 强制精度转换为 float
  操作: tmp_0 = float(_11764)
  ---

第 488 行: half _11827 = clamp(dot(half3(fast::normalize(_11622 + _11517)), _9064), half(0.0), half(1.0));
  变量: _9064
  转换: half3 → float
  类型: implicit_promotion
  原因: dot 函数中的精度提升
  操作: tmp_3 = dot(tmp_2, _9064)
  ---

第 489 行: float _11858 = float(_11815) * 0.5;
  变量: _11815
  转换: half → float
  类型: explicit_cast
  原因: 强制精度转换为 float
  操作: tmp_0 = float(_11815)
  ---

第 495 行: float3 _12025 = float3(_10557);
  变量: _10557
  转换: half3 → float3
  类型: explicit_cast
  原因: 强制精度转换为 float3
  操作: tmp_0 = float3(_10557)
  ---

第 497 行: float3 _8521 = ((_9698 + (float3(mix(_9179, half3(half(((_11862 * powr(float(half(fast::max(float(_9199 - (_11827 * _11827)), 0.0078125))), _11858)) * 0.15915493667125701904296875) * float(half4(float4(_11970 / powr(max(half4(half(9.9956989288330078125e-05)), _11973 - (_11975 * (-half(dot(_11517, _11622))))), _11982)) * float4(0.0795769989490509033203125)).x))), half3(half(fast::clamp((_11763 * _11763) * float(dot(_11812, _8303)), 0.0, 1.0)))) * _11812) * float3(_11629))) + (((float3(_11812) * ((_12025 + (_12120 * _11756)) * (fast::min(1000.0, (_12062 * _12062) * 0.3183098733425140380859375) * (1.0 / fast::max((_11736 + sqrt((_11736 * (_11736 - (_11736 * _12080))) + _12080)) * (_11919 + sqrt((_11919 * (_11919 - (_11919 * _12080))) + _12080)), _12108))))) * _11629) * float(_11772))) * _10588;
  变量: _11970
  转换: half4 → float4
  类型: implicit_promotion
  原因: / 运算中的精度提升
  操作: tmp_13 = _11970 / tmp_12
  ---

第 750 行: float _14176 = float(_19481);
  变量: _19481
  转换: half → float
  类型: explicit_cast
  原因: 强制精度转换为 float
  操作: tmp_0 = float(_19481)
  ---

第 758 行: half3 _14069 = half3(((float3(_19821) * float3(_19786)) * _14176) * float(clamp(((_14029 + _11768) / _11777) * _11777, half(0.0), half(1.0)) * clamp(((_19481 + _11764) / _11790) * _11790, half(0.0), half(1.0))));
  变量: _19821
  转换: half3 → float3
  类型: explicit_cast
  原因: 强制精度转换为 float3
  操作: tmp_0 = float3(_19821)
  ---

第 759 行: half _14084 = clamp(dot(half3(fast::normalize(_19751 + _19716)), _9064), half(0.0), half(1.0));
  变量: _9064
  转换: half3 → float
  类型: implicit_promotion
  原因: dot 函数中的精度提升
  操作: tmp_3 = dot(tmp_2, _9064)
  ---

第 784 行: _19965 = (_18431 + (float3(mix(_9179, half3(half(((_11862 * powr(float(half(fast::max(float(_9199 - (_14084 * _14084)), 0.0078125))), _11858)) * 0.15915493667125701904296875) * float(half4(float4(_11970 / powr(max(half4(half(9.9956989288330078125e-05)), _11973 - (_11975 * (-half(dot(_19716, _19751))))), _11982)) * float4(0.0795769989490509033203125)).x))), half3(half(fast::clamp((_14020 * _14020) * float(dot(_14069, _8303)), 0.0, 1.0)))) * _14069) * float3(_19573))) + (((float3(_14069) * _19883) * _19573) * float(_14029));
  变量: _11970
  转换: half4 → float4
  类型: implicit_promotion
  原因: / 运算中的精度提升
  操作: tmp_13 = _11970 / tmp_12
  ---

第 861 行: float _14797 = float(_14451);
  变量: _14451
  转换: uint → float
  类型: explicit_cast
  原因: 强制精度转换为 float
  操作: tmp_0 = float(_14451)
  ---

第 869 行: float _14850 = (_14788 - float((_8397 & 251658240u) >> 24u)) + ((3.0 - float(_14469)) * 3.0);
  变量: _14469
  转换: uint → float
  类型: explicit_cast
  原因: 强制精度转换为 float
  操作: tmp_4 = float(_14469)
  ---

第 929 行: half3 _15343 = half3(((float3(_15133.xyz) * float3(2.0)) - float3(1.0)) * float(_15258)).xyz;
  变量: _15133.xyz
  转换: half3 → float3
  类型: explicit_cast
  原因: 强制精度转换为 float3
  操作: tmp_0 = float3(_15133.xyz)
  ---
  变量: _15258
  转换: half → float
  类型: explicit_cast
  原因: 强制精度转换为 float
  操作: tmp_5 = float(_15258)
  ---

第 931 行: _18130.x = half(float(dot(_14556, _15343)) * 2.0);
  变量: _14556
  转换: half3 → float
  类型: implicit_promotion
  原因: dot 函数中的精度提升
  操作: tmp_0 = dot(_14556, _15343)
  ---
  变量: _15343
  转换: half3 → float
  类型: implicit_promotion
  原因: dot 函数中的精度提升
  操作: tmp_0 = dot(_14556, _15343)
  ---

第 932 行: half3 _15352 = half3(((float3(_15193.xyz) * float3(2.0)) - float3(1.0)) * float(_15262)).xyz;
  变量: _15193.xyz
  转换: half3 → float3
  类型: explicit_cast
  原因: 强制精度转换为 float3
  操作: tmp_0 = float3(_15193.xyz)
  ---
  变量: _15262
  转换: half → float
  类型: explicit_cast
  原因: 强制精度转换为 float
  操作: tmp_5 = float(_15262)
  ---

第 933 行: _18130.y = half(float(dot(_14556, _15352)) * 2.0);
  变量: _14556
  转换: half3 → float
  类型: implicit_promotion
  原因: dot 函数中的精度提升
  操作: tmp_0 = dot(_14556, _15352)
  ---
  变量: _15352
  转换: half3 → float
  类型: implicit_promotion
  原因: dot 函数中的精度提升
  操作: tmp_0 = dot(_14556, _15352)
  ---

第 934 行: half3 _15361 = half3(((float3(_15253.xyz) * float3(2.0)) - float3(1.0)) * float(_15266)).xyz;
  变量: _15253.xyz
  转换: half3 → float3
  类型: explicit_cast
  原因: 强制精度转换为 float3
  操作: tmp_0 = float3(_15253.xyz)
  ---
  变量: _15266
  转换: half → float
  类型: explicit_cast
  原因: 强制精度转换为 float
  操作: tmp_5 = float(_15266)
  ---

第 935 行: _18130.z = half(float(dot(_14556, _15361)) * 2.0);
  变量: _14556
  转换: half3 → float
  类型: implicit_promotion
  原因: dot 函数中的精度提升
  操作: tmp_0 = dot(_14556, _15361)
  ---
  变量: _15361
  转换: half3 → float
  类型: implicit_promotion
  原因: dot 函数中的精度提升
  操作: tmp_0 = dot(_14556, _15361)
  ---

第 939 行: _18819 = half3(((float3(_15343) * float3(0.21199999749660491943359375)) + (float3(_15352) * float3(0.714999973773956298828125))) + (float3(_15361) * float3(0.0719999969005584716796875)));
  变量: _15343
  转换: half3 → float3
  类型: explicit_cast
  原因: 强制精度转换为 float3
  操作: tmp_0 = float3(_15343)
  ---
  变量: _15352
  转换: half3 → float3
  类型: explicit_cast
  原因: 强制精度转换为 float3
  操作: tmp_3 = float3(_15352)
  ---
  变量: _15361
  转换: half3 → float3
  类型: explicit_cast
  原因: 强制精度转换为 float3
  操作: tmp_7 = float3(_15361)
  ---

第 1028 行: float3 _14569 = float3(_18824);
  变量: _18824
  转换: half3 → float3
  类型: explicit_cast
  原因: 强制精度转换为 float3
  操作: tmp_0 = float3(_18824)
  ---

第 1029 行: float _14575 = float(dot(_18826, _18826));
  变量: _18826
  转换: half3 → float
  类型: implicit_promotion
  原因: dot 函数中的精度提升
  操作: tmp_0 = dot(_18826, _18826)
  ---
  变量: _18826
  转换: half3 → float
  类型: implicit_promotion
  原因: dot 函数中的精度提升
  操作: tmp_0 = dot(_18826, _18826)
  ---

第 1034 行: float3 _14600 = (float3(_18826) / float3(sqrt(_14575))) * float3(fast::clamp(1.0 - (_14592 * _14592), 0.0, 1.0));
  变量: _18826
  转换: half3 → float3
  类型: explicit_cast
  原因: 强制精度转换为 float3
  操作: tmp_0 = float3(_18826)
  ---

第 1075 行: float3 _15762 = float3(_10569);
  变量: _10569
  转换: half3 → float3
  类型: explicit_cast
  原因: 强制精度转换为 float3
  操作: tmp_0 = float3(_10569)
  ---

第 1086 行: float _8657 = float(_18329);
  变量: _18329
  转换: half → float
  类型: explicit_cast
  原因: 强制精度转换为 float
  操作: tmp_0 = float(_18329)
  ---

第 1099 行: float4 _16284 = float4(half4(_16335, _16336, _16335, half(0.858085691928863525390625 * float(_16342))) * _9064.yzxx);
  变量: _16342
  转换: half → float
  类型: explicit_cast
  原因: 强制精度转换为 float
  操作: tmp_0 = float(_16342)
  ---

第 1100 行: float4 _16306 = float4(_16385);
  变量: _16385
  转换: half4 → float4
  类型: explicit_cast
  原因: 强制精度转换为 float4
  操作: tmp_0 = float4(_16385)
  ---

第 1107 行: float4 _16451 = float4(half4(_16335, _16336, _16335, half(0.858085691928863525390625 * float(_16509))) * _16397.yzxx);
  变量: _16509
  转换: half → float
  类型: explicit_cast
  原因: 强制精度转换为 float
  操作: tmp_0 = float(_16509)
  ---

第 1108 行: float4 _16473 = float4(_16552);
  变量: _16552
  转换: half4 → float4
  类型: explicit_cast
  原因: 强制精度转换为 float4
  操作: tmp_0 = float4(_16552)
  ---

第 1109 行: half3 _16258 = ((((max(_16279 + (half3(half(dot(_Block1.cSHCoefficients[1], _16284)), half(dot(_Block1.cSHCoefficients[3], _16284)), half(dot(_Block1.cSHCoefficients[5], _16284))) + half3(half(dot(_Block1.cSHCoefficients[2], _16306)), half(dot(_Block1.cSHCoefficients[4], _16306)), half(dot(_Block1.cSHCoefficients[6], _16306)))), _9179) * half3(half(0.699999988079071044921875 + (0.300000011920928955078125 * _Block1.EnvInfo.z)))) * half3(half(float(_8295 * _18329) * mix(1.0, _Block1.WorldProbeInfo.y, step(0.0, _Block1.cVisibilitySH[0].w))))) * half3(half(_Block1.GIInfo.z))) + half3(float3(max(_16279 + (half3(half(dot(_Block1.cSHCoefficients[1], _16451)), half(dot(_Block1.cSHCoefficients[3], _16451)), half(dot(_Block1.cSHCoefficients[5], _16451))) + half3(half(dot(_Block1.cSHCoefficients[2], _16473)), half(dot(_Block1.cSHCoefficients[4], _16473)), half(dot(_Block1.cSHCoefficients[6], _16473)))), _9179)) * float3(((3.1415927410125732421875 * float(clamp(dot(_9064, _16397), half(0.0), half(1.0)))) * _Block1.WorldProbeInfo.w) * float(half(float(_8295 * _18236)) * half(0.5))))) * half3(half(_Block1.cSHCoefficients[0].w));
  变量: _9064
  转换: half3 → float
  类型: implicit_promotion
  原因: dot 函数中的精度提升
  操作: tmp_51 = dot(_9064, _16397)
  ---
  变量: _16397
  转换: half3 → float
  类型: implicit_promotion
  原因: dot 函数中的精度提升
  操作: tmp_51 = dot(_9064, _16397)
  ---

第 1110 行: half3 _8776 = ((_18255 + (half3((float3(_15813 * (half(fast::min(1000.0, (_15839 * _15839) * 0.3183098733425140380859375)) * half(0.25 / fast::max(_15861 * _15861, _12108)))) * _Block1.cVirtualLitColor.xyz) * _15805) + half3((_Block1.cVirtualLitColor.xyz * abs(_8378)) * _15762))) + half3(fast::min(float3(8192.0), ((((float3((_15813 * (half(fast::min(1000.0, (_16159 * _16159) * 0.3183098733425140380859375)) * half(0.25 / fast::max(_15861 * ((float(_16031) * _15858) + _15854), _12108)))) * _16031) * float3(_Block1.cLocalVirtualLitPos.w)) + float3(_10569 * _16031)) * float(half(fast::min(float(half((_15986 * _15986) / ((float(_15974) * abs(_Block1.cLocalVirtualLitCustom.y)) + 9.9999997473787516355514526367188e-05))), _Block1.cLocalVirtualLitCustom.w)))) * (_Block1.cLocalVirtualLitColor.xyz * abs(_Block1.cLocalVirtualLitColor.w))) * _Block1.DiyLightingInfo.z))) * _8696;
  变量: _16031
  转换: half → float
  类型: explicit_cast
  原因: 强制精度转换为 float
  操作: tmp_25 = float(_16031)
  ---
  变量: _15974
  转换: half → float
  类型: explicit_cast
  原因: 强制精度转换为 float
  操作: tmp_42 = float(_15974)
  ---

第 1116 行: float3 _16698 = float3(_8303);
  变量: _8303
  转换: half3 → float3
  类型: explicit_cast
  原因: 强制精度转换为 float3
  操作: tmp_0 = float3(_8303)
  ---

第 1126 行: float3 _16862 = (((((((((((_Block1.cVisibilitySH[0].xyz * _8657) * _15762) * _Block1.AmbientColor.w) * _Block1.ReflectionProbeBBMin.w) * float3(_Block1.cSHCoefficients[0].w)).xyz + float3(_18268)).xyz + (_18989 + float3(((_9179 * _9254) + _8393) * _8696))).xyz + float3((_19028 + half3(float3(_16258) * float3((_15805 * 0.5) + 0.5))) * _10569)).xyz + float3((half3(_9698 * _10588) * _11772) + _8776)).xyz * dot(_16715, _16698)) + (_16805 - (_16805 * _16715))).xyz;
  变量: _18268
  转换: half3 → float3
  类型: explicit_cast
  原因: 强制精度转换为 float3
  操作: tmp_7 = float3(_18268)
  ---
  变量: _16258
  转换: half3 → float3
  类型: explicit_cast
  原因: 强制精度转换为 float3
  操作: tmp_17 = float3(_16258)
  ---

