#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试显式强制类型转换检测功能
"""

from Process.Analysis.syntax_tree_builder import SyntaxTreeAnalyzer

def test_explicit_cast_detection():
    """测试显式强制类型转换检测"""
    
    analyzer = SyntaxTreeAnalyzer()
    
    # 测试代码：包含各种显式强制类型转换
    test_code = '''
    float4 testCasts() {
        // 基础类型转换
        half h = 1.0h;
        int i = 42;
        uint u = 100u;
        
        // 构造函数形式的强制转换
        float f1 = float(h);        // half -> float
        float f2 = float(i);        // int -> float
        half h2 = half(f1);         // float -> half (精度损失)
        int i2 = int(f2);           // float -> int (精度损失)
        
        // 向量构造函数转换
        float2 v2 = float2(h, f1);  // half, float -> float2
        float3 v3 = float3(i, f1, h); // int, float, half -> float3
        float4 v4 = float4(v2, v3.xy); // float2, float2 -> float4
        
        // 向量类型转换
        half3 hv3 = half3(1.0h, 2.0h, 3.0h);
        float3 fv3 = float3(hv3);   // half3 -> float3
        
        // C风格强制转换
        float cf1 = (float)i;       // int -> float
        half ch1 = (half)f1;        // float -> half
        int ci1 = (int)f1;          // float -> int
        
        // 矩阵转换
        float4x4 matrix = float4x4(
            1.0f, 0.0f, 0.0f, 0.0f,
            0.0f, 1.0f, 0.0f, 0.0f,
            0.0f, 0.0f, 1.0f, 0.0f,
            0.0f, 0.0f, 0.0f, 1.0f
        );
        
        return float4(f1, f2, v2.x, fv3.x);
    }
    '''
    
    print("=== 测试显式强制类型转换检测 ===")
    print("测试代码:")
    print(test_code)
    print("\n" + "="*60)
    
    # 分析代码
    result = analyzer.analyze_shader_with_syntax_trees(test_code)
    
    # 统计转换
    explicit_casts = []
    implicit_conversions = []
    total_conversions = 0
    
    print("\n=== 检测到的类型转换 ===")
    for line_analysis in result['line_analyses']:
        if line_analysis.type_conversions:
            line_num = line_analysis.line_number
            line_content = line_analysis.line_content.strip()
            
            print(f"\n第 {line_num} 行: {line_content}")
            
            for conversion in line_analysis.type_conversions:
                total_conversions += 1
                reason = conversion.get('reason', '')
                
                # 分类转换类型
                if '显式强制转换' in reason or 'C风格强制转换' in reason:
                    explicit_casts.append(conversion)
                    cast_type = "🔧 显式强转"
                else:
                    implicit_conversions.append(conversion)
                    cast_type = "⚡ 隐式转换"
                
                print(f"  {cast_type} {conversion.get('variable', 'unknown')}: "
                      f"{conversion.get('from_type', 'unknown')} → {conversion.get('to_type', 'unknown')}")
                print(f"     类型: {conversion.get('conversion_type', 'unknown')}")
                print(f"     原因: {reason}")
    
    # 详细统计
    print(f"\n=== 转换统计 ===")
    print(f"总转换次数: {total_conversions}")
    print(f"显式强制转换: {len(explicit_casts)} 次")
    print(f"隐式类型转换: {len(implicit_conversions)} 次")
    
    # 显式转换详细分析
    if explicit_casts:
        print(f"\n=== 显式强制转换详情 ===")
        cast_types = {}
        for cast in explicit_casts:
            conv_type = cast.get('conversion_type', 'unknown')
            cast_types[conv_type] = cast_types.get(conv_type, 0) + 1
        
        for conv_type, count in sorted(cast_types.items()):
            print(f"  {conv_type}: {count} 次")
    
    # 隐式转换详细分析
    if implicit_conversions:
        print(f"\n=== 隐式类型转换详情 ===")
        implicit_types = {}
        for conv in implicit_conversions:
            conv_type = conv.get('conversion_type', 'unknown')
            implicit_types[conv_type] = implicit_types.get(conv_type, 0) + 1
        
        for conv_type, count in sorted(implicit_types.items()):
            print(f"  {conv_type}: {count} 次")
    
    # 性能影响分析
    print(f"\n=== 性能影响分析 ===")
    precision_loss_casts = [c for c in explicit_casts 
                           if 'demotion' in c.get('conversion_type', '')]
    
    if precision_loss_casts:
        print(f"⚠️  精度损失转换: {len(precision_loss_casts)} 次")
        for cast in precision_loss_casts:
            print(f"   {cast.get('variable', 'unknown')}: "
                  f"{cast.get('from_type', 'unknown')} → {cast.get('to_type', 'unknown')}")
    
    high_cost_conversions = len([c for c in explicit_casts + implicit_conversions
                               if 'promotion' in c.get('conversion_type', '')])
    
    print(f"💰 高成本精度提升: {high_cost_conversions} 次")
    
    return total_conversions > 0

if __name__ == "__main__":
    success = test_explicit_cast_detection()
    if success:
        print("\n✅ 显式强制类型转换检测功能正常工作！")
    else:
        print("\n❌ 未检测到预期的类型转换")
