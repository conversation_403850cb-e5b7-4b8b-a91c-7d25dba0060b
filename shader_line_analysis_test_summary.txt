🎯 语法树分析摘要报告
==================================================
📊 基本统计:
  总代码行: 943
  变量声明: 2315
  运算过程: 2807
  临时变量: 2100

🔍 类型分析:
  类型转换: 833
  精度问题: 0

🎨 类型分布:
  bool: 15 (0.6%)
  custom_struct: 2 (0.1%)
  float: 827 (35.7%)
  float2: 115 (5.0%)
  float3: 482 (20.8%)
  float3x3: 5 (0.2%)
  float3x4: 2 (0.1%)
  float4: 73 (3.2%)
  float4x4: 6 (0.3%)
  half: 320 (13.8%)
  half3: 233 (10.1%)
  half4: 49 (2.1%)
  int: 24 (1.0%)
  sampler: 31 (1.3%)
  uint: 79 (3.4%)
  unknown: 52 (2.2%)

📈 语法树分析特点:
  基于AST的精确语法解析
  完整的运算过程分解
  临时变量类型推断
  向量成员访问支持

💡 性能建议:
  ⚠️  发现 833 个类型转换，建议优化