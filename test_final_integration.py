#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终集成测试：验证在生成运算过程时直接记录类型转换
"""

from Process.Analysis.syntax_tree_builder import SyntaxTreeAnalyzer

def test_final_integration():
    """最终集成测试"""
    
    analyzer = SyntaxTreeAnalyzer()
    
    # 综合测试代码
    test_code = '''
    float4 main() {
        half a = 1.0h;
        float b = 2.0f;
        
        // 1. 运算中的隐式提升
        float result1 = a + b;          // a: half → float
        
        // 2. 强制类型转换
        float result2 = float(a);       // a: half → float (强转)
        half result3 = half(b);         // b: float → half (强转)
        
        // 3. 内置函数中的隐式提升
        float result4 = dot(a, b);      // a: half → float
        
        // 4. 成员访问（如果支持的话）
        float result5 = a * _Block1.cEmissionScale;
        
        return float4(result1, result2, result3, result4);
    }
    '''
    
    print("=== 最终集成测试 ===")
    print("验证：在生成运算过程时直接记录类型转换")
    print("期望：能检测到强转和隐式提升两种类型转换")
    print("\n" + "="*60)
    
    # 分析代码
    result = analyzer.analyze_shader_with_syntax_trees(test_code)
    
    # 统计转换
    total_conversions = 0
    explicit_casts = 0
    implicit_promotions = 0
    
    print("=== 检测到的类型转换 ===")
    for line_analysis in result['line_analyses']:
        if hasattr(line_analysis, 'type_conversions') and line_analysis.type_conversions:
            line_content = line_analysis.line_content.strip()
            print(f"\n第 {line_analysis.line_number} 行: {line_content}")
            
            for conversion in line_analysis.type_conversions:
                total_conversions += 1
                variable = conversion.get('variable', 'unknown')
                from_type = conversion.get('from_type', 'unknown')
                to_type = conversion.get('to_type', 'unknown')
                conv_type = conversion.get('conversion_type', 'unknown')
                reason = conversion.get('reason', 'unknown')
                
                if conv_type == 'explicit_cast':
                    explicit_casts += 1
                    print(f"  🔧 强转: {variable} ({from_type} → {to_type})")
                elif conv_type == 'implicit_promotion':
                    implicit_promotions += 1
                    print(f"  ⬆️  隐式提升: {variable} ({from_type} → {to_type})")
                else:
                    print(f"  ❓ 其他: {variable} ({from_type} → {to_type}) - {conv_type}")
                
                print(f"     原因: {reason}")
                
                # 特别标注成员访问
                if '.' in variable:
                    print(f"     ✅ 成员访问变量: {variable}")
    
    # 统计结果
    print(f"\n=== 统计结果 ===")
    print(f"总转换次数: {total_conversions}")
    print(f"强制转换: {explicit_casts} 次")
    print(f"隐式提升: {implicit_promotions} 次")
    
    # 验证核心功能
    print(f"\n=== 功能验证 ===")
    
    success_checks = []
    
    # 检查1: 是否检测到隐式提升
    if implicit_promotions > 0:
        success_checks.append("✅ 检测到隐式精度提升")
    else:
        success_checks.append("❌ 未检测到隐式精度提升")
    
    # 检查2: 是否检测到强制转换
    if explicit_casts > 0:
        success_checks.append("✅ 检测到强制类型转换")
    else:
        success_checks.append("❌ 未检测到强制类型转换")
    
    # 检查3: 是否有运算过程生成
    operation_count = 0
    for line_analysis in result['line_analyses']:
        if hasattr(line_analysis, 'operation_process') and line_analysis.operation_process:
            operation_count += len(line_analysis.operation_process)
    
    if operation_count > 0:
        success_checks.append(f"✅ 生成了 {operation_count} 个运算过程")
    else:
        success_checks.append("❌ 未生成运算过程")
    
    # 检查4: 类型转换是否在运算过程生成时记录
    if total_conversions > 0:
        success_checks.append("✅ 在运算过程生成时记录了类型转换")
    else:
        success_checks.append("❌ 未记录类型转换")
    
    for check in success_checks:
        print(f"  {check}")
    
    # 总体评估
    success_count = len([c for c in success_checks if c.startswith("✅")])
    total_checks = len(success_checks)
    
    print(f"\n=== 总体评估 ===")
    print(f"通过检查: {success_count}/{total_checks}")
    
    if success_count >= 3:
        print("🎉 集成功能基本正常！")
        print("💡 现在可以在生成运算过程时直接记录类型转换")
        print("🎯 实现了你要求的两种转换检测：强转和隐式提升")
        return True
    else:
        print("❌ 集成功能需要进一步完善")
        return False

if __name__ == "__main__":
    test_final_integration()
