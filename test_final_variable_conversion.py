#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试：精确的变量类型转换检测
"""

from Process.Analysis.syntax_tree_builder import SyntaxTreeAnalyzer

def test_final_variable_conversion():
    """测试最终的变量类型转换检测效果"""
    
    analyzer = SyntaxTreeAnalyzer()
    
    # 测试代码 - 模拟你提到的场景
    test_code = '''
    float4 main() {
        half _9176 = 1.0h;
        half _9199 = 2.0h;
        float _5678 = 3.0f;
        
        // 你要求的场景：在构造函数中看到具体变量的转换
        half3 tmp_0 = half3(_9176);           // _9176: half → half3 (scalar_broadcast)
        float3 tmp_1 = float3(_9176, _9199, _5678);  // _9176,_9199: half → float, _5678: 无转换
        float4 tmp_2 = float4(_9176);         // _9176: half → float (precision_promotion)
        
        // 运算中的转换
        float result = _9176 + _5678;         // _9176: half → float (precision_promotion)
        
        return tmp_2;
    }
    '''
    
    print("=== 最终变量类型转换检测结果 ===")
    print("展示格式：变量名: 原类型 → 目标类型 (转换类型)")
    print("\n" + "="*60)
    
    # 分析代码
    result = analyzer.analyze_shader_with_syntax_trees(test_code)
    
    # 按行输出转换信息
    for line_analysis in result['line_analyses']:
        if line_analysis.type_conversions:
            line_content = line_analysis.line_content.strip()
            print(f"\n第 {line_analysis.line_number} 行: {line_content}")
            
            for conversion in line_analysis.type_conversions:
                variable = conversion.get('variable', 'unknown')
                from_type = conversion.get('from_type', 'unknown')
                to_type = conversion.get('to_type', 'unknown')
                conv_type = conversion.get('conversion_type', 'unknown')
                
                # 突出显示变量名和转换信息
                print(f"  🎯 变量 {variable}: {from_type} → {to_type} ({conv_type})")
    
    # 总结
    total_conversions = sum(len(line.type_conversions) for line in result['line_analyses'] if line.type_conversions)
    print(f"\n=== 总结 ===")
    print(f"✅ 总共检测到 {total_conversions} 个变量类型转换")
    print("✅ 每个转换都精确指出了具体的变量名")
    print("✅ 转换类型清晰分类（标量广播、精度提升等）")
    
    return total_conversions > 0

if __name__ == "__main__":
    success = test_final_variable_conversion()
    if success:
        print("\n🎉 完美！现在可以精确看到每个变量的类型转换！")
        print("💡 正如你要求的：在 tmp_0 = half3(_9176) 中，明确指出是 _9176 发生了转换")
    else:
        print("\n❌ 检测功能需要进一步完善")
