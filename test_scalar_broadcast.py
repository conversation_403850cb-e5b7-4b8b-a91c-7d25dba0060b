#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试标量广播转换的精确检测
"""

from Process.Analysis.syntax_tree_builder import SyntaxTreeAnalyzer

def test_scalar_broadcast():
    """测试标量广播转换检测"""
    
    analyzer = SyntaxTreeAnalyzer()
    
    # 测试代码 - 专门测试标量广播场景
    test_code = '''
    float4 main() {
        half _9176 = 1.0h;
        float _5678 = 2.0f;
        int _1234 = 3;
        
        // 标量广播到同类型向量
        half3 result1 = half3(_9176);     // _9176: half → half (scalar_broadcast)
        float2 result2 = float2(_5678);   // _5678: float → float (scalar_broadcast)  
        int4 result3 = int4(_1234);       // _1234: int → int (scalar_broadcast)
        
        // 标量广播到不同精度向量 (既有广播又有精度提升)
        float3 result4 = float3(_9176);   // _9176: half → float (precision_promotion)
        float4 result5 = float4(_1234);   // _1234: int → float (precision_promotion)
        
        return result5;
    }
    '''
    
    print("=== 标量广播转换检测 ===")
    print("重点测试：标量变量到向量类型的转换")
    print("\n" + "="*50)
    
    # 分析代码
    result = analyzer.analyze_shader_with_syntax_trees(test_code)
    
    # 输出转换信息
    broadcast_count = 0
    promotion_count = 0
    
    for line_analysis in result['line_analyses']:
        if line_analysis.type_conversions:
            line_content = line_analysis.line_content.strip()
            
            # 只关注构造函数行
            if any(constructor in line_content for constructor in ['half3(', 'float2(', 'int4(', 'float3(', 'float4(']):
                print(f"\n第 {line_analysis.line_number} 行: {line_content}")
                
                for conversion in line_analysis.type_conversions:
                    variable = conversion.get('variable', 'unknown')
                    from_type = conversion.get('from_type', 'unknown')
                    to_type = conversion.get('to_type', 'unknown')
                    conv_type = conversion.get('conversion_type', 'unknown')
                    
                    if 'scalar_broadcast' in conv_type:
                        broadcast_count += 1
                        print(f"  📡 标量广播: {variable} ({from_type} → {to_type})")
                        print(f"     类型: {conv_type}")
                    elif 'precision_promotion' in conv_type:
                        promotion_count += 1
                        print(f"  ⬆️  精度提升: {variable} ({from_type} → {to_type})")
                        print(f"     类型: {conv_type}")
                    else:
                        print(f"  🔄 其他转换: {variable} ({from_type} → {to_type})")
                        print(f"     类型: {conv_type}")
    
    print(f"\n=== 转换统计 ===")
    print(f"标量广播转换: {broadcast_count} 次")
    print(f"精度提升转换: {promotion_count} 次")
    print(f"总转换次数: {broadcast_count + promotion_count}")
    
    # 验证期望结果
    expected_broadcasts = 3  # half3(_9176), float2(_5678), int4(_1234)
    expected_promotions = 2  # float3(_9176), float4(_1234)
    
    print(f"\n=== 验证结果 ===")
    print(f"期望标量广播: {expected_broadcasts} 次，实际: {broadcast_count} 次")
    print(f"期望精度提升: {expected_promotions} 次，实际: {promotion_count} 次")
    
    success = (broadcast_count >= expected_broadcasts and promotion_count >= expected_promotions)
    
    return success

if __name__ == "__main__":
    success = test_scalar_broadcast()
    if success:
        print("\n✅ 标量广播检测功能完美！")
        print("💡 能够精确区分标量广播和精度提升转换")
    else:
        print("\n❌ 标量广播检测需要进一步优化")
