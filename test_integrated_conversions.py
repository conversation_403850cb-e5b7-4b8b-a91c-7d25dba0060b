#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试集成的类型转换检测：在生成运算过程时直接记录转换
"""

from Process.Analysis.syntax_tree_builder import SyntaxTreeAnalyzer

def test_integrated_conversions():
    """测试集成的类型转换检测"""
    
    analyzer = SyntaxTreeAnalyzer()
    
    # 测试代码：包含成员访问的类型转换
    test_code = '''
    float4 main() {
        half a = 1.0h;
        float b = 2.0f;
        
        // 测试成员访问中的类型转换
        float result1 = tmp_0 * _Block1.cEmissionScale;  // 应该显示 _Block1.cEmissionScale
        
        // 测试强制转换
        float result2 = float(a);       // 强转：a (half -> float)
        half result3 = half(b);         // 强转：b (float -> half)
        
        // 测试运算中的隐式提升
        float result4 = a + b;          // 隐式提升：a (half -> float)
        float result5 = dot(a, b);      // 隐式提升：a (half -> float)
        
        return float4(result1, result2, result4, result5);
    }
    '''
    
    print("=== 集成的类型转换检测测试 ===")
    print("测试重点：在生成运算过程时直接记录类型转换")
    print("期望：能正确识别成员访问变量名，如 _Block1.cEmissionScale")
    print("\n" + "="*60)
    
    # 分析代码
    result = analyzer.analyze_shader_with_syntax_trees(test_code)
    
    # 统计转换
    total_conversions = 0
    explicit_casts = 0
    implicit_promotions = 0
    
    print("=== 检测到的类型转换 ===")
    for line_analysis in result['line_analyses']:
        if hasattr(line_analysis, 'type_conversions') and line_analysis.type_conversions:
            line_content = line_analysis.line_content.strip()
            print(f"\n第 {line_analysis.line_number} 行: {line_content}")
            
            for conversion in line_analysis.type_conversions:
                total_conversions += 1
                variable = conversion.get('variable', 'unknown')
                from_type = conversion.get('from_type', 'unknown')
                to_type = conversion.get('to_type', 'unknown')
                conv_type = conversion.get('conversion_type', 'unknown')
                reason = conversion.get('reason', 'unknown')
                
                if conv_type == 'explicit_cast':
                    explicit_casts += 1
                    print(f"  🔧 强转: {variable} ({from_type} → {to_type})")
                elif conv_type == 'implicit_promotion':
                    implicit_promotions += 1
                    print(f"  ⬆️  隐式提升: {variable} ({from_type} → {to_type})")
                else:
                    print(f"  ❓ 其他: {variable} ({from_type} → {to_type}) - {conv_type}")
                
                print(f"     原因: {reason}")
                
                # 特别检查成员访问变量名
                if '.' in variable:
                    print(f"     ✅ 成功识别成员访问变量: {variable}")
    
    # 统计结果
    print(f"\n=== 统计结果 ===")
    print(f"总转换次数: {total_conversions}")
    print(f"强制转换: {explicit_casts} 次")
    print(f"隐式提升: {implicit_promotions} 次")
    
    # 验证成员访问变量名识别
    member_access_found = False
    for line_analysis in result['line_analyses']:
        if hasattr(line_analysis, 'type_conversions') and line_analysis.type_conversions:
            for conversion in line_analysis.type_conversions:
                variable = conversion.get('variable', 'unknown')
                if '.' in variable and '_Block1' in variable:
                    member_access_found = True
                    print(f"\n✅ 成功识别成员访问变量: {variable}")
                    break
    
    if not member_access_found:
        print(f"\n❌ 未能正确识别成员访问变量名")
    
    # 验证运算过程生成
    print(f"\n=== 运算过程验证 ===")
    operation_count = 0
    for line_analysis in result['line_analyses']:
        if hasattr(line_analysis, 'operation_process') and line_analysis.operation_process:
            operation_count += len(line_analysis.operation_process)
            for op in line_analysis.operation_process:
                if '_Block1' in op.string:
                    print(f"✅ 运算过程包含成员访问: {op.string}")
    
    print(f"总运算过程数: {operation_count}")
    
    return total_conversions > 0 and (explicit_casts > 0 or implicit_promotions > 0)

if __name__ == "__main__":
    success = test_integrated_conversions()
    if success:
        print("\n🎉 集成的类型转换检测功能正常！")
        print("💡 现在在生成运算过程时直接记录类型转换，数据更准确")
    else:
        print("\n❌ 集成的类型转换检测需要进一步完善")
