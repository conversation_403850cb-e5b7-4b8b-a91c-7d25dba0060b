#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试成员访问的类型转换检测
"""

from Process.Analysis.syntax_tree_builder import SyntaxTreeAnalyzer

def test_member_access_conversion():
    """测试成员访问的类型转换检测"""
    
    analyzer = SyntaxTreeAnalyzer()
    
    # 测试成员访问的代码
    test_code = '''
    float4 main() {
        half tmp_0 = 1.0h;
        
        // 成员访问中的类型转换
        float result = tmp_0 * _Block1.cEmissionScale;
        
        return float4(result, 0, 0, 1);
    }
    '''
    
    print("=== 成员访问类型转换测试 ===")
    
    # 分析代码
    result = analyzer.analyze_shader_with_syntax_trees(test_code)
    
    print("=== 分析结果 ===")
    for line_analysis in result['line_analyses']:
        line_content = line_analysis.line_content.strip()
        if not line_content or line_content.startswith('//') or line_content in ['{', '}']:
            continue
            
        print(f"\n第 {line_analysis.line_number} 行: {line_content}")
        
        # 检查运算过程
        if hasattr(line_analysis, 'operation_process') and line_analysis.operation_process:
            print(f"  运算过程: {len(line_analysis.operation_process)} 个操作")
            for op in line_analysis.operation_process:
                print(f"    {op.string}")
        
        # 检查类型转换
        if hasattr(line_analysis, 'type_conversions') and line_analysis.type_conversions:
            print(f"  类型转换: {len(line_analysis.type_conversions)} 个")
            for conv in line_analysis.type_conversions:
                variable = conv.get('variable', 'unknown')
                from_type = conv.get('from_type', 'unknown')
                to_type = conv.get('to_type', 'unknown')
                conv_type = conv.get('conversion_type', 'unknown')
                reason = conv.get('reason', 'unknown')
                
                print(f"    变量 {variable}: {from_type} → {to_type} ({conv_type})")
                print(f"    原因: {reason}")
                
                # 检查是否正确识别了成员访问变量
                if '.' in variable and '_Block1' in variable:
                    print(f"    ✅ 成功识别成员访问变量: {variable}")
                elif variable == '_Block1':
                    print(f"    ❌ 只识别到基础变量，应该是完整的成员访问")

if __name__ == "__main__":
    test_member_access_conversion()
