#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整工作流程测试：从代码分析到网页显示
"""

from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
import tempfile
import os
import webbrowser

def test_complete_workflow():
    """测试完整的工作流程"""
    
    # 真实的着色器代码示例
    shader_code = '''
vertex VertexOut vertex_main(
    VertexIn in [[stage_in]],
    constant Uniforms& uniforms [[buffer(0)]]
) {
    VertexOut out;
    
    // 类型转换场景1: 构造函数中的标量广播
    half _9176 = 0.5h;
    half3 position = half3(_9176);  // _9176: half → half3 (scalar_broadcast)
    
    // 类型转换场景2: 精度提升运算
    float4 worldPos = float4(position, 1.0f);  // position: half3 → float4 (precision_promotion)
    
    // 类型转换场景3: 矩阵运算中的类型转换
    half4x4 mvpMatrix = uniforms.mvpMatrix;
    float4 clipPos = mvpMatrix * worldPos;  // mvpMatrix: half4x4 → float4x4 (precision_promotion)
    
    // 类型转换场景4: 混合类型运算
    int index = 42;
    float weight = index * 0.1f;  // index: int → float (precision_promotion)
    
    out.position = clipPos;
    out.color = float4(weight, weight, weight, 1.0f);
    
    return out;
}
'''
    
    print("=== 完整工作流程测试 ===")
    print("测试场景：真实着色器代码的类型转换分析和网页显示")
    print("\n" + "="*60)
    
    # 步骤1: 创建分析处理器
    print("📊 步骤1: 初始化分析处理器...")
    processor = ShaderAnalysisProcessor()
    
    # 步骤2: 分析着色器代码
    print("🔍 步骤2: 分析着色器代码...")
    result = processor.analyze_shader(shader_code)
    
    # 步骤3: 统计分析结果
    print("📈 步骤3: 统计分析结果...")
    analysis_data = result['analysis']
    
    total_conversions = 0
    conversion_types = {}
    variable_conversions = {}
    
    for line_analysis in analysis_data['line_analyses']:
        if hasattr(line_analysis, 'type_conversions') and line_analysis.type_conversions:
            for conversion in line_analysis.type_conversions:
                total_conversions += 1
                
                conv_type = conversion.get('conversion_type', 'unknown')
                variable = conversion.get('variable', 'unknown')
                
                conversion_types[conv_type] = conversion_types.get(conv_type, 0) + 1
                variable_conversions[variable] = variable_conversions.get(variable, 0) + 1
    
    print(f"   总转换次数: {total_conversions}")
    print(f"   涉及变量数: {len(variable_conversions)}")
    print(f"   转换类型数: {len(conversion_types)}")
    
    # 步骤4: 生成HTML报告
    print("🌐 步骤4: 生成HTML报告...")
    with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
        html_content = processor._generate_html_report(result, shader_code)
        f.write(html_content)
        html_file = f.name
    
    print(f"   HTML文件: {html_file}")
    
    # 步骤5: 验证HTML内容
    print("✅ 步骤5: 验证HTML内容...")
    
    # 检查关键变量是否正确显示
    key_variables = ['_9176', 'position', 'worldPos', 'mvpMatrix', 'index']
    key_conversions = ['scalar_broadcast', 'precision_promotion']
    
    html_checks = []
    for var in key_variables:
        if f'变量 {var}:' in html_content:
            html_checks.append(f"✅ 变量 {var} 正确显示")
        else:
            html_checks.append(f"❌ 变量 {var} 未显示")
    
    for conv in key_conversions:
        if conv in html_content:
            html_checks.append(f"✅ 转换类型 {conv} 正确显示")
        else:
            html_checks.append(f"❌ 转换类型 {conv} 未显示")
    
    for check in html_checks:
        print(f"   {check}")
    
    # 步骤6: 打开浏览器查看
    print("🚀 步骤6: 在浏览器中打开报告...")
    try:
        file_url = f'file:///{os.path.abspath(html_file).replace(os.sep, "/")}'
        webbrowser.open(file_url)
        print(f"   浏览器已打开: {file_url}")
    except Exception as e:
        print(f"   浏览器打开失败: {e}")
    
    # 步骤7: 显示详细的转换信息
    print("\n📋 步骤7: 详细转换信息...")
    for line_analysis in analysis_data['line_analyses']:
        if hasattr(line_analysis, 'type_conversions') and line_analysis.type_conversions:
            line_content = line_analysis.line_content.strip()
            if line_content and not line_content.startswith('//'):
                print(f"\n第 {line_analysis.line_number} 行: {line_content}")
                
                for conversion in line_analysis.type_conversions:
                    variable = conversion.get('variable', 'unknown')
                    from_type = conversion.get('from_type', 'unknown')
                    to_type = conversion.get('to_type', 'unknown')
                    conv_type = conversion.get('conversion_type', 'unknown')
                    
                    print(f"  🎯 变量 {variable}: {from_type} → {to_type} ({conv_type})")
    
    print(f"\n🎉 完整工作流程测试完成！")
    print(f"📊 总共检测到 {total_conversions} 个类型转换")
    print(f"🌐 HTML报告已生成并在浏览器中打开")
    
    return total_conversions > 0

if __name__ == "__main__":
    success = test_complete_workflow()
    if success:
        print("\n✅ 完整工作流程测试成功！")
        print("💡 从代码分析到网页显示，所有功能都正常工作")
    else:
        print("\n❌ 工作流程存在问题，需要进一步检查")
