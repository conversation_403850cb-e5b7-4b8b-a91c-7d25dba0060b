#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试类型转换统计功能
"""

from Process.Analysis.syntax_tree_builder import SyntaxTreeAnalyzer
import os

def test_conversion_tracking():
    """测试类型转换统计功能"""
    
    analyzer = SyntaxTreeAnalyzer()
    
    # 测试代码：包含多种类型转换
    test_code = '''
    float4 main() {
        half a = 1.0h;
        float b = 2.0f;
        half3 c = half3(1.0h, 2.0h, 3.0h);
        
        // 变量a的转换
        float result1 = a + b;          // a: half → float (第7行)
        float result2 = float(a);       // a: half → float (第8行)
        
        // 变量b的转换
        half result3 = half(b);         // b: float → half (第11行)
        
        // 变量c的转换
        float3 result4 = c + float3(1.0f, 2.0f, 3.0f);  // c: half3 → float3 (第14行)
        
        // 字面量转换
        half d = 3.0f;                  // 3.0f: float → half (第17行)
        
        return float4(result1, result2, result3, result4.x);
    }
    '''
    
    print("=== 类型转换统计功能测试 ===")
    print("测试目标：统计每个变量的类型转换记录")
    print("期望：记录变量名、行号、转换类型等信息")
    print("\n" + "="*60)
    
    # 分析代码
    result = analyzer.analyze_shader_with_syntax_trees(test_code)
    
    print("\n=== 检查生成的文件 ===")
    
    # 检查生成的日志文件
    log_files = [
        "type_conversion_log.txt",
        "type_conversion_summary.txt"
    ]
    
    for log_file in log_files:
        if os.path.exists(log_file):
            print(f"✅ {log_file} 已生成")
            
            # 显示文件内容的前几行
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                print(f"   文件行数: {len(lines)}")
                if lines:
                    print(f"   前3行内容:")
                    for i, line in enumerate(lines[:3]):
                        print(f"     {i+1}: {line.strip()}")
        else:
            print(f"❌ {log_file} 未生成")
    
    # 检查统计数据
    tree_builder = analyzer.tree_builder
    if hasattr(tree_builder, 'variable_conversions'):
        conversions = tree_builder.variable_conversions
        print(f"\n=== 统计数据检查 ===")
        print(f"统计的变量数: {len(conversions)}")
        
        for variable, records in conversions.items():
            print(f"变量 '{variable}': {len(records)} 次转换")
            for record in records:
                line_num = record['line_number']
                from_type = record['from_type']
                to_type = record['to_type']
                conv_type = record['conversion_type']
                print(f"  第{line_num}行: {from_type} → {to_type} ({conv_type})")
    
    # 验证期望的转换
    expected_variables = ['a', 'b', 'c']  # 不包括字面量和tmp变量
    
    print(f"\n=== 验证结果 ===")
    success = True
    
    if hasattr(tree_builder, 'variable_conversions'):
        actual_variables = set(tree_builder.variable_conversions.keys())
        
        # 检查是否包含期望的变量（排除字面量）
        found_real_variables = [var for var in actual_variables if not var.replace('.', '').replace('_', '').isdigit()]
        
        if found_real_variables:
            print(f"✅ 检测到变量转换: {found_real_variables}")
        else:
            print(f"❌ 未检测到预期的变量转换")
            success = False
        
        # 检查是否正确排除了tmp变量
        tmp_variables = [var for var in actual_variables if var.startswith('tmp_')]
        if not tmp_variables:
            print(f"✅ 正确排除了tmp变量")
        else:
            print(f"❌ 错误包含了tmp变量: {tmp_variables}")
            success = False
        
        # 检查转换记录是否包含行号信息
        has_line_info = True
        for records in tree_builder.variable_conversions.values():
            for record in records:
                if 'line_number' not in record or record['line_number'] <= 0:
                    has_line_info = False
                    break
        
        if has_line_info:
            print(f"✅ 转换记录包含行号信息")
        else:
            print(f"❌ 转换记录缺少行号信息")
            success = False
    else:
        print(f"❌ 未找到转换统计数据")
        success = False
    
    return success

if __name__ == "__main__":
    success = test_conversion_tracking()
    if success:
        print("\n🎉 类型转换统计功能正常！")
        print("💡 现在可以统计每个变量的类型转换记录")
        print("📁 生成的文件:")
        print("   - type_conversion_log.txt: 详细转换日志")
        print("   - type_conversion_summary.txt: 转换统计摘要")
    else:
        print("\n❌ 类型转换统计功能需要进一步完善")
