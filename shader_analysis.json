{"analysis": {"code_lines": [{"__type__": "CodeLineInfo", "line_number": 2, "content": "vertex VertexOut vertex_main("}, {"__type__": "CodeLineInfo", "line_number": 3, "content": "VertexIn in [[stage_in]],"}, {"__type__": "CodeLineInfo", "line_number": 4, "content": "constant Uniforms& uniforms [[buffer(0)]]"}, {"__type__": "CodeLineInfo", "line_number": 5, "content": ") {"}, {"__type__": "CodeLineInfo", "line_number": 6, "content": "VertexOut out;"}, {"__type__": "CodeLineInfo", "line_number": 9, "content": "half _9176 = 0.5h;"}, {"__type__": "CodeLineInfo", "line_number": 10, "content": "half3 position = half3(_9176);  // _9176: half → half3 (scalar_broadcast)"}, {"__type__": "CodeLineInfo", "line_number": 13, "content": "float4 worldPos = float4(position, 1.0f);  // position: half3 → float4 (precision_promotion)"}, {"__type__": "CodeLineInfo", "line_number": 16, "content": "half4x4 mvpMatrix = uniforms.mvpMatrix;"}, {"__type__": "CodeLineInfo", "line_number": 17, "content": "float4 clipPos = mvpMatrix * worldPos;  // mvpMatrix: half4x4 → float4x4 (precision_promotion)"}, {"__type__": "CodeLineInfo", "line_number": 20, "content": "int index = 42;"}, {"__type__": "CodeLineInfo", "line_number": 21, "content": "float weight = index * 0.1f;  // index: int → float (precision_promotion)"}, {"__type__": "CodeLineInfo", "line_number": 23, "content": "out.position = clipPos;"}, {"__type__": "CodeLineInfo", "line_number": 24, "content": "out.color = float4(weight, weight, weight, 1.0f);"}, {"__type__": "CodeLineInfo", "line_number": 26, "content": "return out;"}], "line_analyses": [{"__type__": "LineAnalysisResult", "line_number": 2, "line_content": "vertex VertexOut vertex_main(", "syntax_tree": {"__type__": "ASTNode", "node_type": {"__type__": "NodeType", "_value_": "declaration", "_name_": "DECLARATION", "_sort_order_": 5}, "value": "VertexOut", "data_type": {"__type__": "DataType", "_value_": "unknown", "_name_": "UNKNOWN", "_sort_order_": 0}, "children": [], "line_number": 2, "position": 0}, "variable_types": {"VertexOut": {"__type__": "DataType", "_value_": "unknown", "_name_": "UNKNOWN", "_sort_order_": 0}}, "type_conversions": [], "precision_issues": [], "operation_process": [], "message": "分析成功"}, {"__type__": "LineAnalysisResult", "line_number": 3, "line_content": "VertexIn in [[stage_in]],", "syntax_tree": {"__type__": "ASTNode", "node_type": {"__type__": "NodeType", "_value_": "declaration", "_name_": "DECLARATION", "_sort_order_": 5}, "value": "in", "data_type": {"__type__": "DataType", "_value_": "unknown", "_name_": "UNKNOWN", "_sort_order_": 0}, "children": [], "line_number": 3, "position": 0}, "variable_types": {"in": {"__type__": "DataType", "_value_": "unknown", "_name_": "UNKNOWN", "_sort_order_": 0}}, "type_conversions": [], "precision_issues": [], "operation_process": [], "message": "分析成功"}, {"__type__": "LineAnalysisResult", "line_number": 4, "line_content": "constant Uniforms& uniforms [[buffer(0)]]", "syntax_tree": {"__type__": "ASTNode", "node_type": {"__type__": "NodeType", "_value_": "variable", "_name_": "VARIABLE", "_sort_order_": 0}, "value": "constant", "data_type": {"__type__": "DataType", "_value_": "unknown", "_name_": "UNKNOWN", "_sort_order_": 0}, "children": [], "line_number": 4, "position": 0}, "variable_types": {"constant": {"__type__": "DataType", "_value_": "unknown", "_name_": "UNKNOWN", "_sort_order_": 0}}, "type_conversions": [], "precision_issues": [], "operation_process": [], "message": "分析成功"}, {"__type__": "LineAnalysisResult", "line_number": 5, "line_content": ") {", "syntax_tree": null, "variable_types": {}, "type_conversions": [], "precision_issues": [], "operation_process": [], "message": "分析失败: 'NoneType' object has no attribute 'node_type'"}, {"__type__": "LineAnalysisResult", "line_number": 6, "line_content": "VertexOut out;", "syntax_tree": {"__type__": "ASTNode", "node_type": {"__type__": "NodeType", "_value_": "declaration", "_name_": "DECLARATION", "_sort_order_": 5}, "value": "out", "data_type": {"__type__": "DataType", "_value_": "unknown", "_name_": "UNKNOWN", "_sort_order_": 0}, "children": [], "line_number": 6, "position": 0}, "variable_types": {"out": {"__type__": "DataType", "_value_": "unknown", "_name_": "UNKNOWN", "_sort_order_": 0}}, "type_conversions": [], "precision_issues": [], "operation_process": [], "message": "分析成功"}, {"__type__": "LineAnalysisResult", "line_number": 9, "line_content": "half _9176 = 0.5h;", "syntax_tree": {"__type__": "ASTNode", "node_type": {"__type__": "NodeType", "_value_": "assignment", "_name_": "ASSIGNMENT", "_sort_order_": 4}, "value": "=", "data_type": {"__type__": "DataType", "_value_": "half", "_name_": "HALF", "_sort_order_": 18}, "children": [{"type": "ASTNode", "value": "_9176"}, {"type": "ASTNode", "value": "0.5"}], "line_number": 9, "position": 0}, "variable_types": {"_9176": {"__type__": "DataType", "_value_": "half", "_name_": "HALF", "_sort_order_": 18}}, "type_conversions": [], "precision_issues": [], "operation_process": [{"__type__": "operationProcess", "string": "_9176 = 0.5", "left_dataType": [{"__type__": "DataType", "_value_": "half", "_name_": "HALF", "_sort_order_": 18}], "right_dataType": [{"__type__": "DataType", "_value_": "float", "_name_": "FLOAT", "_sort_order_": 14}]}], "message": "分析成功"}, {"__type__": "LineAnalysisResult", "line_number": 10, "line_content": "half3 position = half3(_9176);  // _9176: half → half3 (scalar_broadcast)", "syntax_tree": {"__type__": "ASTNode", "node_type": {"__type__": "NodeType", "_value_": "assignment", "_name_": "ASSIGNMENT", "_sort_order_": 4}, "value": "=", "data_type": {"__type__": "DataType", "_value_": "half3", "_name_": "HALF3", "_sort_order_": 20}, "children": [{"type": "ASTNode", "value": "position"}, {"type": "ASTNode", "value": "half3"}], "line_number": 10, "position": 0}, "variable_types": {"position": {"__type__": "DataType", "_value_": "half3", "_name_": "HALF3", "_sort_order_": 20}, "_9176": {"__type__": "DataType", "_value_": "half", "_name_": "HALF", "_sort_order_": 18}}, "type_conversions": [{"variable": "_9176", "operation": "tmp_0 = half3(_9176)", "from_type": "half", "to_type": "half3", "conversion_type": "scalar_broadcast", "reason": "构造函数参数转换为 half3"}], "precision_issues": [], "operation_process": [{"__type__": "operationProcess", "string": "tmp_0 = half3(_9176)", "left_dataType": [{"__type__": "DataType", "_value_": "half3", "_name_": "HALF3", "_sort_order_": 20}], "right_dataType": [{"__type__": "DataType", "_value_": "half", "_name_": "HALF", "_sort_order_": 18}]}, {"__type__": "operationProcess", "string": "position = tmp_0", "left_dataType": [{"__type__": "DataType", "_value_": "half3", "_name_": "HALF3", "_sort_order_": 20}], "right_dataType": [{"__type__": "DataType", "_value_": "half3", "_name_": "HALF3", "_sort_order_": 20}]}], "message": "分析成功"}, {"__type__": "LineAnalysisResult", "line_number": 13, "line_content": "float4 worldPos = float4(position, 1.0f);  // position: half3 → float4 (precision_promotion)", "syntax_tree": {"__type__": "ASTNode", "node_type": {"__type__": "NodeType", "_value_": "assignment", "_name_": "ASSIGNMENT", "_sort_order_": 4}, "value": "=", "data_type": {"__type__": "DataType", "_value_": "float4", "_name_": "FLOAT4", "_sort_order_": 17}, "children": [{"type": "ASTNode", "value": "worldPos"}, {"type": "ASTNode", "value": "float4"}], "line_number": 13, "position": 0}, "variable_types": {"worldPos": {"__type__": "DataType", "_value_": "float4", "_name_": "FLOAT4", "_sort_order_": 17}, "position": {"__type__": "DataType", "_value_": "half3", "_name_": "HALF3", "_sort_order_": 20}}, "type_conversions": [{"variable": "position", "operation": "tmp_0 = float4(position, 1.0f)", "from_type": "half3", "to_type": "float4", "conversion_type": "vector_conversion", "reason": "构造函数参数转换为 float4"}, {"variable": "1.0f", "operation": "tmp_0 = float4(position, 1.0f)", "from_type": "float", "to_type": "float4", "conversion_type": "scalar_broadcast", "reason": "构造函数参数转换为 float4"}], "precision_issues": [], "operation_process": [{"__type__": "operationProcess", "string": "tmp_0 = float4(position, 1.0f)", "left_dataType": [{"__type__": "DataType", "_value_": "float4", "_name_": "FLOAT4", "_sort_order_": 17}], "right_dataType": [{"__type__": "DataType", "_value_": "half3", "_name_": "HALF3", "_sort_order_": 20}, {"__type__": "DataType", "_value_": "float", "_name_": "FLOAT", "_sort_order_": 14}]}, {"__type__": "operationProcess", "string": "worldPos = tmp_0", "left_dataType": [{"__type__": "DataType", "_value_": "float4", "_name_": "FLOAT4", "_sort_order_": 17}], "right_dataType": [{"__type__": "DataType", "_value_": "float4", "_name_": "FLOAT4", "_sort_order_": 17}]}], "message": "分析成功"}, {"__type__": "LineAnalysisResult", "line_number": 16, "line_content": "half4x4 mvpMatrix = uniforms.mvpMatrix;", "syntax_tree": {"__type__": "ASTNode", "node_type": {"__type__": "NodeType", "_value_": "assignment", "_name_": "ASSIGNMENT", "_sort_order_": 4}, "value": "=", "data_type": {"__type__": "DataType", "_value_": "half4x4", "_name_": "HALF4X4", "_sort_order_": 43}, "children": [{"type": "ASTNode", "value": "mvpMatrix"}, {"type": "ASTNode", "value": "uniforms.mvpMatrix"}], "line_number": 16, "position": 0}, "variable_types": {"mvpMatrix": {"__type__": "DataType", "_value_": "half4x4", "_name_": "HALF4X4", "_sort_order_": 43}, "uniforms.mvpMatrix": {"__type__": "DataType", "_value_": "unknown", "_name_": "UNKNOWN", "_sort_order_": 0}}, "type_conversions": [], "precision_issues": [], "operation_process": [{"__type__": "operationProcess", "string": "mvpMatrix = uniforms.mvpMatrix", "left_dataType": [{"__type__": "DataType", "_value_": "half4x4", "_name_": "HALF4X4", "_sort_order_": 43}], "right_dataType": [{"__type__": "DataType", "_value_": "unknown", "_name_": "UNKNOWN", "_sort_order_": 0}]}], "message": "分析成功"}, {"__type__": "LineAnalysisResult", "line_number": 17, "line_content": "float4 clipPos = mvpMatrix * worldPos;  // mvpMatrix: half4x4 → float4x4 (precision_promotion)", "syntax_tree": {"__type__": "ASTNode", "node_type": {"__type__": "NodeType", "_value_": "assignment", "_name_": "ASSIGNMENT", "_sort_order_": 4}, "value": "=", "data_type": {"__type__": "DataType", "_value_": "float4", "_name_": "FLOAT4", "_sort_order_": 17}, "children": [{"type": "ASTNode", "value": "clipPos"}, {"type": "ASTNode", "value": "*"}], "line_number": 17, "position": 0}, "variable_types": {"clipPos": {"__type__": "DataType", "_value_": "float4", "_name_": "FLOAT4", "_sort_order_": 17}, "mvpMatrix": {"__type__": "DataType", "_value_": "half4x4", "_name_": "HALF4X4", "_sort_order_": 43}, "worldPos": {"__type__": "DataType", "_value_": "float4", "_name_": "FLOAT4", "_sort_order_": 17}}, "type_conversions": [{"variable": "tmp_0", "operation": "clipPos = tmp_0", "from_type": "half4x4", "to_type": "float4", "conversion_type": "type_conversion", "reason": "赋值类型转换"}], "precision_issues": [], "operation_process": [{"__type__": "operationProcess", "string": "tmp_0 = mvpMatrix * worldPos", "left_dataType": [{"__type__": "DataType", "_value_": "half4x4", "_name_": "HALF4X4", "_sort_order_": 43}], "right_dataType": [{"__type__": "DataType", "_value_": "half4x4", "_name_": "HALF4X4", "_sort_order_": 43}, {"__type__": "DataType", "_value_": "float4", "_name_": "FLOAT4", "_sort_order_": 17}]}, {"__type__": "operationProcess", "string": "clipPos = tmp_0", "left_dataType": [{"__type__": "DataType", "_value_": "float4", "_name_": "FLOAT4", "_sort_order_": 17}], "right_dataType": [{"__type__": "DataType", "_value_": "half4x4", "_name_": "HALF4X4", "_sort_order_": 43}]}], "message": "分析成功"}, {"__type__": "LineAnalysisResult", "line_number": 20, "line_content": "int index = 42;", "syntax_tree": {"__type__": "ASTNode", "node_type": {"__type__": "NodeType", "_value_": "assignment", "_name_": "ASSIGNMENT", "_sort_order_": 4}, "value": "=", "data_type": {"__type__": "DataType", "_value_": "int", "_name_": "INT", "_sort_order_": 6}, "children": [{"type": "ASTNode", "value": "index"}, {"type": "ASTNode", "value": "42"}], "line_number": 20, "position": 0}, "variable_types": {"index": {"__type__": "DataType", "_value_": "int", "_name_": "INT", "_sort_order_": 6}}, "type_conversions": [], "precision_issues": [], "operation_process": [{"__type__": "operationProcess", "string": "index = 42", "left_dataType": [{"__type__": "DataType", "_value_": "int", "_name_": "INT", "_sort_order_": 6}], "right_dataType": [{"__type__": "DataType", "_value_": "int", "_name_": "INT", "_sort_order_": 6}]}], "message": "分析成功"}, {"__type__": "LineAnalysisResult", "line_number": 21, "line_content": "float weight = index * 0.1f;  // index: int → float (precision_promotion)", "syntax_tree": {"__type__": "ASTNode", "node_type": {"__type__": "NodeType", "_value_": "assignment", "_name_": "ASSIGNMENT", "_sort_order_": 4}, "value": "=", "data_type": {"__type__": "DataType", "_value_": "float", "_name_": "FLOAT", "_sort_order_": 14}, "children": [{"type": "ASTNode", "value": "weight"}, {"type": "ASTNode", "value": "*"}], "line_number": 21, "position": 0}, "variable_types": {"weight": {"__type__": "DataType", "_value_": "float", "_name_": "FLOAT", "_sort_order_": 14}, "index": {"__type__": "DataType", "_value_": "int", "_name_": "INT", "_sort_order_": 6}}, "type_conversions": [{"variable": "index", "operation": "tmp_0 = index * 0.1f", "from_type": "int", "to_type": "float", "conversion_type": "precision_promotion", "reason": "精度提升用于 * 运算"}], "precision_issues": [], "operation_process": [{"__type__": "operationProcess", "string": "tmp_0 = index * 0.1f", "left_dataType": [{"__type__": "DataType", "_value_": "float", "_name_": "FLOAT", "_sort_order_": 14}], "right_dataType": [{"__type__": "DataType", "_value_": "int", "_name_": "INT", "_sort_order_": 6}, {"__type__": "DataType", "_value_": "float", "_name_": "FLOAT", "_sort_order_": 14}]}, {"__type__": "operationProcess", "string": "weight = tmp_0", "left_dataType": [{"__type__": "DataType", "_value_": "float", "_name_": "FLOAT", "_sort_order_": 14}], "right_dataType": [{"__type__": "DataType", "_value_": "float", "_name_": "FLOAT", "_sort_order_": 14}]}], "message": "分析成功"}, {"__type__": "LineAnalysisResult", "line_number": 23, "line_content": "out.position = clipPos;", "syntax_tree": {"__type__": "ASTNode", "node_type": {"__type__": "NodeType", "_value_": "assignment", "_name_": "ASSIGNMENT", "_sort_order_": 4}, "value": "=", "data_type": {"__type__": "DataType", "_value_": "unknown", "_name_": "UNKNOWN", "_sort_order_": 0}, "children": [{"type": "ASTNode", "value": "out.position"}, {"type": "ASTNode", "value": "clipPos"}], "line_number": 23, "position": 0}, "variable_types": {"out.position": {"__type__": "DataType", "_value_": "unknown", "_name_": "UNKNOWN", "_sort_order_": 0}, "clipPos": {"__type__": "DataType", "_value_": "float4", "_name_": "FLOAT4", "_sort_order_": 17}}, "type_conversions": [], "precision_issues": [], "operation_process": [{"__type__": "operationProcess", "string": "out.position = clipPos", "left_dataType": [], "right_dataType": [{"__type__": "DataType", "_value_": "float4", "_name_": "FLOAT4", "_sort_order_": 17}]}], "message": "分析成功"}, {"__type__": "LineAnalysisResult", "line_number": 24, "line_content": "out.color = float4(weight, weight, weight, 1.0f);", "syntax_tree": {"__type__": "ASTNode", "node_type": {"__type__": "NodeType", "_value_": "assignment", "_name_": "ASSIGNMENT", "_sort_order_": 4}, "value": "=", "data_type": {"__type__": "DataType", "_value_": "unknown", "_name_": "UNKNOWN", "_sort_order_": 0}, "children": [{"type": "ASTNode", "value": "out.color"}, {"type": "ASTNode", "value": "float4"}], "line_number": 24, "position": 0}, "variable_types": {"out.color": {"__type__": "DataType", "_value_": "unknown", "_name_": "UNKNOWN", "_sort_order_": 0}, "weight": {"__type__": "DataType", "_value_": "float", "_name_": "FLOAT", "_sort_order_": 14}}, "type_conversions": [{"variable": "weight", "operation": "tmp_0 = float4(weight, weight, weight, 1.0f)", "from_type": "float", "to_type": "float4", "conversion_type": "scalar_broadcast", "reason": "构造函数参数转换为 float4"}, {"variable": "weight", "operation": "tmp_0 = float4(weight, weight, weight, 1.0f)", "from_type": "float", "to_type": "float4", "conversion_type": "scalar_broadcast", "reason": "构造函数参数转换为 float4"}, {"variable": "weight", "operation": "tmp_0 = float4(weight, weight, weight, 1.0f)", "from_type": "float", "to_type": "float4", "conversion_type": "scalar_broadcast", "reason": "构造函数参数转换为 float4"}, {"variable": "1.0f", "operation": "tmp_0 = float4(weight, weight, weight, 1.0f)", "from_type": "float", "to_type": "float4", "conversion_type": "scalar_broadcast", "reason": "构造函数参数转换为 float4"}], "precision_issues": [], "operation_process": [{"__type__": "operationProcess", "string": "tmp_0 = float4(weight, weight, weight, 1.0f)", "left_dataType": [{"__type__": "DataType", "_value_": "float4", "_name_": "FLOAT4", "_sort_order_": 17}], "right_dataType": [{"__type__": "DataType", "_value_": "float", "_name_": "FLOAT", "_sort_order_": 14}, {"__type__": "DataType", "_value_": "float", "_name_": "FLOAT", "_sort_order_": 14}, {"__type__": "DataType", "_value_": "float", "_name_": "FLOAT", "_sort_order_": 14}, {"__type__": "DataType", "_value_": "float", "_name_": "FLOAT", "_sort_order_": 14}]}, {"__type__": "operationProcess", "string": "out.color = tmp_0", "left_dataType": [], "right_dataType": [{"__type__": "DataType", "_value_": "float4", "_name_": "FLOAT4", "_sort_order_": 17}]}], "message": "分析成功"}, {"__type__": "LineAnalysisResult", "line_number": 26, "line_content": "return out;", "syntax_tree": {"__type__": "ASTNode", "node_type": {"__type__": "NodeType", "_value_": "declaration", "_name_": "DECLARATION", "_sort_order_": 5}, "value": "out", "data_type": {"__type__": "DataType", "_value_": "unknown", "_name_": "UNKNOWN", "_sort_order_": 0}, "children": [], "line_number": 26, "position": 0}, "variable_types": {"out": {"__type__": "DataType", "_value_": "unknown", "_name_": "UNKNOWN", "_sort_order_": 0}}, "type_conversions": [], "precision_issues": [], "operation_process": [], "message": "分析成功"}], "global_type_table": {"out": {"__type__": "DataType", "_value_": "unknown", "_name_": "UNKNOWN", "_sort_order_": 0}, "_9176": {"__type__": "DataType", "_value_": "half", "_name_": "HALF", "_sort_order_": 18}, "position": {"__type__": "DataType", "_value_": "half3", "_name_": "HALF3", "_sort_order_": 20}, "worldPos": {"__type__": "DataType", "_value_": "float4", "_name_": "FLOAT4", "_sort_order_": 17}, "mvpMatrix": {"__type__": "DataType", "_value_": "half4x4", "_name_": "HALF4X4", "_sort_order_": 43}, "clipPos": {"__type__": "DataType", "_value_": "float4", "_name_": "FLOAT4", "_sort_order_": 17}, "index": {"__type__": "DataType", "_value_": "int", "_name_": "INT", "_sort_order_": 6}, "weight": {"__type__": "DataType", "_value_": "float", "_name_": "FLOAT", "_sort_order_": 14}, "VertexOut": {"__type__": "DataType", "_value_": "unknown", "_name_": "UNKNOWN", "_sort_order_": 0}, "in": {"__type__": "DataType", "_value_": "unknown", "_name_": "UNKNOWN", "_sort_order_": 0}}, "overall_statistics": {"total_lines": 15, "total_variables": 22, "total_operations": 14, "total_temp_variables": 5, "total_type_conversions": 9, "total_precision_issues": 0, "type_distribution": {"unknown": 8, "half": 2, "half3": 2, "float4": 4, "half4x4": 2, "int": 2, "float": 2}}}, "files": {}}