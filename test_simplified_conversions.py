#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简化的类型转换检测：只关注强转和隐式提升
"""

from Process.Analysis.syntax_tree_builder import SyntaxTreeAnalyzer

def test_simplified_conversions():
    """测试简化的类型转换检测"""
    
    analyzer = SyntaxTreeAnalyzer()
    
    # 测试代码：只包含两种类型转换
    test_code = '''
    float4 main() {
        half a = 1.0h;
        float b = 2.0f;
        int c = 3;
        
        // 1. 强制类型转换（显式转换）
        float result1 = float(a);       // 强转：half -> float
        half result2 = half(b);         // 强转：float -> half
        float4 result3 = float4(a);     // 强转：half -> float4
        
        // 2. 运算中的隐式提升
        float result4 = a + b;          // 隐式提升：a (half -> float)
        float result5 = c + b;          // 隐式提升：c (int -> float)
        float result6 = dot(a, b);      // 隐式提升：a (half -> float)
        float result7 = max(a, b);      // 隐式提升：a (half -> float)
        
        return float4(result1, result2, result4, result5);
    }
    '''
    
    print("=== 简化的类型转换检测 ===")
    print("只统计两种类型转换：")
    print("1. 强转 (explicit_cast) - 如 float(a)")
    print("2. 隐式提升 (implicit_promotion) - 如运算中的精度提升")
    print("\n" + "="*60)
    
    # 分析代码
    result = analyzer.analyze_shader_with_syntax_trees(test_code)
    
    # 统计转换类型
    explicit_casts = []
    implicit_promotions = []
    
    for line_analysis in result['line_analyses']:
        if line_analysis.type_conversions:
            line_content = line_analysis.line_content.strip()
            print(f"\n第 {line_analysis.line_number} 行: {line_content}")
            
            for conversion in line_analysis.type_conversions:
                variable = conversion.get('variable', 'unknown')
                from_type = conversion.get('from_type', 'unknown')
                to_type = conversion.get('to_type', 'unknown')
                conv_type = conversion.get('conversion_type', 'unknown')
                reason = conversion.get('reason', 'unknown')
                
                if conv_type == 'explicit_cast':
                    explicit_casts.append(conversion)
                    print(f"  🔧 强转: {variable} ({from_type} → {to_type})")
                    print(f"     原因: {reason}")
                elif conv_type == 'implicit_promotion':
                    implicit_promotions.append(conversion)
                    print(f"  ⬆️  隐式提升: {variable} ({from_type} → {to_type})")
                    print(f"     原因: {reason}")
                else:
                    print(f"  ❓ 其他: {variable} ({from_type} → {to_type}) - {conv_type}")
    
    # 统计结果
    print(f"\n=== 统计结果 ===")
    print(f"强制类型转换: {len(explicit_casts)} 次")
    print(f"隐式精度提升: {len(implicit_promotions)} 次")
    print(f"总转换次数: {len(explicit_casts) + len(implicit_promotions)}")
    
    # 详细分析
    if explicit_casts:
        print(f"\n📋 强制转换详情:")
        for cast in explicit_casts:
            var = cast.get('variable', 'unknown')
            from_t = cast.get('from_type', 'unknown')
            to_t = cast.get('to_type', 'unknown')
            print(f"  {var}: {from_t} → {to_t}")
    
    if implicit_promotions:
        print(f"\n📋 隐式提升详情:")
        for promo in implicit_promotions:
            var = promo.get('variable', 'unknown')
            from_t = promo.get('from_type', 'unknown')
            to_t = promo.get('to_type', 'unknown')
            print(f"  {var}: {from_t} → {to_t}")
    
    return len(explicit_casts) + len(implicit_promotions) > 0

if __name__ == "__main__":
    success = test_simplified_conversions()
    if success:
        print("\n✅ 简化的类型转换检测功能正常！")
        print("💡 现在只关注两种核心转换：强转和隐式提升")
    else:
        print("\n❌ 未检测到预期的类型转换")
