#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试精度转换检测：区分精度转换和维度广播
"""

from Process.Analysis.syntax_tree_builder import SyntaxTreeAnalyzer

def test_precision_conversion():
    """测试精度转换检测"""
    
    analyzer = SyntaxTreeAnalyzer()
    
    # 测试代码：区分精度转换和维度广播
    test_code = '''
    float4 main() {
        half a = 1.0h;
        float b = 2.0f;
        float3 c = float3(1.0f, 2.0f, 3.0f);
        half3 d = half3(1.0h, 2.0h, 3.0h);
        
        // 1. 应该检测：精度转换
        float result1 = a + b;          // a: half → float (精度提升)
        float3 result2 = d + c;         // d: half3 → float3 (精度提升)
        
        // 2. 不应该检测：维度广播（硬件原生支持）
        float3 result3 = c * b;         // b 不需要转换为 float3
        half3 result4 = d * a;          // a 不需要转换为 half3
        
        // 3. 应该检测：强制转换
        float result5 = float(a);       // a: half → float (强转)
        half3 result6 = half3(c);       // c: float3 → half3 (强转)
        
        return float4(result1, result2.x, result3.x, result4.x);
    }
    '''
    
    print("=== 精度转换检测测试 ===")
    print("目标：只检测精度转换，不检测维度广播")
    print("✅ 应该检测：half → float, half3 → float3")
    print("❌ 不应该检测：float * float3 中的维度广播")
    print("\n" + "="*60)
    
    # 分析代码
    result = analyzer.analyze_shader_with_syntax_trees(test_code)
    
    # 统计转换
    precision_conversions = []
    dimension_broadcasts = []
    explicit_casts = []
    
    print("=== 检测到的类型转换 ===")
    for line_analysis in result['line_analyses']:
        if hasattr(line_analysis, 'type_conversions') and line_analysis.type_conversions:
            line_content = line_analysis.line_content.strip()
            print(f"\n第 {line_analysis.line_number} 行: {line_content}")
            
            for conversion in line_analysis.type_conversions:
                variable = conversion.get('variable', 'unknown')
                from_type = conversion.get('from_type', 'unknown')
                to_type = conversion.get('to_type', 'unknown')
                conv_type = conversion.get('conversion_type', 'unknown')
                reason = conversion.get('reason', 'unknown')
                
                if conv_type == 'explicit_cast':
                    explicit_casts.append(conversion)
                    print(f"  🔧 强转: {variable} ({from_type} → {to_type})")
                elif conv_type == 'implicit_promotion':
                    # 判断是精度转换还是维度广播
                    if _is_precision_change(from_type, to_type):
                        precision_conversions.append(conversion)
                        print(f"  ⬆️  精度提升: {variable} ({from_type} → {to_type})")
                    else:
                        dimension_broadcasts.append(conversion)
                        print(f"  📐 维度广播: {variable} ({from_type} → {to_type}) [不应该检测]")
                
                print(f"     原因: {reason}")
    
    # 验证结果
    print(f"\n=== 验证结果 ===")
    
    expected_precision = [
        "half → float",
        "half3 → float3"
    ]
    
    unexpected_broadcasts = [
        "float → float3",
        "half → half3"
    ]
    
    print(f"精度转换: {len(precision_conversions)} 个")
    print(f"维度广播: {len(dimension_broadcasts)} 个 (应该为0)")
    print(f"强制转换: {len(explicit_casts)} 个")
    
    # 检查是否正确排除了维度广播
    success = True
    if dimension_broadcasts:
        print(f"\n❌ 错误检测到维度广播:")
        for broadcast in dimension_broadcasts:
            var = broadcast.get('variable', 'unknown')
            from_t = broadcast.get('from_type', 'unknown')
            to_t = broadcast.get('to_type', 'unknown')
            print(f"   {var}: {from_t} → {to_t}")
        success = False
    else:
        print(f"\n✅ 正确排除了维度广播")
    
    # 检查是否检测到了预期的精度转换
    if len(precision_conversions) >= 2:
        print(f"✅ 检测到预期的精度转换")
    else:
        print(f"❌ 未检测到足够的精度转换")
        success = False
    
    return success

def _is_precision_change(from_type_str: str, to_type_str: str) -> bool:
    """辅助函数：判断是否是精度变化"""
    precision_map = {
        'half': 'half', 'half2': 'half', 'half3': 'half', 'half4': 'half',
        'float': 'float', 'float2': 'float', 'float3': 'float', 'float4': 'float',
        'int': 'int', 'int2': 'int', 'int3': 'int', 'int4': 'int',
    }
    
    from_precision = precision_map.get(from_type_str, from_type_str)
    to_precision = precision_map.get(to_type_str, to_type_str)
    
    return from_precision != to_precision

if __name__ == "__main__":
    success = test_precision_conversion()
    if success:
        print("\n🎉 精度转换检测功能正确！")
        print("💡 现在只检测真正的精度转换，不检测维度广播")
        print("🎯 符合着色器硬件的实际运行机制")
    else:
        print("\n❌ 精度转换检测需要进一步调整")
