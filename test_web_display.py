#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试网页显示类型转换信息
"""

from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
import tempfile
import os

def test_web_display():
    """测试网页显示类型转换信息"""
    
    # 测试代码
    test_code = '''
float4 main() {
    half _9176 = 1.0h;
    half _9199 = 2.0h;
    float _5678 = 3.0f;
    
    // 构造函数中的类型转换
    half3 tmp_0 = half3(_9176);           // _9176: half → half3 (scalar_broadcast)
    float3 tmp_1 = float3(_9176, _9199, _5678);  // 多个参数转换
    float4 tmp_2 = float4(_9176);         // _9176: half → float (precision_promotion)
    
    // 运算中的转换
    float result = _9176 + _5678;         // _9176: half → float (precision_promotion)
    
    return tmp_2;
}
'''
    
    print("=== 测试网页显示类型转换信息 ===")
    
    # 创建分析处理器
    processor = ShaderAnalysisProcessor()
    
    # 分析代码
    result = processor.analyze_shader(test_code)
    
    # 生成HTML报告
    with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
        html_content = processor._generate_html_report(result, test_code)
        f.write(html_content)
        html_file = f.name
    
    print(f"HTML报告已生成: {html_file}")
    
    # 检查HTML内容是否包含变量名信息
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 检查关键信息是否存在
    checks = [
        ('变量 _9176:', '变量名显示'),
        ('half → half3', '类型转换方向'),
        ('scalar_broadcast', '转换类型'),
        ('precision_promotion', '精度提升'),
        ('conversion-item', 'CSS类名'),
        ('var-name', '变量名CSS类'),
    ]
    
    print("\n=== HTML内容检查 ===")
    all_passed = True
    for check_text, description in checks:
        if check_text in html_content:
            print(f"✅ {description}: 找到 '{check_text}'")
        else:
            print(f"❌ {description}: 未找到 '{check_text}'")
            all_passed = False
    
    # 统计转换信息
    conversion_count = html_content.count('conversion-item')
    variable_count = html_content.count('变量 _')
    
    print(f"\n=== 统计信息 ===")
    print(f"转换项目数量: {conversion_count}")
    print(f"变量引用数量: {variable_count}")
    
    # 打开浏览器查看（可选）
    try:
        import webbrowser
        file_url = f'file:///{os.path.abspath(html_file).replace(os.sep, "/")}'
        print(f"\n🌐 在浏览器中打开: {file_url}")
        webbrowser.open(file_url)
    except Exception as e:
        print(f"无法打开浏览器: {e}")
    
    # 清理临时文件（可选，为了调试保留文件）
    # os.unlink(html_file)
    
    return all_passed and conversion_count > 0

if __name__ == "__main__":
    success = test_web_display()
    if success:
        print("\n🎉 网页显示类型转换信息功能正常！")
        print("💡 现在可以在网页中看到具体的变量名和转换信息")
    else:
        print("\n❌ 网页显示需要进一步修复")
